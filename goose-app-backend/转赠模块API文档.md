# 🎁 小鹅养成APP - 转赠模块API文档

**版本**: v1.0.0  
**更新时间**: 2024-12-19  
**基础URL**: `/api/v1/gifts`  
**认证方式**: Sa-Token (Header: `satoken: Bearer {token}`)  

---

## 📋 API概览

转赠模块提供11个API接口，支持完整的小鹅转赠功能，包括创建、接收、取消、查询和统计等操作。

### 🔗 接口列表
| 序号 | 接口 | 方法 | 认证 | 描述 |
|------|------|------|------|------|
| 1 | `/api/v1/gifts` | POST | ✅ | 创建转赠 |
| 2 | `/api/v1/gifts/{giftCode}` | GET | ❌ | 获取转赠详情 |
| 3 | `/api/v1/gifts/{giftCode}/receive` | POST | ✅ | 接收转赠 |
| 4 | `/api/v1/gifts/{giftCode}` | DELETE | ✅ | 取消转赠 |
| 5 | `/api/v1/gifts/my/sent` | GET | ✅ | 我发送的转赠 |
| 6 | `/api/v1/gifts/my/received` | GET | ✅ | 我接收的转赠 |
| 7 | `/api/v1/gifts/statistics` | GET | ✅ | 转赠统计 |
| 8 | `/api/v1/gifts/check/{gooseId}` | GET | ✅ | 检查转赠权限 |
| 9 | `/api/v1/gifts/quota/remaining` | GET | ✅ | 剩余转赠次数 |
| 10 | `/api/v1/gifts/validate/{giftCode}` | GET | ❌ | 验证转赠码 |
| 11 | `/api/v1/gifts/{giftCode}/share-url` | GET | ❌ | 获取分享链接 |

---

## 🔧 详细接口文档

### 1. 创建转赠

**接口**: `POST /api/v1/gifts`  
**认证**: 必需  
**描述**: 用户创建小鹅转赠

#### 请求参数
```json
{
  "gooseId": 1,
  "message": "希望这只小鹅能给你带来快乐！",
  "expiresHours": 72
}
```

| 参数 | 类型 | 必需 | 描述 | 限制 |
|------|------|------|------|------|
| gooseId | Long | ✅ | 小鹅ID | 必须为正数 |
| message | String | ❌ | 转赠留言 | 最大500字符 |
| expiresHours | Integer | ✅ | 过期时间(小时) | 1-168小时 |

#### 响应示例
```json
{
  "code": 200,
  "message": "转赠创建成功",
  "data": {
    "id": 1,
    "giftCode": "GIFT20241219143025A1B2",
    "senderId": 1,
    "sender": {
      "id": 1,
      "username": "testuser",
      "nickname": "测试用户",
      "avatar": "https://example.com/avatar.jpg"
    },
    "gooseId": 1,
    "goose": {
      "id": 1,
      "name": "小白",
      "breed": "白鹅",
      "level": 3,
      "imageUrl": "https://example.com/goose.jpg"
    },
    "message": "希望这只小鹅能给你带来快乐！",
    "status": 0,
    "statusDescription": "待接收",
    "expiresAt": "2024-12-22T14:30:25",
    "shareUrl": "http://localhost:8080/gift/GIFT20241219143025A1B2",
    "remainingHours": 72,
    "canBeReceived": true,
    "canBeCancelled": true,
    "createdAt": "2024-12-19T14:30:25"
  }
}
```

### 2. 获取转赠详情

**接口**: `GET /api/v1/gifts/{giftCode}`  
**认证**: 不需要  
**描述**: 根据转赠码获取转赠详情（公开访问）

#### 路径参数
| 参数 | 类型 | 描述 |
|------|------|------|
| giftCode | String | 转赠码 |

#### 响应示例
```json
{
  "code": 200,
  "message": "查询成功",
  "data": {
    "id": 1,
    "giftCode": "GIFT20241219143025A1B2",
    "senderId": 1,
    "sender": {
      "id": 1,
      "nickname": "测试用户",
      "avatar": "https://example.com/avatar.jpg"
    },
    "goose": {
      "id": 1,
      "name": "小白",
      "breed": "白鹅",
      "level": 3,
      "health": 100,
      "imageUrl": "https://example.com/goose.jpg"
    },
    "message": "希望这只小鹅能给你带来快乐！",
    "status": 0,
    "statusDescription": "待接收",
    "expiresAt": "2024-12-22T14:30:25",
    "remainingHours": 72,
    "canBeReceived": true,
    "isExpired": false
  }
}
```

### 3. 接收转赠

**接口**: `POST /api/v1/gifts/{giftCode}/receive`  
**认证**: 必需  
**描述**: 用户接收转赠的小鹅

#### 路径参数
| 参数 | 类型 | 描述 |
|------|------|------|
| giftCode | String | 转赠码 |

#### 请求参数
```json
{
  "confirmed": true,
  "note": "谢谢你的小鹅！"
}
```

#### 响应示例
```json
{
  "code": 200,
  "message": "转赠接收成功",
  "data": {
    "id": 1,
    "giftCode": "GIFT20241219143025A1B2",
    "senderId": 1,
    "receiverId": 2,
    "receiver": {
      "id": 2,
      "nickname": "接收者",
      "avatar": "https://example.com/avatar2.jpg"
    },
    "status": 1,
    "statusDescription": "已接收",
    "receivedAt": "2024-12-19T15:30:25"
  }
}
```

### 4. 取消转赠

**接口**: `DELETE /api/v1/gifts/{giftCode}`  
**认证**: 必需  
**描述**: 转赠创建者取消转赠

#### 响应示例
```json
{
  "code": 200,
  "message": "转赠取消成功",
  "data": true
}
```

### 5. 我发送的转赠

**接口**: `GET /api/v1/gifts/my/sent`  
**认证**: 必需  
**描述**: 获取当前用户发送的转赠列表

#### 查询参数
| 参数 | 类型 | 必需 | 描述 | 默认值 |
|------|------|------|------|--------|
| status | Integer | ❌ | 状态筛选 | 全部 |
| page | Integer | ❌ | 页码 | 0 |
| size | Integer | ❌ | 每页大小 | 10 |
| sort | String | ❌ | 排序字段 | createdAt |
| direction | String | ❌ | 排序方向 | desc |

#### 响应示例
```json
{
  "code": 200,
  "message": "查询成功",
  "data": {
    "content": [
      {
        "id": 1,
        "giftCode": "GIFT20241219143025A1B2",
        "gooseName": "小白",
        "gooseBreed": "白鹅",
        "gooseLevel": 3,
        "otherUserNickname": "接收者",
        "message": "希望这只小鹅能给你带来快乐！",
        "status": 1,
        "statusDescription": "已接收",
        "operationType": "none",
        "createdAt": "2024-12-19T14:30:25"
      }
    ],
    "totalElements": 1,
    "totalPages": 1,
    "size": 10,
    "number": 0
  }
}
```

### 6. 转赠统计

**接口**: `GET /api/v1/gifts/statistics`  
**认证**: 必需  
**描述**: 获取当前用户的转赠统计信息

#### 响应示例
```json
{
  "code": 200,
  "message": "查询成功",
  "data": {
    "totalSent": 10,
    "totalReceived": 5,
    "pendingReceived": 2,
    "completedSent": 8,
    "expiredSent": 1,
    "todaySent": 2,
    "todayRemaining": 3,
    "dailyLimit": 5,
    "weekSent": 5,
    "monthSent": 10,
    "successRate": 80.0,
    "activityScore": 85
  }
}
```

---

## 📊 状态码说明

### 转赠状态
| 状态码 | 描述 | 说明 |
|--------|------|------|
| 0 | 待接收 | 转赠已创建，等待接收 |
| 1 | 已接收 | 转赠已被接收 |
| 2 | 已完成 | 转赠流程完成 |
| 3 | 已过期 | 转赠已过期 |
| 4 | 已取消 | 转赠已被取消 |

### HTTP状态码
| 状态码 | 描述 | 场景 |
|--------|------|------|
| 200 | 成功 | 请求成功处理 |
| 400 | 请求错误 | 参数验证失败 |
| 401 | 未授权 | 需要登录 |
| 403 | 禁止访问 | 权限不足 |
| 404 | 资源不存在 | 转赠码不存在 |
| 500 | 服务器错误 | 系统内部错误 |

---

## 🔒 安全说明

### 认证机制
- 使用Sa-Token进行用户认证
- Token格式: `satoken: Bearer {token}`
- Token有效期: 7天（可配置）

### 权限控制
- 只能转赠自己拥有的小鹅
- 只能取消自己创建的转赠
- 不能接收自己发送的转赠
- 每日转赠次数限制

### 数据验证
- 所有输入参数进行严格验证
- SQL注入防护
- XSS攻击防护
- 参数长度和格式限制

---

## 📝 使用示例

### JavaScript示例
```javascript
// 创建转赠
const createGift = async (gooseId, message, expiresHours) => {
  const response = await fetch('/api/v1/gifts', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'satoken': `Bearer ${token}`
    },
    body: JSON.stringify({
      gooseId,
      message,
      expiresHours
    })
  });
  return response.json();
};

// 接收转赠
const receiveGift = async (giftCode) => {
  const response = await fetch(`/api/v1/gifts/${giftCode}/receive`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'satoken': `Bearer ${token}`
    },
    body: JSON.stringify({
      confirmed: true
    })
  });
  return response.json();
};
```

---

**文档版本**: v1.0  
**维护者**: 全栈开发工程师  
**更新频率**: 随版本发布更新
