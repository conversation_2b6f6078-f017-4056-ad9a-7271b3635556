# 🦆 小鹅养成APP - 后端服务

## 📋 项目简介

小鹅养成APP后端服务，基于Spring Boot 3.2.1开发，提供用户认证、小鹅管理、商城功能、转赠功能等核心API接口。

## 🛠 技术栈

- **框架**: Spring Boot 3.2.1
- **数据库**: MySQL 8.0+
- **认证**: Sa-Token 1.37.0
- **缓存**: Redis
- **构建工具**: Maven 3.9+
- **JDK版本**: 17
- **API文档**: SpringDoc OpenAPI 3

## 🚀 快速开始

### 环境要求

- JDK 17+
- Maven 3.9+
- MySQL 8.0+
- Redis 6.0+

### 1. 克隆项目

```bash
git clone <repository-url>
cd goose-app-backend
```

### 2. 数据库初始化

```bash
# 连接MySQL
mysql -u root -p

# 执行初始化脚本
source scripts/init-db.sql
```

### 3. 配置文件

复制并修改配置文件：

```bash
cp src/main/resources/application-dev.yml.example src/main/resources/application-dev.yml
```

修改数据库连接信息：

```yaml
spring:
  datasource:
    url: *************************************
    username: goose_user
    password: goose_password
```

### 4. 启动应用

```bash
# 开发环境启动
mvn spring-boot:run -Dspring-boot.run.profiles=dev

# 或者编译后启动
mvn clean package
java -jar target/goose-app-backend-1.0.0.jar --spring.profiles.active=dev
```

### 5. 验证启动

访问以下地址验证服务启动成功：

- **应用首页**: http://localhost:8080
- **API文档**: http://localhost:8080/swagger-ui.html
- **健康检查**: http://localhost:8080/actuator/health

## 📚 API文档

启动应用后，访问 [http://localhost:8080/swagger-ui.html](http://localhost:8080/swagger-ui.html) 查看完整的API文档。

### 主要接口模块

#### 认证模块 (`/api/v1/auth`)
- `POST /register` - 用户注册
- `POST /login` - 用户登录
- `POST /logout` - 用户登出
- `POST /refresh` - 刷新Token
- `GET /me` - 获取当前用户信息

#### 用户模块 (`/api/v1/users`)
- `GET /profile` - 获取用户信息
- `PUT /profile` - 更新用户信息
- `GET /stats` - 获取用户统计

### 请求示例

#### 用户注册
```bash
curl -X POST http://localhost:8080/api/v1/auth/register \
  -H "Content-Type: application/json" \
  -d '{
    "username": "testuser",
    "email": "<EMAIL>",
    "password": "password123",
    "confirmPassword": "password123",
    "nickname": "测试用户"
  }'
```

#### 用户登录
```bash
curl -X POST http://localhost:8080/api/v1/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "username": "testuser",
    "password": "password123"
  }'
```

## 🧪 测试

### 运行单元测试

```bash
# 运行所有测试
mvn test

# 运行特定测试类
mvn test -Dtest=UserServiceTest

# 运行测试并生成覆盖率报告
mvn test jacoco:report
```

### 测试覆盖率

测试覆盖率报告生成在 `target/site/jacoco/index.html`

### 集成测试

```bash
# 运行集成测试
mvn verify -P integration-test
```

## 📁 项目结构

```
src/
├── main/
│   ├── java/
│   │   └── com/gooseapp/
│   │       ├── GooseAppApplication.java          # 主启动类
│   │       ├── common/                           # 公共模块
│   │       │   ├── config/                       # 配置类
│   │       │   ├── constant/                     # 常量定义
│   │       │   ├── exception/                    # 异常处理
│   │       │   ├── response/                     # 响应封装
│   │       │   └── utils/                        # 工具类
│   │       └── user/                             # 用户模块
│   │           ├── controller/                   # 控制器
│   │           ├── service/                      # 业务逻辑
│   │           ├── repository/                   # 数据访问
│   │           ├── entity/                       # 实体类
│   │           └── dto/                          # 数据传输对象
│   └── resources/
│       ├── application.yml                       # 主配置文件
│       ├── application-dev.yml                   # 开发环境配置
│       └── application-prod.yml                  # 生产环境配置
└── test/
    ├── java/                                     # 测试代码
    └── resources/
        └── application-test.yml                  # 测试环境配置
```

## 🔧 配置说明

### 数据库配置

```yaml
spring:
  datasource:
    url: *************************************
    username: goose_user
    password: goose_password
    hikari:
      maximum-pool-size: 20
      minimum-idle: 5
```

### Redis配置

```yaml
spring:
  data:
    redis:
      host: localhost
      port: 6379
      database: 0
      timeout: 5000ms
```

### Sa-Token配置

```yaml
sa-token:
  token-name: satoken
  timeout: 2592000        # 30天
  is-concurrent: true
  is-share: true
  token-style: uuid
  is-read-header: true
  token-prefix: Bearer
```

## 🐛 故障排除

### 常见问题

1. **数据库连接失败**
   - 检查MySQL服务是否启动
   - 验证数据库连接信息
   - 确认数据库和用户已创建

2. **Redis连接失败**
   - 检查Redis服务是否启动
   - 验证Redis连接配置

3. **端口占用**
   - 修改 `server.port` 配置
   - 或者停止占用8080端口的进程

### 日志查看

```bash
# 查看应用日志
tail -f logs/goose-app.log

# 查看错误日志
grep ERROR logs/goose-app.log
```

## 📈 性能监控

### 应用监控

访问 http://localhost:8080/actuator 查看应用监控信息：

- `/actuator/health` - 健康检查
- `/actuator/metrics` - 应用指标
- `/actuator/info` - 应用信息

### 数据库监控

```sql
-- 查看连接数
SHOW STATUS LIKE 'Threads_connected';

-- 查看慢查询
SHOW VARIABLES LIKE 'slow_query_log';
```

## 🚀 部署

### Docker部署

```bash
# 构建镜像
docker build -t goose-app-backend .

# 运行容器
docker run -d -p 8080:8080 \
  -e SPRING_PROFILES_ACTIVE=prod \
  -e MYSQL_HOST=mysql \
  -e REDIS_HOST=redis \
  goose-app-backend
```

### Docker Compose部署

```bash
# 启动所有服务
docker-compose up -d

# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f app
```

## 🤝 贡献指南

1. Fork 项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 📞 联系方式

- 项目维护者：全栈开发工程师
- 邮箱：<EMAIL>
- 项目地址：https://github.com/goose-app/backend

---

**注意**: 这是一个开发中的项目，API可能会发生变化。请关注项目更新。
