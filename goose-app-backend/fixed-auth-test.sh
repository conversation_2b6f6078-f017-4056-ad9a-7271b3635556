#!/bin/bash

# ======================================================
# 小鹅养成APP - 认证模块修复版API测试脚本
# Java后端开发专家 - 修复Sa-Token认证头问题
# ======================================================

# 颜色配置
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 服务器配置
BASE_URL="http://localhost:8080"
API_PREFIX="/api/v1/auth"

# 测试计数器
TOTAL_TESTS=0
PASSED_TESTS=0
FAILED_TESTS=0

# 测试数据
TEST_USER_USERNAME="testuser_$(date +%s)"
TEST_USER_EMAIL="test_$(date +%s)@example.com"
TEST_USER_PASSWORD="password123"
TEST_USER_NICKNAME="测试用户"
ACCESS_TOKEN=""

# 打印函数
print_title() {
    echo -e "\n${BLUE}============================================${NC}"
    echo -e "${BLUE} $1 ${NC}"
    echo -e "${BLUE}============================================${NC}\n"
}

print_test() {
    echo -e "${YELLOW}🧪 测试: $1${NC}"
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
}

print_success() {
    echo -e "${GREEN}✅ 通过: $1${NC}"
    PASSED_TESTS=$((PASSED_TESTS + 1))
}

print_error() {
    echo -e "${RED}❌ 失败: $1${NC}"
    FAILED_TESTS=$((FAILED_TESTS + 1))
}

# 修复版测试函数 - 使用正确的Sa-Token头格式
test_get() {
    local url="$1"
    local description="$2"
    local auth_token="$3"
    
    print_test "$description"
    echo "📡 GET $BASE_URL$url"
    
    if [ -n "$auth_token" ]; then
        # 使用Sa-Token配置的正确header格式
        response=$(curl -s -w "\nHTTP_CODE:%{http_code}" \
            -H "satoken: Bearer $auth_token" \
            "$BASE_URL$url")
    else
        response=$(curl -s -w "\nHTTP_CODE:%{http_code}" "$BASE_URL$url")
    fi
    
    http_code=$(echo "$response" | grep "HTTP_CODE:" | cut -d: -f2)
    body=$(echo "$response" | grep -v "HTTP_CODE:")
    
    echo "📋 响应: $body"
    echo "📊 状态码: $http_code"
    echo
    
    echo "$body" > /tmp/last_response.json
    echo "$http_code" > /tmp/last_http_code
}

test_post() {
    local url="$1"
    local data="$2"
    local description="$3"
    local auth_token="$4"
    
    print_test "$description"
    echo "📡 POST $BASE_URL$url"
    if [ -n "$data" ]; then
        echo "📝 数据: $data"
    fi
    
    if [ -n "$auth_token" ]; then
        # 使用Sa-Token配置的正确header格式
        response=$(curl -s -w "\nHTTP_CODE:%{http_code}" \
            -X POST \
            -H "Content-Type: application/json" \
            -H "satoken: Bearer $auth_token" \
            -d "$data" \
            "$BASE_URL$url")
    else
        response=$(curl -s -w "\nHTTP_CODE:%{http_code}" \
            -X POST \
            -H "Content-Type: application/json" \
            -d "$data" \
            "$BASE_URL$url")
    fi
    
    http_code=$(echo "$response" | grep "HTTP_CODE:" | cut -d: -f2)
    body=$(echo "$response" | grep -v "HTTP_CODE:")
    
    echo "📋 响应: $body"
    echo "📊 状态码: $http_code"
    echo
    
    echo "$body" > /tmp/last_response.json
    echo "$http_code" > /tmp/last_http_code
}

check_status() {
    local expected="$1"
    local description="$2"
    
    local actual=$(cat /tmp/last_http_code)
    
    if [ "$actual" = "$expected" ]; then
        print_success "$description - 状态码正确 ($actual)"
        return 0
    else
        print_error "$description - 状态码错误，期望: $expected，实际: $actual"
        return 1
    fi
}

check_json_field() {
    local field="$1"
    local expected="$2"
    local description="$3"
    
    local actual=$(cat /tmp/last_response.json | jq -r "$field" 2>/dev/null)
    
    if [ "$actual" = "$expected" ]; then
        print_success "$description - 字段验证通过: $field = $expected"
        return 0
    else
        print_error "$description - 字段验证失败，期望: $expected，实际: $actual"
        return 1
    fi
}

# ======================================================
# 开始测试 - 修复版
# ======================================================

print_title "小鹅养成APP认证模块API测试 - 修复版"

# 快速重新注册一个用户用于测试
print_title "准备测试环境"

# 1. 用户注册
register_data='{"username":"'$TEST_USER_USERNAME'","email":"'$TEST_USER_EMAIL'","password":"'$TEST_USER_PASSWORD'","confirmPassword":"'$TEST_USER_PASSWORD'","nickname":"'$TEST_USER_NICKNAME'"}'

test_post "$API_PREFIX/register" "$register_data" "测试用户注册"
if check_status "200" "用户注册"; then
    # 提取token
    ACCESS_TOKEN=$(cat /tmp/last_response.json | jq -r '.data.token' 2>/dev/null)
    if [ "$ACCESS_TOKEN" != "null" ] && [ -n "$ACCESS_TOKEN" ]; then
        print_success "成功获取访问令牌: ${ACCESS_TOKEN:0:20}..."
    else
        print_error "未能获取访问令牌"
        exit 1
    fi
fi

# ======================================================
# 测试需要认证的接口
# ======================================================

print_title "测试认证相关接口"

# 7. 获取当前用户信息 - 修复版
print_test "未认证获取用户信息（应该失败）"
test_get "$API_PREFIX/me" "未认证获取用户信息"
if [ "$(cat /tmp/last_http_code)" = "401" ]; then
    print_success "未认证获取用户信息正确返回401"
else
    actual_code=$(cat /tmp/last_http_code)
    print_error "未认证获取用户信息应该返回401，实际: $actual_code"
fi

print_test "已认证获取用户信息"
test_get "$API_PREFIX/me" "已认证获取用户信息" "$ACCESS_TOKEN"
if check_status "200" "获取用户信息"; then
    check_json_field ".code" "200" "用户信息响应码"
    check_json_field ".data.username" "$TEST_USER_USERNAME" "用户名验证"
fi

# 8. 刷新Token - 修复版
print_test "未认证刷新Token（应该失败）"
test_post "$API_PREFIX/refresh" "" "未认证刷新Token"
if [ "$(cat /tmp/last_http_code)" = "401" ]; then
    print_success "未认证刷新Token正确返回401"
else
    actual_code=$(cat /tmp/last_http_code)
    print_error "未认证刷新Token应该返回401，实际: $actual_code"
fi

print_test "已认证刷新Token"
test_post "$API_PREFIX/refresh" "" "已认证刷新Token" "$ACCESS_TOKEN"
if check_status "200" "Token刷新"; then
    check_json_field ".code" "200" "Token刷新响应码"
    check_json_field ".message" "Token刷新成功" "Token刷新消息"
fi

# 9. 修改密码 - 修复版
print_test "未认证修改密码（应该失败）"
change_password_data='{"oldPassword":"'$TEST_USER_PASSWORD'","newPassword":"new_password123"}'
test_post "$API_PREFIX/change-password" "$change_password_data" "未认证修改密码"
if [ "$(cat /tmp/last_http_code)" = "401" ]; then
    print_success "未认证修改密码正确返回401"
else
    actual_code=$(cat /tmp/last_http_code)
    print_error "未认证修改密码应该返回401，实际: $actual_code"
fi

print_test "已认证修改密码"
test_post "$API_PREFIX/change-password" "$change_password_data" "已认证修改密码" "$ACCESS_TOKEN"
if check_status "200" "密码修改"; then
    check_json_field ".code" "200" "密码修改响应码"
    check_json_field ".message" "密码修改成功" "密码修改消息"
    TEST_USER_PASSWORD="new_password123"
    print_success "密码已更新为: $TEST_USER_PASSWORD"
fi

# 10. 验证新密码登录
print_test "新密码登录验证"
new_login_data='{"username":"'$TEST_USER_USERNAME'","password":"new_password123"}'
test_post "$API_PREFIX/login" "$new_login_data" "新密码登录"
if check_status "200" "新密码登录"; then
    check_json_field ".code" "200" "新密码登录响应码"
    # 更新token
    ACCESS_TOKEN=$(cat /tmp/last_response.json | jq -r '.data.token' 2>/dev/null)
    print_success "新密码登录成功，token已更新"
fi

# 11. 用户登出 - 修复版
print_test "未认证用户登出（应该失败）"
test_post "$API_PREFIX/logout" "" "未认证用户登出"
if [ "$(cat /tmp/last_http_code)" = "401" ]; then
    print_success "未认证登出正确返回401"
else
    actual_code=$(cat /tmp/last_http_code)
    print_error "未认证登出应该返回401，实际: $actual_code"
fi

print_test "已认证用户登出"
test_post "$API_PREFIX/logout" "" "已认证用户登出" "$ACCESS_TOKEN"
if check_status "200" "用户登出"; then
    check_json_field ".code" "200" "登出响应码"
    check_json_field ".message" "登出成功" "登出消息"
fi

# 12. 验证登出后状态
print_test "登出后验证token失效"
test_get "$API_PREFIX/me" "登出后获取用户信息" "$ACCESS_TOKEN"
if [ "$(cat /tmp/last_http_code)" = "401" ]; then
    print_success "登出后Token失效，正确返回401"
else
    actual_code=$(cat /tmp/last_http_code)
    print_error "登出后Token应该失效，实际状态码: $actual_code"
fi

# ======================================================
# 额外的边界测试
# ======================================================

print_title "边界条件和安全测试"

print_test "测试无效Token"
test_get "$API_PREFIX/me" "无效Token测试" "invalid_token_12345"
if [ "$(cat /tmp/last_http_code)" = "401" ]; then
    print_success "无效Token正确返回401"
else
    actual_code=$(cat /tmp/last_http_code)
    print_error "无效Token应该返回401，实际: $actual_code"
fi

print_test "测试空Token"
test_get "$API_PREFIX/me" "空Token测试" ""
if [ "$(cat /tmp/last_http_code)" = "401" ]; then
    print_success "空Token正确返回401"
else
    actual_code=$(cat /tmp/last_http_code)
    print_error "空Token应该返回401，实际: $actual_code"
fi

# ======================================================
# 测试结果统计
# ======================================================
print_title "测试结果统计"

echo -e "${BLUE}📊 修复版测试统计报告:${NC}"
echo -e "   总测试用例: ${TOTAL_TESTS}"
echo -e "   ${GREEN}✅ 通过: ${PASSED_TESTS}${NC}"
echo -e "   ${RED}❌ 失败: ${FAILED_TESTS}${NC}"

if [ $TOTAL_TESTS -gt 0 ]; then
    PASS_RATE=$((PASSED_TESTS * 100 / TOTAL_TESTS))
    echo -e "   通过率: ${PASS_RATE}%"
fi

echo -e "\n${BLUE}🔧 修复说明:${NC}"
echo -e "   ✓ 修复了Sa-Token认证头格式问题"
echo -e "   ✓ 使用正确的header: 'satoken: Bearer <token>'"
echo -e "   ✓ 替代了错误的: 'Authorization: Bearer <token>'"

echo -e "\n${BLUE}📋 认证接口测试覆盖:${NC}"
echo -e "   ✓ 用户信息获取（认证/未认证）"
echo -e "   ✓ Token刷新（认证/未认证）"
echo -e "   ✓ 密码修改（认证/未认证）"
echo -e "   ✓ 用户登出（认证/未认证）"
echo -e "   ✓ Token失效验证"
echo -e "   ✓ 无效Token测试"
echo -e "   ✓ 边界条件测试"

if [ $FAILED_TESTS -eq 0 ]; then
    echo -e "\n${GREEN}🎉 所有测试用例都通过了！认证模块API工作完全正常。${NC}"
    echo -e "${GREEN}✨ Sa-Token认证问题已修复，接口测试覆盖完整！${NC}"
    exit 0
else
    echo -e "\n${YELLOW}⚠️  还有 $FAILED_TESTS 个测试用例失败。${NC}"
    echo -e "${YELLOW}📝 建议进一步检查失败的接口实现。${NC}"
    exit 1
fi

# 清理临时文件
rm -f /tmp/last_response.json /tmp/last_http_code 