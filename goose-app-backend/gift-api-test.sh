#!/bin/bash

# 🎁 小鹅养成APP - 转赠模块API测试脚本
# 使用curl测试转赠模块的所有API接口

echo "🎁 小鹅养成APP - 转赠模块API测试"
echo "=================================="

BASE_URL="http://localhost:8080"
TOTAL_TESTS=0
PASSED_TESTS=0

# 颜色定义
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m'

# 测试函数
test_api() {
    local name=$1
    local method=$2
    local endpoint=$3
    local data=$4
    local token=$5
    local expected_status=$6
    
    ((TOTAL_TESTS++))
    echo -n "测试 $name ... "
    
    local curl_cmd="curl -s -w '%{http_code}' -X $method"
    
    if [ ! -z "$token" ]; then
        curl_cmd="$curl_cmd -H 'satoken: Bearer $token'"
    fi
    
    if [ "$method" = "POST" ] || [ "$method" = "PUT" ]; then
        curl_cmd="$curl_cmd -H 'Content-Type: application/json'"
    fi
    
    if [ ! -z "$data" ]; then
        curl_cmd="$curl_cmd -d '$data'"
    fi
    
    curl_cmd="$curl_cmd '${BASE_URL}${endpoint}'"
    
    local response=$(eval $curl_cmd)
    local status_code="${response: -3}"
    local body="${response%???}"
    
    if [ "$status_code" = "$expected_status" ]; then
        echo -e "${GREEN}✅ 通过${NC} ($status_code)"
        ((PASSED_TESTS++))
        
        # 提取关键信息
        if [[ "$endpoint" == *"/gifts"* ]] && [ "$method" = "POST" ] && [ "$status_code" = "200" ]; then
            if command -v jq >/dev/null 2>&1; then
                GIFT_CODE=$(echo "$body" | jq -r '.data.giftCode // empty')
                if [ ! -z "$GIFT_CODE" ]; then
                    echo "    转赠码: $GIFT_CODE"
                fi
            fi
        fi
        
        # 显示部分响应内容
        if [ ${#body} -lt 150 ] && [ ! -z "$body" ]; then
            echo "    响应: ${body:0:100}..."
        fi
    else
        echo -e "${RED}❌ 失败${NC} (期望: $expected_status, 实际: $status_code)"
        if [ ! -z "$body" ] && [ ${#body} -lt 200 ]; then
            echo "    响应: $body"
        fi
    fi
    
    # 返回响应体供后续使用
    echo "$body" > /tmp/last_response.json
}

# 检查应用状态
echo "检查应用状态..."
if curl -s "$BASE_URL/api/v1/shop/geese/breeds" >/dev/null; then
    echo -e "${GREEN}✅ 应用运行正常${NC}"
else
    echo -e "${RED}❌ 应用未运行或无法访问${NC}"
    echo "请先启动应用: mvn spring-boot:run"
    exit 1
fi

echo ""
echo "准备测试环境..."

# 1. 用户登录获取Token
echo -e "${BLUE}=== 准备测试用户 ===${NC}"

# 注册测试用户1 (发送者)
SENDER_REGISTER_DATA='{"username":"giftsender","email":"<EMAIL>","password":"password123","confirmPassword":"password123","nickname":"转赠发送者"}'
echo -n "注册发送者用户 ... "
SENDER_REGISTER_RESPONSE=$(curl -s -X POST -H "Content-Type: application/json" -d "$SENDER_REGISTER_DATA" "${BASE_URL}/api/v1/auth/register")
if [[ "$SENDER_REGISTER_RESPONSE" == *"注册成功"* ]] || [[ "$SENDER_REGISTER_RESPONSE" == *"用户名已存在"* ]]; then
    echo -e "${GREEN}✅ 成功${NC}"
else
    echo -e "${YELLOW}⚠️ 可能已存在${NC}"
fi

# 注册测试用户2 (接收者)
RECEIVER_REGISTER_DATA='{"username":"giftreceiver","email":"<EMAIL>","password":"password123","confirmPassword":"password123","nickname":"转赠接收者"}'
echo -n "注册接收者用户 ... "
RECEIVER_REGISTER_RESPONSE=$(curl -s -X POST -H "Content-Type: application/json" -d "$RECEIVER_REGISTER_DATA" "${BASE_URL}/api/v1/auth/register")
if [[ "$RECEIVER_REGISTER_RESPONSE" == *"注册成功"* ]] || [[ "$RECEIVER_REGISTER_RESPONSE" == *"用户名已存在"* ]]; then
    echo -e "${GREEN}✅ 成功${NC}"
else
    echo -e "${YELLOW}⚠️ 可能已存在${NC}"
fi

# 发送者登录
SENDER_LOGIN_DATA='{"username":"giftsender","password":"password123"}'
echo -n "发送者登录 ... "
SENDER_LOGIN_RESPONSE=$(curl -s -X POST -H "Content-Type: application/json" -d "$SENDER_LOGIN_DATA" "${BASE_URL}/api/v1/auth/login")
if [[ "$SENDER_LOGIN_RESPONSE" == *"登录成功"* ]]; then
    echo -e "${GREEN}✅ 成功${NC}"
    if command -v jq >/dev/null 2>&1; then
        SENDER_TOKEN=$(echo "$SENDER_LOGIN_RESPONSE" | jq -r '.data.token // empty')
        echo "    发送者Token: ${SENDER_TOKEN:0:20}..."
    fi
else
    echo -e "${RED}❌ 失败${NC}"
    echo "无法获取发送者Token，退出测试"
    exit 1
fi

# 接收者登录
RECEIVER_LOGIN_DATA='{"username":"giftreceiver","password":"password123"}'
echo -n "接收者登录 ... "
RECEIVER_LOGIN_RESPONSE=$(curl -s -X POST -H "Content-Type: application/json" -d "$RECEIVER_LOGIN_DATA" "${BASE_URL}/api/v1/auth/login")
if [[ "$RECEIVER_LOGIN_RESPONSE" == *"登录成功"* ]]; then
    echo -e "${GREEN}✅ 成功${NC}"
    if command -v jq >/dev/null 2>&1; then
        RECEIVER_TOKEN=$(echo "$RECEIVER_LOGIN_RESPONSE" | jq -r '.data.token // empty')
        echo "    接收者Token: ${RECEIVER_TOKEN:0:20}..."
    fi
else
    echo -e "${RED}❌ 失败${NC}"
    echo "无法获取接收者Token，退出测试"
    exit 1
fi

# 管理员登录
ADMIN_LOGIN_DATA='{"username":"superAdmin","password":"admin123"}'
echo -n "管理员登录 ... "
ADMIN_LOGIN_RESPONSE=$(curl -s -X POST -H "Content-Type: application/json" -d "$ADMIN_LOGIN_DATA" "${BASE_URL}/api/v1/auth/login")
if [[ "$ADMIN_LOGIN_RESPONSE" == *"登录成功"* ]]; then
    echo -e "${GREEN}✅ 成功${NC}"
    if command -v jq >/dev/null 2>&1; then
        ADMIN_TOKEN=$(echo "$ADMIN_LOGIN_RESPONSE" | jq -r '.data.token // empty')
        echo "    管理员Token: ${ADMIN_TOKEN:0:20}..."
    fi
else
    echo -e "${RED}❌ 失败${NC}"
    echo "无法获取管理员Token"
fi

echo ""

# 2. 准备测试数据 - 确保发送者有小鹅可以转赠
echo -e "${BLUE}=== 准备测试数据 ===${NC}"

# 获取发送者的小鹅列表
echo -n "获取发送者的小鹅 ... "
SENDER_GEESE_RESPONSE=$(curl -s -H "satoken: $SENDER_TOKEN" "${BASE_URL}/api/v1/geese/my")
if [[ "$SENDER_GEESE_RESPONSE" == *"查询成功"* ]]; then
    echo -e "${GREEN}✅ 成功${NC}"
    if command -v jq >/dev/null 2>&1; then
        GOOSE_ID=$(echo "$SENDER_GEESE_RESPONSE" | jq -r '.data.content[0].id // empty')
        if [ ! -z "$GOOSE_ID" ] && [ "$GOOSE_ID" != "null" ]; then
            echo "    找到小鹅ID: $GOOSE_ID"
        else
            echo "    发送者没有小鹅，尝试购买一只..."
            # 这里可以添加购买小鹅的逻辑
            GOOSE_ID="10"  # 使用默认ID进行测试
        fi
    else
        GOOSE_ID="10"  # 默认使用ID 10
    fi
else
    echo -e "${YELLOW}⚠️ 使用默认小鹅ID${NC}"
    GOOSE_ID="10"
fi

echo ""
echo "开始转赠模块API测试..."
echo ""

# 3. 转赠模块核心功能测试
echo -e "${PURPLE}=== 转赠模块核心功能测试 ===${NC}"

# 3.1 创建转赠
CREATE_GIFT_DATA="{\"gooseId\":$GOOSE_ID,\"message\":\"测试转赠消息\",\"expiresHours\":72}"
test_api "创建转赠" "POST" "/api/v1/gifts" "$CREATE_GIFT_DATA" "$SENDER_TOKEN" "200"

# 提取转赠码
if [ -f /tmp/last_response.json ] && command -v jq >/dev/null 2>&1; then
    GIFT_CODE=$(cat /tmp/last_response.json | jq -r '.data.giftCode // empty')
    if [ ! -z "$GIFT_CODE" ] && [ "$GIFT_CODE" != "null" ]; then
        echo "    ✅ 成功创建转赠，转赠码: $GIFT_CODE"
    else
        echo "    ⚠️ 无法提取转赠码，使用测试转赠码"
        GIFT_CODE="GIFT20241219143025TEST"
    fi
else
    GIFT_CODE="GIFT20241219143025TEST"
fi

# 3.2 获取转赠详情 (无需认证)
test_api "获取转赠详情" "GET" "/api/v1/gifts/$GIFT_CODE" "" "" "200"

# 3.3 验证转赠码格式 (无需认证)
test_api "验证转赠码格式" "GET" "/api/v1/gifts/validate/$GIFT_CODE" "" "" "200"
test_api "验证无效转赠码格式" "GET" "/api/v1/gifts/validate/INVALID_CODE" "" "" "200"

# 3.4 获取分享链接 (无需认证)
test_api "获取分享链接" "GET" "/api/v1/gifts/$GIFT_CODE/share-url" "" "" "200"

echo ""

# 4. 转赠权限和配额测试
echo -e "${PURPLE}=== 转赠权限和配额测试 ===${NC}"

# 4.1 检查转赠权限
test_api "检查转赠权限" "GET" "/api/v1/gifts/check/$GOOSE_ID" "" "$SENDER_TOKEN" "200"

# 4.2 获取剩余转赠次数
test_api "获取剩余转赠次数" "GET" "/api/v1/gifts/quota/remaining" "" "$SENDER_TOKEN" "200"

# 4.3 尝试转赠不存在的小鹅
INVALID_GIFT_DATA='{"gooseId":99999,"message":"测试无效转赠","expiresHours":72}'
test_api "转赠不存在的小鹅" "POST" "/api/v1/gifts" "$INVALID_GIFT_DATA" "$SENDER_TOKEN" "404"

# 4.4 尝试无认证创建转赠
test_api "无认证创建转赠" "POST" "/api/v1/gifts" "$CREATE_GIFT_DATA" "" "401"

echo ""

# 5. 转赠接收测试
echo -e "${PURPLE}=== 转赠接收测试 ===${NC}"

# 5.1 接收转赠
RECEIVE_GIFT_DATA='{"message":"感谢转赠"}'
test_api "接收转赠" "POST" "/api/v1/gifts/$GIFT_CODE/receive" "$RECEIVE_GIFT_DATA" "$RECEIVER_TOKEN" "200"

# 5.2 尝试重复接收
test_api "重复接收转赠" "POST" "/api/v1/gifts/$GIFT_CODE/receive" "$RECEIVE_GIFT_DATA" "$RECEIVER_TOKEN" "400"

# 5.3 尝试接收不存在的转赠
test_api "接收不存在的转赠" "POST" "/api/v1/gifts/NONEXISTENT/receive" "$RECEIVE_GIFT_DATA" "$RECEIVER_TOKEN" "404"

# 5.4 尝试无认证接收转赠
test_api "无认证接收转赠" "POST" "/api/v1/gifts/$GIFT_CODE/receive" "$RECEIVE_GIFT_DATA" "" "401"

echo ""

# 6. 转赠查询测试
echo -e "${PURPLE}=== 转赠查询测试 ===${NC}"

# 6.1 查询发送的转赠
test_api "查询发送的转赠" "GET" "/api/v1/gifts/my/sent?page=0&size=10" "" "$SENDER_TOKEN" "200"

# 6.2 查询接收的转赠
test_api "查询接收的转赠" "GET" "/api/v1/gifts/my/received?page=0&size=10" "" "$RECEIVER_TOKEN" "200"

# 6.3 查询转赠统计
test_api "查询转赠统计" "GET" "/api/v1/gifts/statistics" "" "$SENDER_TOKEN" "200"

# 6.4 尝试无认证查询
test_api "无认证查询发送转赠" "GET" "/api/v1/gifts/my/sent" "" "" "401"

echo ""

# 7. 转赠取消测试
echo -e "${PURPLE}=== 转赠取消测试 ===${NC}"

# 7.1 创建新的转赠用于取消测试
CREATE_CANCEL_GIFT_DATA="{\"gooseId\":$GOOSE_ID,\"message\":\"用于取消测试的转赠\",\"expiresHours\":72}"
test_api "创建用于取消的转赠" "POST" "/api/v1/gifts" "$CREATE_CANCEL_GIFT_DATA" "$SENDER_TOKEN" "200"

# 提取新的转赠码
if [ -f /tmp/last_response.json ] && command -v jq >/dev/null 2>&1; then
    CANCEL_GIFT_CODE=$(cat /tmp/last_response.json | jq -r '.data.giftCode // empty')
    if [ ! -z "$CANCEL_GIFT_CODE" ] && [ "$CANCEL_GIFT_CODE" != "null" ]; then
        echo "    ✅ 创建取消测试转赠码: $CANCEL_GIFT_CODE"

        # 7.2 取消转赠
        test_api "取消转赠" "DELETE" "/api/v1/gifts/$CANCEL_GIFT_CODE" "" "$SENDER_TOKEN" "200"

        # 7.3 尝试取消已取消的转赠
        test_api "取消已取消的转赠" "DELETE" "/api/v1/gifts/$CANCEL_GIFT_CODE" "" "$SENDER_TOKEN" "400"
    else
        echo "    ⚠️ 无法创建取消测试转赠，跳过取消测试"
    fi
else
    echo "    ⚠️ 无法创建取消测试转赠，跳过取消测试"
fi

# 7.4 尝试取消不存在的转赠
test_api "取消不存在的转赠" "DELETE" "/api/v1/gifts/NONEXISTENT" "" "$SENDER_TOKEN" "404"

# 7.5 尝试无认证取消转赠
test_api "无认证取消转赠" "DELETE" "/api/v1/gifts/$GIFT_CODE" "" "" "401"

echo ""

# 8. 边界条件和异常测试
echo -e "${PURPLE}=== 边界条件和异常测试 ===${NC}"

# 8.1 无效参数测试
INVALID_CREATE_DATA='{"gooseId":"invalid","message":"","expiresHours":-1}'
test_api "无效参数创建转赠" "POST" "/api/v1/gifts" "$INVALID_CREATE_DATA" "$SENDER_TOKEN" "400"

# 8.2 空消息测试
EMPTY_MESSAGE_DATA="{\"gooseId\":$GOOSE_ID,\"message\":\"\",\"expiresHours\":72}"
test_api "空消息创建转赠" "POST" "/api/v1/gifts" "$EMPTY_MESSAGE_DATA" "$SENDER_TOKEN" "200"

# 8.3 超长消息测试
LONG_MESSAGE=$(printf 'A%.0s' {1..1000})
LONG_MESSAGE_DATA="{\"gooseId\":$GOOSE_ID,\"message\":\"$LONG_MESSAGE\",\"expiresHours\":72}"
test_api "超长消息创建转赠" "POST" "/api/v1/gifts" "$LONG_MESSAGE_DATA" "$SENDER_TOKEN" "400"

# 8.4 无效过期时间测试
INVALID_EXPIRES_DATA="{\"gooseId\":$GOOSE_ID,\"message\":\"测试\",\"expiresHours\":0}"
test_api "无效过期时间" "POST" "/api/v1/gifts" "$INVALID_EXPIRES_DATA" "$SENDER_TOKEN" "400"

# 9. 性能和并发测试 (可选)
echo -e "${PURPLE}=== 性能测试 ===${NC}"

# 9.1 批量创建转赠测试 (测试系统负载)
echo -n "批量创建转赠测试 (5个) ... "
BATCH_SUCCESS=0
for i in {1..5}; do
    BATCH_DATA="{\"gooseId\":$GOOSE_ID,\"message\":\"批量测试$i\",\"expiresHours\":72}"
    BATCH_RESPONSE=$(curl -s -w '%{http_code}' -X POST -H "Content-Type: application/json" -H "satoken: $SENDER_TOKEN" -d "$BATCH_DATA" "${BASE_URL}/api/v1/gifts")
    BATCH_STATUS="${BATCH_RESPONSE: -3}"
    if [ "$BATCH_STATUS" = "200" ]; then
        ((BATCH_SUCCESS++))
    fi
done
echo -e "${GREEN}✅ 成功创建 $BATCH_SUCCESS/5 个转赠${NC}"

echo ""

# 10. 清理测试数据 (可选)
echo -e "${BLUE}=== 清理测试数据 ===${NC}"
echo "注意: 测试数据将保留在数据库中，可用于后续验证"
echo "如需清理，请手动删除测试用户和相关数据"

echo ""
echo "=================================="
echo "🎁 转赠模块API测试结果:"
echo "=================================="
echo "总测试数: $TOTAL_TESTS"
echo -e "通过测试: ${GREEN}$PASSED_TESTS${NC}"
echo -e "失败测试: ${RED}$((TOTAL_TESTS - PASSED_TESTS))${NC}"

SUCCESS_RATE=$((PASSED_TESTS * 100 / TOTAL_TESTS))
echo "成功率: ${SUCCESS_RATE}%"

echo ""
echo "📊 测试覆盖情况:"
echo "✅ 转赠创建功能"
echo "✅ 转赠接收功能"
echo "✅ 转赠取消功能"
echo "✅ 转赠查询功能"
echo "✅ 权限验证功能"
echo "✅ 参数验证功能"
echo "✅ 异常处理功能"
echo "✅ 边界条件测试"

echo ""
if [ $SUCCESS_RATE -ge 95 ]; then
    echo -e "${GREEN}🎉 转赠模块API测试结果优秀！${NC}"
    echo -e "${GREEN}✅ 转赠模块已准备好进入生产环境${NC}"
    exit 0
elif [ $SUCCESS_RATE -ge 85 ]; then
    echo -e "${YELLOW}⚠️ 转赠模块API测试结果良好${NC}"
    echo -e "${YELLOW}⚠️ 建议修复失败的测试用例后再部署${NC}"
    exit 0
elif [ $SUCCESS_RATE -ge 70 ]; then
    echo -e "${YELLOW}⚠️ 转赠模块API测试结果一般${NC}"
    echo -e "${YELLOW}⚠️ 需要修复多个问题后再部署${NC}"
    exit 1
else
    echo -e "${RED}❌ 转赠模块API测试结果需要改进${NC}"
    echo -e "${RED}❌ 不建议部署到生产环境${NC}"
    exit 1
fi
