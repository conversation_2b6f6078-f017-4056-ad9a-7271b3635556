# 小鹅核心模块测试指南

## 概述

本文档提供了小鹅核心模块的完整测试指南，包括单元测试、集成测试和API测试的执行方法。

## 测试架构

### 测试分层
```
测试层级
├── 单元测试 (Unit Tests)
│   ├── Service层测试 - 业务逻辑测试
│   └── Repository层测试 - 数据访问测试
├── 集成测试 (Integration Tests)
│   ├── Controller层测试 - API接口测试
│   └── 数据库集成测试
└── 端到端测试 (E2E Tests)
    └── 完整业务流程测试
```

### 测试技术栈
- **JUnit 5** - 测试框架
- **Mockito** - Mock框架
- **Spring Boot Test** - Spring集成测试
- **TestContainers** - 数据库集成测试
- **H2 Database** - 内存数据库测试

## 测试执行

### 1. 运行所有测试
```bash
# 运行所有测试
mvn test

# 跳过测试
mvn clean install -DskipTests
```

### 2. 运行特定模块测试

#### Service层单元测试
```bash
# 运行所有Service测试
mvn test -Dtest=com.gooseapp.goose.service.**

# 运行特定Service测试
mvn test -Dtest=GooseServiceTest
mvn test -Dtest=InteractionServiceTest
```

#### Repository层测试
```bash
# 运行所有Repository测试
mvn test -Dtest=com.gooseapp.goose.repository.**

# 运行特定Repository测试
mvn test -Dtest=GooseRepositoryTest
mvn test -Dtest=InteractionRepositoryTest
```

#### Controller层测试
```bash
# 运行所有Controller测试
mvn test -Dtest=com.gooseapp.goose.controller.**

# 运行特定Controller测试
mvn test -Dtest=GooseControllerTest
mvn test -Dtest=InteractionControllerTest
```

### 3. 运行特定测试方法
```bash
# 运行特定测试方法
mvn test -Dtest=GooseServiceTest#testCreateGoose
mvn test -Dtest=InteractionServiceTest#testPerformInteraction_Success
```

## 测试配置

### 测试环境配置
测试使用独立的配置文件：`src/test/resources/application-test.yml`

主要配置：
- H2内存数据库
- 测试专用Sa-Token配置
- 降低的业务规则限制（如冷却时间）

### 测试数据
测试数据通过以下方式提供：
- `src/test/resources/test-data.sql` - 基础测试数据
- 测试类中的`@BeforeEach`方法 - 动态测试数据

## 测试覆盖率

### 当前测试覆盖情况

#### GooseService测试覆盖
- ✅ 创建小鹅 (`testCreateGoose`)
- ✅ 获取小鹅详情 (`testGetGooseDetail`)
- ✅ 更新小鹅名称 (`testUpdateGooseName`)
- ✅ 删除小鹅 (`testDeleteGoose`)
- ✅ 喂食小鹅 (`testFeedGoose`)
- ✅ 获取需要照顾的小鹅 (`testGetGeeseNeedingCare`)
- ✅ 获取用户小鹅列表 (`testGetUserGeese`)
- ✅ 获取用户统计 (`testGetUserGooseStats`)
- ✅ 搜索小鹅 (`testSearchUserGeese`)

#### InteractionService测试覆盖
- ✅ 执行互动操作 (`testPerformInteraction_Success`)
- ✅ 冷却期检查 (`testPerformInteraction_InCooldown`)
- ✅ 冷却状态查询 (`testIsInCooldown`)
- ✅ 冷却剩余时间 (`testGetCooldownRemainingMinutes_*`)
- ✅ 获取所有冷却状态 (`testGetAllCooldownStatus`)
- ✅ 获取互动记录 (`testGetInteractions`)
- ✅ 今日统计 (`testGetTodayStats`)
- ✅ 互动类型统计 (`testGetInteractionTypeStats`)
- ✅ 连续天数计算 (`testCalculateContinuousDays`)
- ✅ 最活跃小鹅 (`testGetMostActiveGeese`)
- ✅ 用户总统计 (`testGetUserTotalStats`)

### 测试覆盖率报告
```bash
# 生成测试覆盖率报告
mvn clean test jacoco:report

# 查看报告
open target/site/jacoco/index.html
```

## 测试最佳实践

### 1. 测试命名规范
```java
// 格式：test + 方法名 + 场景 + 期望结果
@Test
void testCreateGoose_ValidInput_Success() { }

@Test
void testFeedGoose_InCooldown_ThrowsException() { }
```

### 2. 测试结构 (AAA模式)
```java
@Test
void testMethodName() {
    // Arrange - 准备测试数据
    // Given

    // Act - 执行被测试方法
    // When

    // Assert - 验证结果
    // Then
}
```

### 3. Mock使用原则
- 只Mock外部依赖
- 避免过度Mock
- 使用`@MockBean`进行Spring集成测试
- 使用`@Mock`进行纯单元测试

### 4. 测试数据管理
- 每个测试方法独立的测试数据
- 使用`@BeforeEach`准备通用数据
- 避免测试间的数据依赖

## 常见问题解决

### 1. 数据库相关错误
```bash
# 问题：Table 'xxx' doesn't exist
# 解决：确保schema.sql正确执行
# 检查：src/main/resources/schema.sql

# 问题：测试数据不存在
# 解决：检查test-data.sql
# 检查：src/test/resources/test-data.sql
```

### 2. Mock相关错误
```bash
# 问题：UnnecessaryStubbingException
# 解决：移除未使用的when().thenReturn()

# 问题：NullPointerException in mock
# 解决：确保所有依赖都正确Mock
```

### 3. Spring Context错误
```bash
# 问题：ApplicationContext failure
# 解决：检查测试配置和依赖注入

# 问题：Bean creation failed
# 解决：检查@MockBean和@Autowired的使用
```

## 持续集成

### GitHub Actions配置
```yaml
# .github/workflows/test.yml
name: Tests
on: [push, pull_request]
jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - name: Set up JDK 17
        uses: actions/setup-java@v2
        with:
          java-version: '17'
      - name: Run tests
        run: mvn clean test
```

### 测试报告
- 测试结果：`target/surefire-reports/`
- 覆盖率报告：`target/site/jacoco/`
- 测试日志：控制台输出

## 性能测试

### 基准测试
```java
@Test
@Timeout(value = 5, unit = TimeUnit.SECONDS)
void testPerformanceRequirement() {
    // 性能要求测试
}
```

### 压力测试
```bash
# 使用JMeter或类似工具进行API压力测试
# 测试并发用户场景
# 验证系统在高负载下的表现
```

## API集成测试

### 测试环境准备

#### 1. 启动服务
```bash
# 启动Spring Boot应用
mvn spring-boot:run

# 或者使用IDE启动主类
# com.gooseapp.GooseAppApplication
```

#### 2. 服务地址
- **本地开发环境**: `http://localhost:8080`
- **测试环境**: `http://test.gooseapp.com:8080`

#### 3. 数据库初始化
确保数据库已初始化并包含测试数据：
```sql
-- 测试用户账号
INSERT INTO users (username, password, email, nickname) VALUES
('testuser', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8imdqMNq4NjewGdPiS6qk2FQRwDgO', '<EMAIL>', '测试用户');
-- 密码: 123456
```

### API测试流程

#### 第一步：用户认证

##### 1.1 用户登录获取Token
```bash
curl -X POST "http://localhost:8080/api/v1/auth/login" \
  -H "Content-Type: application/json" \
  -d '{
    "username": "testuser",
    "password": "123456"
  }'
```

**预期响应**:
```json
{
  "success": true,
  "message": "登录成功",
  "data": {
    "token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
    "tokenName": "satoken",
    "tokenValue": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
    "isLogin": true,
    "loginId": 1,
    "loginType": "login",
    "tokenTimeout": 2592000,
    "sessionTimeout": -1,
    "tokenSessionTimeout": -2,
    "tokenActiveTimeout": -1,
    "loginDevice": "default-device",
    "tag": null,
    "userInfo": {
      "id": 1,
      "username": "testuser",
      "email": "<EMAIL>",
      "nickname": "测试用户"
    }
  }
}
```

> **重要**: 复制响应中的`token`值，后续所有请求都需要在Header中携带此token

#### 第二步：小鹅管理接口测试

##### 2.1 创建新小鹅
```bash
curl -X POST "http://localhost:8080/api/v1/geese" \
  -H "Content-Type: application/json" \
  -H "satoken: Bearer YOUR_TOKEN_HERE" \
  -d '{
    "name": "小白",
    "breed": "白鹅",
    "gender": 1
  }'
```

**预期响应**:
```json
{
  "success": true,
  "message": "创建成功",
  "data": {
    "id": 1,
    "name": "小白",
    "breed": "白鹅",
    "gender": 1,
    "age": 1,
    "level": 1,
    "experience": 0,
    "expToNextLevel": 100,
    "imageUrl": "/images/geese/白鹅_male.jpg",
    "status": 1,
    "statusDescription": "正常",
    "overallScore": 85.0,
    "needsCare": false,
    "createdAt": "2024-12-19T10:30:00"
  }
}
```

##### 2.2 获取我的小鹅列表
```bash
curl -X GET "http://localhost:8080/api/v1/geese/my" \
  -H "satoken: Bearer YOUR_TOKEN_HERE"
```

##### 2.3 获取小鹅详细信息
```bash
curl -X GET "http://localhost:8080/api/v1/geese/1" \
  -H "satoken: Bearer YOUR_TOKEN_HERE"
```

**预期响应**:
```json
{
  "success": true,
  "message": "查询成功",
  "data": {
    "id": 1,
    "userId": 1,
    "name": "小白",
    "breed": "白鹅",
    "breedDescription": "温顺可爱的白鹅",
    "gender": 1,
    "genderDescription": "雄性",
    "age": 1,
    "health": 95,
    "hunger": 20,
    "thirst": 15,
    "cleanliness": 90,
    "happiness": 85,
    "level": 1,
    "experience": 0,
    "expToNextLevel": 100,
    "imageUrl": "/images/geese/白鹅_male.jpg",
    "videoUrl": null,
    "lastFeedTime": null,
    "lastWaterTime": null,
    "lastCleanTime": null,
    "status": 1,
    "statusDescription": "正常",
    "overallScore": 85.0,
    "needsCare": false,
    "cooldownStatus": {
      "feedCooldownMinutes": 0,
      "canFeed": true,
      "waterCooldownMinutes": 0,
      "canWater": true,
      "cleanCooldownMinutes": 0,
      "canClean": true,
      "playCooldownMinutes": 0,
      "canPlay": true
    },
    "todayStats": {
      "totalInteractions": 0,
      "feedCount": 0,
      "waterCount": 0,
      "cleanCount": 0,
      "playCount": 0,
      "experienceGained": 0
    }
  }
}
```

##### 2.4 更新小鹅名称
```bash
curl -X PUT "http://localhost:8080/api/v1/geese/1/name" \
  -H "Content-Type: application/json" \
  -H "satoken: Bearer YOUR_TOKEN_HERE" \
  -d '{
    "name": "小白白"
  }'
```

##### 2.5 搜索小鹅
```bash
curl -X GET "http://localhost:8080/api/v1/geese/search?keyword=小白" \
  -H "satoken: Bearer YOUR_TOKEN_HERE"
```

##### 2.6 获取需要照顾的小鹅
```bash
curl -X GET "http://localhost:8080/api/v1/geese/need-care" \
  -H "satoken: Bearer YOUR_TOKEN_HERE"
```

#### 第三步：互动操作接口测试

##### 3.1 喂食小鹅
```bash
curl -X POST "http://localhost:8080/api/v1/geese/1/feed" \
  -H "satoken: Bearer YOUR_TOKEN_HERE"
```

**预期响应**:
```json
{
  "success": true,
  "message": "喂食成功",
  "data": {
    "id": 1,
    "userId": 1,
    "gooseId": 1,
    "gooseName": "小白白",
    "interactionType": "feed",
    "interactionTypeDescription": "喂食",
    "valueChange": 15,
    "experienceGained": 5,
    "cooldownMinutes": 60,
    "inCooldown": true,
    "remainingCooldownMinutes": 60,
    "cooldownEndTime": "2024-12-19T11:30:00",
    "createdAt": "2024-12-19T10:30:00"
  }
}
```

##### 3.2 给小鹅喂水
```bash
curl -X POST "http://localhost:8080/api/v1/geese/1/water" \
  -H "satoken: Bearer YOUR_TOKEN_HERE"
```

##### 3.3 清洁小鹅
```bash
curl -X POST "http://localhost:8080/api/v1/geese/1/clean" \
  -H "satoken: Bearer YOUR_TOKEN_HERE"
```

##### 3.4 与小鹅玩耍
```bash
curl -X POST "http://localhost:8080/api/v1/geese/1/play" \
  -H "satoken: Bearer YOUR_TOKEN_HERE"
```

#### 第四步：查询统计接口测试

##### 4.1 获取用户小鹅统计
```bash
curl -X GET "http://localhost:8080/api/v1/geese/stats" \
  -H "satoken: Bearer YOUR_TOKEN_HERE"
```

**预期响应**:
```json
{
  "success": true,
  "message": "查询成功",
  "data": {
    "totalGeese": 1,
    "healthyGeese": 1,
    "sickGeese": 0,
    "geeseNeedingCare": 0,
    "totalInteractions": 4,
    "todayInteractions": 4,
    "averageLevel": 1.0,
    "averageHealth": 95.0,
    "totalExperience": 20,
    "continuousDays": 1
  }
}
```

##### 4.2 获取小鹅互动记录
```bash
curl -X GET "http://localhost:8080/api/v1/geese/1/interactions?page=0&size=10" \
  -H "satoken: Bearer YOUR_TOKEN_HERE"
```

##### 4.3 获取我的所有互动记录
```bash
curl -X GET "http://localhost:8080/api/v1/interactions/my?page=0&size=20" \
  -H "satoken: Bearer YOUR_TOKEN_HERE"
```

##### 4.4 查询特定小鹅的冷却状态
```bash
curl -X GET "http://localhost:8080/api/v1/interactions/cooldown/1" \
  -H "satoken: Bearer YOUR_TOKEN_HERE"
```

**预期响应**:
```json
{
  "success": true,
  "message": "查询成功",
  "data": {
    "feed": {
      "inCooldown": true,
      "remainingMinutes": 45,
      "endTime": "2024-12-19T11:30:00",
      "canInteract": false
    },
    "water": {
      "inCooldown": false,
      "remainingMinutes": 0,
      "endTime": null,
      "canInteract": true
    },
    "clean": {
      "inCooldown": false,
      "remainingMinutes": 0,
      "endTime": null,
      "canInteract": true
    },
    "play": {
      "inCooldown": false,
      "remainingMinutes": 0,
      "endTime": null,
      "canInteract": true
    }
  }
}
```

##### 4.5 查询特定互动类型的冷却状态
```bash
curl -X GET "http://localhost:8080/api/v1/interactions/cooldown/1/feed" \
  -H "satoken: Bearer YOUR_TOKEN_HERE"
```

##### 4.6 获取今日互动统计
```bash
# 特定小鹅的今日统计
curl -X GET "http://localhost:8080/api/v1/interactions/stats/today?gooseId=1" \
  -H "satoken: Bearer YOUR_TOKEN_HERE"

# 所有小鹅的今日统计
curl -X GET "http://localhost:8080/api/v1/interactions/stats/today" \
  -H "satoken: Bearer YOUR_TOKEN_HERE"
```

##### 4.7 获取互动类型统计
```bash
curl -X GET "http://localhost:8080/api/v1/interactions/stats/types" \
  -H "satoken: Bearer YOUR_TOKEN_HERE"
```

**预期响应**:
```json
{
  "success": true,
  "message": "查询成功",
  "data": {
    "feed": 1,
    "water": 1,
    "clean": 1,
    "play": 1
  }
}
```

##### 4.8 获取周统计数据
```bash
curl -X GET "http://localhost:8080/api/v1/interactions/stats/weekly" \
  -H "satoken: Bearer YOUR_TOKEN_HERE"
```

##### 4.9 获取用户总统计
```bash
curl -X GET "http://localhost:8080/api/v1/interactions/stats/total" \
  -H "satoken: Bearer YOUR_TOKEN_HERE"
```

##### 4.10 获取最活跃小鹅排行
```bash
curl -X GET "http://localhost:8080/api/v1/interactions/stats/active-geese?limit=10" \
  -H "satoken: Bearer YOUR_TOKEN_HERE"
```

##### 4.11 获取连续互动天数
```bash
curl -X GET "http://localhost:8080/api/v1/interactions/stats/continuous-days" \
  -H "satoken: Bearer YOUR_TOKEN_HERE"
```

#### 第五步：删除操作测试

##### 5.1 删除小鹅（谨慎操作）
```bash
curl -X DELETE "http://localhost:8080/api/v1/geese/1" \
  -H "satoken: Bearer YOUR_TOKEN_HERE"
```

### ApiPost使用指南

#### 1. 导入curl命令
1. 打开ApiPost
2. 点击"导入" -> "从cURL导入"
3. 粘贴上述curl命令
4. 点击"导入"

#### 2. 环境变量设置
在ApiPost中设置环境变量：
```
BASE_URL: http://localhost:8080
TOKEN: YOUR_TOKEN_HERE
```

然后在请求中使用：
```
URL: {{BASE_URL}}/api/v1/geese/my
Header: satoken: Bearer {{TOKEN}}
```

#### 3. 测试集合创建
建议按以下顺序创建测试集合：
1. **认证测试** - 登录获取token
2. **小鹅管理** - CRUD操作
3. **互动操作** - 四种互动类型
4. **统计查询** - 各种统计接口
5. **清理操作** - 删除测试数据

### 常见错误处理

#### 1. 认证相关错误
```json
{
  "success": false,
  "code": 401,
  "message": "未登录或token已过期"
}
```
**解决方案**: 重新登录获取新token

#### 2. 权限相关错误
```json
{
  "success": false,
  "code": 403,
  "message": "小鹅不存在或不属于当前用户"
}
```
**解决方案**: 检查小鹅ID是否正确，确保是当前用户的小鹅

#### 3. 业务规则错误
```json
{
  "success": false,
  "code": 400,
  "message": "互动操作冷却中，请稍后再试"
}
```
**解决方案**: 等待冷却时间结束或查询冷却状态

#### 4. 参数验证错误
```json
{
  "success": false,
  "code": 400,
  "message": "小鹅名称不能为空"
}
```
**解决方案**: 检查请求参数是否符合验证规则

### 自动化测试脚本

#### Postman/ApiPost测试脚本
```javascript
// 登录后自动保存token
pm.test("Login successful", function () {
    var jsonData = pm.response.json();
    pm.expect(jsonData.success).to.be.true;
    pm.environment.set("TOKEN", jsonData.data.token);
});

// 验证响应格式
pm.test("Response format is correct", function () {
    var jsonData = pm.response.json();
    pm.expect(jsonData).to.have.property('success');
    pm.expect(jsonData).to.have.property('message');
    pm.expect(jsonData).to.have.property('data');
});
```

## 总结

本测试指南涵盖了小鹅核心模块的完整测试策略。通过遵循这些指南，可以确保：

1. **代码质量** - 通过全面的单元测试保证业务逻辑正确性
2. **系统稳定性** - 通过集成测试验证组件间协作
3. **API可靠性** - 通过Controller测试和API集成测试确保接口正确性
4. **持续改进** - 通过测试覆盖率监控代码质量

定期运行测试，保持高测试覆盖率，是保证项目质量的重要手段。
