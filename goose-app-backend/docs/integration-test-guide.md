# 小鹅养成APP集成测试指导文档

## 📋 测试概述

本文档提供了小鹅养成APP重构后的完整集成测试指导，包括环境搭建、数据库初始化、服务启动和API测试用例。

## 🛠️ 测试环境搭建

### 1. 环境要求
- Java 17+
- MySQL 8.0+
- Maven 3.6+
- APIPost 或 Postman 测试工具

### 2. 数据库初始化

#### 2.1 创建数据库
```sql
CREATE DATABASE goose_app_test CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE goose_app_test;
```

#### 2.2 执行初始化脚本
请按顺序执行以下SQL脚本：

1. **用户表和角色表**（已存在）
2. **小鹅表**（已存在）
3. **购买记录表**（新增）

```sql
-- 购买记录表
CREATE TABLE goose_purchase (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    user_id BIGINT NOT NULL COMMENT '购买用户ID',
    goose_id BIGINT NOT NULL COMMENT '购买的小鹅ID',
    product_id BIGINT COMMENT '关联商品ID',
    purchase_price DECIMAL(10,2) NOT NULL COMMENT '购买价格',
    purchase_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '购买时间',
    status INT NOT NULL DEFAULT 1 COMMENT '状态：1-成功，0-失败',
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_user_id (user_id),
    INDEX idx_goose_id (goose_id),
    INDEX idx_purchase_time (purchase_time),
    INDEX idx_status (status)
) COMMENT='小鹅购买记录表';

-- 用户角色关联表
CREATE TABLE user_role (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    user_id BIGINT NOT NULL COMMENT '用户ID',
    role VARCHAR(50) NOT NULL COMMENT '角色：ADMIN-管理员，USER-普通用户',
    assigned_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '分配时间',
    assigned_by BIGINT COMMENT '分配者ID',
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY uk_user_role (user_id, role),
    INDEX idx_user_id (user_id),
    INDEX idx_role (role)
) COMMENT='用户角色关联表';
```

#### 2.3 插入测试数据

```sql
-- 插入测试用户
INSERT INTO user (username, password, email, phone, status) VALUES
('admin', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iKXIGfcqkunzgTB2l2EmOtUubuxK', '<EMAIL>', '13800000001', 1),
('testuser', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iKXIGfcqkunzgTB2l2EmOtUubuxK', '<EMAIL>', '13800000002', 1);

-- 分配角色
INSERT INTO user_role (user_id, role, assigned_by) VALUES
(1, 'ADMIN', 1),
(2, 'USER', 1);

-- 插入待售小鹅（管理员创建的商品池）
INSERT INTO goose (user_id, name, breed, gender, age, health, hunger, thirst, cleanliness, happiness, level, experience, ownership_status, status) VALUES
(NULL, '小白', '白鹅', 1, 30, 100, 80, 85, 90, 95, 1, 0, 0, 1),
(NULL, '小灰', '灰鹅', 2, 25, 95, 75, 80, 85, 90, 1, 0, 0, 1),
(NULL, '小黑', '黑鹅', 1, 35, 90, 70, 75, 80, 85, 2, 150, 0, 1);

-- 插入已归属小鹅（用户已购买的）
INSERT INTO goose (user_id, name, breed, gender, age, health, hunger, thirst, cleanliness, happiness, level, experience, ownership_status, status, purchase_time) VALUES
(2, '我的小鹅', '白鹅', 1, 20, 85, 60, 65, 70, 75, 1, 50, 1, 1, NOW());

-- 插入购买记录
INSERT INTO goose_purchase (user_id, goose_id, purchase_price, purchase_time, status) VALUES
(2, 4, 99.99, NOW(), 1);
```

## 🚀 服务启动

### 1. 配置文件检查
确保 `application-test.yml` 配置正确：

```yaml
spring:
  datasource:
    url: ***************************************************************************************************************************
    username: root
    password: your_password
  jpa:
    hibernate:
      ddl-auto: validate
    show-sql: true

sa-token:
  token-name: satoken
  timeout: 2592000
  activity-timeout: -1
  is-concurrent: true
  is-share: true
  token-style: uuid
  is-log: false
```

### 2. 启动命令
```bash
cd goose-app-backend
mvn spring-boot:run -Dspring-boot.run.profiles=test
```

### 3. 验证启动
访问：http://localhost:8080/actuator/health
预期返回：`{"status":"UP"}`

## 🧪 API测试用例

### 测试前准备
1. 获取管理员Token：
   ```
   POST /api/v1/auth/login
   Content-Type: application/json
   
   {
     "username": "admin",
     "password": "123456"
   }
   ```

2. 获取普通用户Token：
   ```
   POST /api/v1/auth/login
   Content-Type: application/json
   
   {
     "username": "testuser", 
     "password": "123456"
   }
   ```

### 核心功能测试用例

#### 1. 管理员功能测试

**1.1 创建小鹅到商品池**
```
POST /api/v1/admin/geese
Headers: satoken: Bearer {admin_token}
Content-Type: application/json

{
  "name": "新小鹅",
  "breed": "白鹅",
  "gender": 1,
  "age": 30,
  "health": 100,
  "hunger": 80,
  "thirst": 85,
  "cleanliness": 90,
  "happiness": 95
}
```
预期结果：创建成功，返回小鹅信息，ownershipStatus为0

**1.2 批量创建小鹅**
```
POST /api/v1/admin/geese/batch
Headers: satoken: Bearer {admin_token}
Content-Type: application/json

{
  "geese": [
    {
      "name": "批量小鹅1",
      "breed": "灰鹅",
      "gender": 1
    },
    {
      "name": "批量小鹅2", 
      "breed": "黑鹅",
      "gender": 2
    }
  ],
  "ignoreErrors": false
}
```
预期结果：批量创建成功，返回创建的小鹅列表

**1.3 查看所有小鹅**
```
GET /api/v1/admin/geese?page=0&size=20
Headers: satoken: Bearer {admin_token}
```
预期结果：返回所有小鹅的分页列表

#### 2. 商城功能测试

**2.1 查看待售小鹅**
```
GET /api/v1/shop/geese/available?page=0&size=20
```
预期结果：返回ownershipStatus为0的小鹅列表

**2.2 按品种筛选**
```
GET /api/v1/shop/geese/available/breed/白鹅
```
预期结果：返回指定品种的待售小鹅

**2.3 购买小鹅**
```
POST /api/v1/shop/purchase
Headers: satoken: Bearer {user_token}
Content-Type: application/json

{
  "gooseId": 1,
  "purchasePrice": 99.99,
  "remark": "购买测试"
}
```
预期结果：购买成功，小鹅归属权转移给用户

**2.4 查看购买记录**
```
GET /api/v1/shop/purchases/my?page=0&size=20
Headers: satoken: Bearer {user_token}
```
预期结果：返回用户的购买记录

#### 3. 用户小鹅管理测试

**3.1 查看我的小鹅**
```
GET /api/v1/geese/my
Headers: satoken: Bearer {user_token}
```
预期结果：只返回用户已归属的小鹅（ownershipStatus为1）

**3.2 小鹅互动**
```
POST /api/v1/geese/{gooseId}/feed
Headers: satoken: Bearer {user_token}
Content-Type: application/json

{
  "foodType": "premium",
  "amount": 10
}
```
预期结果：喂食成功，小鹅属性更新

## ✅ 验证标准

### 1. 功能验证
- [ ] 管理员可以创建小鹅到商品池
- [ ] 用户可以浏览和购买待售小鹅
- [ ] 购买后小鹅归属权正确转移
- [ ] 用户只能看到和操作自己的小鹅
- [ ] 权限控制正确（管理员/用户权限分离）

### 2. 数据一致性验证
- [ ] 购买后小鹅的ownershipStatus更新为1
- [ ] 购买记录正确创建
- [ ] 用户ID正确关联
- [ ] 时间戳正确记录

### 3. 异常处理验证
- [ ] 购买不存在的小鹅返回错误
- [ ] 购买已归属的小鹅返回错误
- [ ] 无权限操作返回401/403错误
- [ ] 参数验证错误返回400错误

## 📊 测试报告模板

测试完成后，请按以下格式记录测试结果：

```
测试时间：2024-12-19
测试环境：本地开发环境
测试人员：[姓名]

功能测试结果：
✅ 管理员创建小鹅：通过
✅ 用户购买小鹅：通过
✅ 权限控制：通过
❌ 批量操作：失败（具体错误信息）

发现问题：
1. [问题描述]
2. [问题描述]

建议：
1. [改进建议]
2. [改进建议]
```

## 🔧 常见问题排查

1. **数据库连接失败**：检查数据库配置和服务状态
2. **Token验证失败**：确认Token格式为 `satoken: Bearer TOKEN`
3. **权限错误**：确认用户角色分配正确
4. **小鹅不可购买**：检查ownershipStatus状态

---

**注意**：本测试指导文档基于重构后的业务逻辑，请严格按照步骤执行测试。
