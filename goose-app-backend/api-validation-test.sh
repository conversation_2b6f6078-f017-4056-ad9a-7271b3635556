#!/bin/bash

# 小鹅养成APP - API验证测试脚本
# 验证所有核心API接口功能

echo "🦆 小鹅养成APP - API验证测试"
echo "================================"

BASE_URL="http://localhost:8080"
TOTAL_TESTS=0
PASSED_TESTS=0

# 颜色定义
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 测试函数
test_api() {
    local name=$1
    local method=$2
    local endpoint=$3
    local data=$4
    local token=$5
    local expected_status=$6
    
    ((TOTAL_TESTS++))
    echo -n "测试 $name ... "
    
    local curl_cmd="curl -s -w '%{http_code}' -X $method"
    
    if [ ! -z "$token" ]; then
        curl_cmd="$curl_cmd -H 'satoken: Bearer $token'"
    fi
    
    if [ "$method" = "POST" ] || [ "$method" = "PUT" ]; then
        curl_cmd="$curl_cmd -H 'Content-Type: application/json'"
    fi
    
    if [ ! -z "$data" ]; then
        curl_cmd="$curl_cmd -d '$data'"
    fi
    
    curl_cmd="$curl_cmd '${BASE_URL}${endpoint}'"
    
    local response=$(eval $curl_cmd)
    local status_code="${response: -3}"
    local body="${response%???}"
    
    if [ "$status_code" = "$expected_status" ]; then
        echo -e "${GREEN}✅ 通过${NC} ($status_code)"
        ((PASSED_TESTS++))
        
        # 如果是登录接口，提取token
        if [[ "$endpoint" == *"/login"* ]] && [ "$status_code" = "200" ]; then
            if command -v jq >/dev/null 2>&1; then
                TOKEN=$(echo "$body" | jq -r '.data.token // empty')
                if [ ! -z "$TOKEN" ]; then
                    echo "    Token: ${TOKEN:0:20}..."
                fi
            fi
        fi
    else
        echo -e "${RED}❌ 失败${NC} (期望: $expected_status, 实际: $status_code)"
        if [ ! -z "$body" ] && [ ${#body} -lt 200 ]; then
            echo "    响应: $body"
        fi
    fi
}

echo "检查应用状态..."
if curl -s "$BASE_URL/api/v1/shop/geese/breeds" >/dev/null; then
    echo -e "${GREEN}✅ 应用运行正常${NC}"
else
    echo -e "${RED}❌ 应用未运行或无法访问${NC}"
    exit 1
fi

echo ""
echo "开始API验证测试..."
echo ""

# 1. 商城模块测试 (无需认证)
echo -e "${BLUE}=== 商城模块测试 ===${NC}"
test_api "获取小鹅品种列表" "GET" "/api/v1/shop/geese/breeds" "" "" "200"
test_api "获取待售小鹅列表" "GET" "/api/v1/shop/geese/available" "" "" "200"
test_api "按品种查询小鹅" "GET" "/api/v1/shop/geese/available/breed/白鹅" "" "" "200"
test_api "搜索待售小鹅" "GET" "/api/v1/shop/geese/search?keyword=小鹅" "" "" "200"
test_api "获取热门小鹅" "GET" "/api/v1/shop/geese/popular?limit=5" "" "" "200"

echo ""

# 2. 用户认证模块测试
echo -e "${BLUE}=== 用户认证模块测试 ===${NC}"

# 注册新用户
REGISTER_DATA='{"username":"apitest","email":"<EMAIL>","password":"password123","confirmPassword":"password123","nickname":"API测试用户"}'
test_api "用户注册" "POST" "/api/v1/auth/register" "$REGISTER_DATA" "" "200"

# 用户登录
LOGIN_DATA='{"username":"apitest","password":"password123"}'
echo -n "测试 用户登录 ... "
LOGIN_RESPONSE=$(curl -s -X POST -H "Content-Type: application/json" -d "$LOGIN_DATA" "${BASE_URL}/api/v1/auth/login")
LOGIN_STATUS=$(echo "$LOGIN_RESPONSE" | tail -c 4)

if [[ "$LOGIN_RESPONSE" == *"登录成功"* ]]; then
    echo -e "${GREEN}✅ 通过${NC} (200)"
    ((PASSED_TESTS++))
    
    # 提取用户Token
    if command -v jq >/dev/null 2>&1; then
        USER_TOKEN=$(echo "$LOGIN_RESPONSE" | jq -r '.data.token // empty')
        echo "    用户Token: ${USER_TOKEN:0:20}..."
    fi
else
    echo -e "${RED}❌ 失败${NC}"
fi
((TOTAL_TESTS++))

# 管理员登录
ADMIN_LOGIN_DATA='{"username":"superAdmin","password":"admin123"}'
echo -n "测试 管理员登录 ... "
ADMIN_LOGIN_RESPONSE=$(curl -s -X POST -H "Content-Type: application/json" -d "$ADMIN_LOGIN_DATA" "${BASE_URL}/api/v1/auth/login")

if [[ "$ADMIN_LOGIN_RESPONSE" == *"登录成功"* ]]; then
    echo -e "${GREEN}✅ 通过${NC} (200)"
    ((PASSED_TESTS++))
    
    # 提取管理员Token
    if command -v jq >/dev/null 2>&1; then
        ADMIN_TOKEN=$(echo "$ADMIN_LOGIN_RESPONSE" | jq -r '.data.token // empty')
        echo "    管理员Token: ${ADMIN_TOKEN:0:20}..."
    fi
else
    echo -e "${RED}❌ 失败${NC}"
fi
((TOTAL_TESTS++))

# 测试无效登录
INVALID_LOGIN_DATA='{"username":"nonexistent","password":"wrongpassword"}'
test_api "无效用户登录" "POST" "/api/v1/auth/login" "$INVALID_LOGIN_DATA" "" "400"

echo ""

# 3. 用户管理模块测试 (需要认证)
if [ ! -z "$USER_TOKEN" ]; then
    echo -e "${BLUE}=== 用户管理模块测试 ===${NC}"
    test_api "获取当前用户信息" "GET" "/api/v1/auth/me" "" "$USER_TOKEN" "200"
    test_api "获取用户详细信息" "GET" "/api/v1/users/profile" "" "$USER_TOKEN" "200"
    test_api "获取用户统计信息" "GET" "/api/v1/users/statistics" "" "$USER_TOKEN" "200"
    test_api "搜索用户" "GET" "/api/v1/users/search?keyword=test" "" "$USER_TOKEN" "200"
    
    # 更新用户信息
    UPDATE_DATA='{"nickname":"更新后的昵称","gender":1}'
    test_api "更新用户信息" "PUT" "/api/v1/users/profile" "$UPDATE_DATA" "$USER_TOKEN" "200"
    
    echo ""
fi

# 4. 小鹅核心模块测试 (需要认证)
if [ ! -z "$USER_TOKEN" ]; then
    echo -e "${BLUE}=== 小鹅核心模块测试 ===${NC}"
    test_api "获取我的小鹅列表" "GET" "/api/v1/geese/my" "" "$USER_TOKEN" "200"
    test_api "搜索小鹅" "GET" "/api/v1/geese/search?keyword=小鹅" "" "$USER_TOKEN" "200"
    test_api "获取需要照顾的小鹅" "GET" "/api/v1/geese/needing-care" "" "$USER_TOKEN" "200"
    test_api "获取不存在的小鹅" "GET" "/api/v1/geese/99999" "" "$USER_TOKEN" "404"
    
    echo ""
fi

# 5. 小鹅互动模块测试 (需要认证)
if [ ! -z "$USER_TOKEN" ]; then
    echo -e "${BLUE}=== 小鹅互动模块测试 ===${NC}"
    test_api "获取我的互动记录" "GET" "/api/v1/interactions/my?page=0&size=10" "" "$USER_TOKEN" "200"
    test_api "获取互动统计信息" "GET" "/api/v1/interactions/statistics" "" "$USER_TOKEN" "200"
    test_api "获取今日互动统计" "GET" "/api/v1/interactions/today" "" "$USER_TOKEN" "200"
    test_api "获取互动成就" "GET" "/api/v1/interactions/achievements" "" "$USER_TOKEN" "200"
    test_api "获取互动排行榜" "GET" "/api/v1/interactions/leaderboard?type=feed&limit=5" "" "$USER_TOKEN" "200"
    
    echo ""
fi

# 6. 商城购买模块测试 (需要认证)
if [ ! -z "$USER_TOKEN" ]; then
    echo -e "${BLUE}=== 商城购买模块测试 ===${NC}"
    test_api "查看我的购买记录" "GET" "/api/v1/shop/purchases/my?page=0&size=10" "" "$USER_TOKEN" "200"
    test_api "获取我的购买统计" "GET" "/api/v1/shop/purchases/statistics/my" "" "$USER_TOKEN" "200"
    
    # 测试购买不存在的小鹅
    PURCHASE_DATA='{"gooseId":99999,"paymentMethod":"wechat"}'
    test_api "购买不存在的小鹅" "POST" "/api/v1/shop/purchase" "$PURCHASE_DATA" "$USER_TOKEN" "404"
    
    echo ""
fi

# 7. 管理后台模块测试 (需要管理员认证)
if [ ! -z "$ADMIN_TOKEN" ]; then
    echo -e "${BLUE}=== 管理后台模块测试 ===${NC}"
    test_api "管理员查看所有小鹅" "GET" "/api/v1/admin/geese/all?page=0&size=10" "" "$ADMIN_TOKEN" "200"
    test_api "管理员查看待售小鹅" "GET" "/api/v1/admin/geese/available?page=0&size=10" "" "$ADMIN_TOKEN" "200"
    test_api "管理员查看已归属小鹅" "GET" "/api/v1/admin/geese/owned?page=0&size=10" "" "$ADMIN_TOKEN" "200"
    test_api "管理员获取小鹅统计" "GET" "/api/v1/admin/geese/statistics" "" "$ADMIN_TOKEN" "200"
    test_api "管理员搜索小鹅" "GET" "/api/v1/admin/geese/search?keyword=小鹅" "" "$ADMIN_TOKEN" "200"
    
    # 测试普通用户访问管理员接口
    if [ ! -z "$USER_TOKEN" ]; then
        test_api "普通用户访问管理员接口" "GET" "/api/v1/admin/geese/all" "" "$USER_TOKEN" "403"
    fi
    
    echo ""
fi

# 8. 权限控制测试
echo -e "${BLUE}=== 权限控制测试 ===${NC}"
test_api "无Token访问用户接口" "GET" "/api/v1/users/profile" "" "" "401"
test_api "无Token访问小鹅接口" "GET" "/api/v1/geese/my" "" "" "401"
test_api "无Token访问管理员接口" "GET" "/api/v1/admin/geese/all" "" "" "401"

echo ""
echo "================================"
echo "API验证测试结果:"
echo "总测试数: $TOTAL_TESTS"
echo -e "通过测试: ${GREEN}$PASSED_TESTS${NC}"
echo -e "失败测试: ${RED}$((TOTAL_TESTS - PASSED_TESTS))${NC}"

SUCCESS_RATE=$((PASSED_TESTS * 100 / TOTAL_TESTS))
echo "成功率: ${SUCCESS_RATE}%"

if [ $SUCCESS_RATE -ge 90 ]; then
    echo -e "${GREEN}🎉 API验证测试结果优秀！${NC}"
    exit 0
elif [ $SUCCESS_RATE -ge 80 ]; then
    echo -e "${YELLOW}⚠️ API验证测试结果良好${NC}"
    exit 0
else
    echo -e "${RED}❌ API验证测试需要改进${NC}"
    exit 1
fi
