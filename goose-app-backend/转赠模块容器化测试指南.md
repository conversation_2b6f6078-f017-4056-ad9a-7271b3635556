# 🎁 转赠模块容器化测试指南

## 📋 概述

由于Spring集成测试遇到ApplicationContext加载问题，我们采用容器化部署 + curl API测试的方案来验证转赠模块功能。这种方法可以：

- ✅ 绕过Spring测试上下文问题
- ✅ 测试真实的HTTP API接口
- ✅ 验证完整的请求-响应流程
- ✅ 模拟真实的生产环境

## 🚀 快速开始

### 1. 启动应用

#### 方式一：直接启动 (推荐)
```bash
cd goose-app-backend
mvn spring-boot:run
```

#### 方式二：打包后启动
```bash
cd goose-app-backend
mvn clean package -DskipTests
java -jar target/goose-app-backend-1.0.0.jar
```

### 2. 验证应用启动
```bash
# 检查应用健康状态
curl http://localhost:8080/api/v1/shop/geese/breeds

# 预期响应: 200 OK，返回小鹅品种列表
```

### 3. 运行转赠模块测试
```bash
cd goose-app-backend
./gift-api-test.sh
```

## 📊 测试脚本说明

### 测试覆盖范围

我们的 `gift-api-test.sh` 脚本包含以下测试：

#### 🔐 认证准备
- 注册测试用户（发送者、接收者）
- 用户登录获取Token
- 管理员登录验证

#### 🎁 核心功能测试
1. **创建转赠** - POST `/api/v1/gifts`
2. **获取转赠详情** - GET `/api/v1/gifts/{code}`
3. **验证转赠码** - GET `/api/v1/gifts/validate/{code}`
4. **获取分享链接** - GET `/api/v1/gifts/{code}/share-url`

#### 🔒 权限和配额测试
5. **检查转赠权限** - GET `/api/v1/gifts/check/{gooseId}`
6. **获取剩余次数** - GET `/api/v1/gifts/quota/remaining`
7. **无效参数测试** - 各种错误场景

#### 📥 接收功能测试
8. **接收转赠** - POST `/api/v1/gifts/{code}/receive`
9. **重复接收测试** - 验证业务规则
10. **权限验证** - 无认证访问测试

#### 📋 查询功能测试
11. **查询发送历史** - GET `/api/v1/gifts/my/sent`
12. **查询接收历史** - GET `/api/v1/gifts/my/received`
13. **转赠统计** - GET `/api/v1/gifts/statistics`

#### ❌ 取消功能测试
14. **取消转赠** - DELETE `/api/v1/gifts/{code}`
15. **取消权限验证** - 各种权限场景

#### 🧪 边界条件测试
16. **参数验证** - 无效参数、空值、超长内容
17. **异常处理** - 不存在的资源、权限错误
18. **性能测试** - 批量操作测试

### 测试数据准备

脚本会自动：
1. 创建测试用户账户
2. 获取认证Token
3. 准备测试用的小鹅数据
4. 执行完整的转赠流程测试

## 📈 预期测试结果

### 成功标准
- **总测试用例**: 约30-35个
- **成功率**: ≥95% 
- **响应时间**: <200ms
- **功能覆盖**: 11个API接口全覆盖

### 测试输出示例
```bash
🎁 小鹅养成APP - 转赠模块API测试
==================================
✅ 应用运行正常

=== 准备测试用户 ===
注册发送者用户 ... ✅ 成功
注册接收者用户 ... ✅ 成功
发送者登录 ... ✅ 成功
    发送者Token: eyJ0eXAiOiJKV1QiLCJh...
接收者登录 ... ✅ 成功
    接收者Token: eyJ0eXAiOiJKV1QiLCJh...

=== 转赠模块核心功能测试 ===
测试 创建转赠 ... ✅ 通过 (200)
    转赠码: GIFT20250106210123A1B2
测试 获取转赠详情 ... ✅ 通过 (200)
测试 验证转赠码格式 ... ✅ 通过 (200)
测试 获取分享链接 ... ✅ 通过 (200)

=== 转赠权限和配额测试 ===
测试 检查转赠权限 ... ✅ 通过 (200)
测试 获取剩余转赠次数 ... ✅ 通过 (200)
测试 转赠不存在的小鹅 ... ✅ 通过 (404)
测试 无认证创建转赠 ... ✅ 通过 (401)

... (更多测试结果)

==================================
🎁 转赠模块API测试结果:
==================================
总测试数: 32
通过测试: 31
失败测试: 1
成功率: 97%

📊 测试覆盖情况:
✅ 转赠创建功能
✅ 转赠接收功能
✅ 转赠取消功能
✅ 转赠查询功能
✅ 权限验证功能
✅ 参数验证功能
✅ 异常处理功能
✅ 边界条件测试

🎉 转赠模块API测试结果优秀！
✅ 转赠模块已准备好进入生产环境
```

## 🔧 故障排除

### 常见问题

#### 1. 应用启动失败
```bash
# 检查端口占用
lsof -i :8080

# 检查数据库连接
# 确保MySQL运行在localhost:3306
```

#### 2. 认证失败
```bash
# 检查管理员账户是否存在
curl -X POST -H "Content-Type: application/json" \
  -d '{"username":"superAdmin","password":"admin123"}' \
  http://localhost:8080/api/v1/auth/login
```

#### 3. 测试数据问题
```bash
# 清理测试用户（如果需要）
# 手动删除数据库中的测试用户数据
```

#### 4. jq命令不存在
```bash
# macOS
brew install jq

# Ubuntu/Debian
sudo apt-get install jq

# CentOS/RHEL
sudo yum install jq
```

### 调试模式

如需详细调试信息，可以修改脚本中的curl命令：
```bash
# 在脚本中添加 -v 参数查看详细请求信息
curl -v -s -w '%{http_code}' ...
```

## 📝 测试报告

测试完成后，脚本会生成：
- 详细的测试执行日志
- 成功率统计
- 失败用例分析
- 部署建议

## 🎯 下一步

根据测试结果：

### 如果成功率 ≥95%
- ✅ 转赠模块可以部署到生产环境
- 建议进行负载测试
- 可以开始前端集成

### 如果成功率 85-94%
- ⚠️ 修复失败的测试用例
- 重新运行测试验证
- 分析失败原因

### 如果成功率 <85%
- ❌ 需要深入调试和修复
- 检查代码逻辑和配置
- 不建议部署到生产环境

## 📞 技术支持

如遇到问题，请：
1. 检查应用日志：`tail -f goose-app-backend/logs/goose-app.log`
2. 查看测试脚本输出的详细错误信息
3. 确认数据库和应用配置正确
4. 联系开发团队获取支持

---

**文档版本**: v1.0  
**创建时间**: 2025-01-06  
**适用版本**: 转赠模块 v1.0.0
