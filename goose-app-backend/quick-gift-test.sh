#!/bin/bash

# 🎁 转赠模块快速测试脚本
# 快速验证转赠模块核心功能是否正常

echo "🎁 转赠模块快速功能测试"
echo "======================"

BASE_URL="http://localhost:8080"

# 颜色定义
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 检查应用状态
echo -n "检查应用状态 ... "
if curl -s "$BASE_URL/api/v1/shop/geese/breeds" >/dev/null; then
    echo -e "${GREEN}✅ 运行正常${NC}"
else
    echo -e "${RED}❌ 应用未运行${NC}"
    echo "请先启动应用: mvn spring-boot:run"
    exit 1
fi

# 管理员登录获取Token
echo -n "管理员登录 ... "
ADMIN_LOGIN_DATA='{"username":"superAdmin","password":"admin123"}'
ADMIN_LOGIN_RESPONSE=$(curl -s -X POST -H "Content-Type: application/json" -d "$ADMIN_LOGIN_DATA" "${BASE_URL}/api/v1/auth/login")

if [[ "$ADMIN_LOGIN_RESPONSE" == *"登录成功"* ]]; then
    echo -e "${GREEN}✅ 成功${NC}"
    if command -v jq >/dev/null 2>&1; then
        ADMIN_TOKEN=$(echo "$ADMIN_LOGIN_RESPONSE" | jq -r '.data.token // empty')
    else
        # 简单提取token（如果没有jq）
        ADMIN_TOKEN=$(echo "$ADMIN_LOGIN_RESPONSE" | grep -o '"token":"[^"]*"' | cut -d'"' -f4)
    fi
    echo "    Token: ${ADMIN_TOKEN:0:20}..."
else
    echo -e "${RED}❌ 失败${NC}"
    echo "无法获取管理员Token，请检查管理员账户"
    exit 1
fi

echo ""
echo "开始核心功能测试..."
echo ""

# 测试1: 获取剩余转赠次数
echo -n "1. 获取剩余转赠次数 ... "
QUOTA_RESPONSE=$(curl -s -w '%{http_code}' -H "satoken: Bearer $ADMIN_TOKEN" "${BASE_URL}/api/v1/gifts/quota/remaining")
QUOTA_STATUS="${QUOTA_RESPONSE: -3}"
if [ "$QUOTA_STATUS" = "200" ]; then
    echo -e "${GREEN}✅ 通过${NC}"
else
    echo -e "${RED}❌ 失败 ($QUOTA_STATUS)${NC}"
fi

# 测试2: 检查转赠权限
echo -n "2. 检查转赠权限 ... "
PERMISSION_RESPONSE=$(curl -s -w '%{http_code}' -H "satoken: Bearer $ADMIN_TOKEN" "${BASE_URL}/api/v1/gifts/check/9")
PERMISSION_STATUS="${PERMISSION_RESPONSE: -3}"
if [ "$PERMISSION_STATUS" = "200" ]; then
    echo -e "${GREEN}✅ 通过${NC}"
else
    echo -e "${RED}❌ 失败 ($PERMISSION_STATUS)${NC}"
fi

# 测试3: 验证转赠码格式
echo -n "3. 验证转赠码格式 ... "
VALIDATE_RESPONSE=$(curl -s -w '%{http_code}' "${BASE_URL}/api/v1/gifts/validate/GIFT20241219143025TEST")
VALIDATE_STATUS="${VALIDATE_RESPONSE: -3}"
if [ "$VALIDATE_STATUS" = "200" ]; then
    echo -e "${GREEN}✅ 通过${NC}"
else
    echo -e "${RED}❌ 失败 ($VALIDATE_STATUS)${NC}"
fi

# 测试4: 获取分享链接
echo -n "4. 获取分享链接 ... "
SHARE_RESPONSE=$(curl -s -w '%{http_code}' "${BASE_URL}/api/v1/gifts/GIFT20241219143025TEST/share-url")
SHARE_STATUS="${SHARE_RESPONSE: -3}"
if [ "$SHARE_STATUS" = "200" ]; then
    echo -e "${GREEN}✅ 通过${NC}"
else
    echo -e "${RED}❌ 失败 ($SHARE_STATUS)${NC}"
fi

# 测试5: 创建转赠
echo -n "5. 创建转赠 ... "
CREATE_DATA='{"gooseId":9,"message":"快速测试转赠","expiresHours":72}'
CREATE_RESPONSE=$(curl -s -w '%{http_code}' -X POST -H "Content-Type: application/json" -H "satoken: Bearer $ADMIN_TOKEN" -d "$CREATE_DATA" "${BASE_URL}/api/v1/gifts")
CREATE_STATUS="${CREATE_RESPONSE: -3}"
CREATE_BODY="${CREATE_RESPONSE%???}"

if [ "$CREATE_STATUS" = "200" ]; then
    echo -e "${GREEN}✅ 通过${NC}"

    # 提取转赠码
    if command -v jq >/dev/null 2>&1; then
        GIFT_CODE=$(echo "$CREATE_BODY" | jq -r '.data.giftCode // empty')
    else
        GIFT_CODE=$(echo "$CREATE_BODY" | grep -o '"giftCode":"[^"]*"' | cut -d'"' -f4)
    fi

    if [ ! -z "$GIFT_CODE" ] && [ "$GIFT_CODE" != "null" ]; then
        echo "    转赠码: $GIFT_CODE"

        # 测试6: 获取转赠详情
        echo -n "6. 获取转赠详情 ... "
        DETAIL_RESPONSE=$(curl -s -w '%{http_code}' "${BASE_URL}/api/v1/gifts/$GIFT_CODE")
        DETAIL_STATUS="${DETAIL_RESPONSE: -3}"
        if [ "$DETAIL_STATUS" = "200" ]; then
            echo -e "${GREEN}✅ 通过${NC}"
        else
            echo -e "${RED}❌ 失败 ($DETAIL_STATUS)${NC}"
        fi

        # 测试7: 取消转赠
        echo -n "7. 取消转赠 ... "
        CANCEL_RESPONSE=$(curl -s -w '%{http_code}' -X DELETE -H "satoken: Bearer $ADMIN_TOKEN" "${BASE_URL}/api/v1/gifts/$GIFT_CODE")
        CANCEL_STATUS="${CANCEL_RESPONSE: -3}"
        if [ "$CANCEL_STATUS" = "200" ]; then
            echo -e "${GREEN}✅ 通过${NC}"
        else
            echo -e "${RED}❌ 失败 ($CANCEL_STATUS)${NC}"
        fi
    else
        echo "    ⚠️ 无法提取转赠码，跳过后续测试"
    fi
else
    echo -e "${RED}❌ 失败 ($CREATE_STATUS)${NC}"
    echo "    响应: ${CREATE_BODY:0:100}..."
fi

# 测试8: 查询发送的转赠
echo -n "8. 查询发送的转赠 ... "
SENT_RESPONSE=$(curl -s -w '%{http_code}' -H "satoken: Bearer $ADMIN_TOKEN" "${BASE_URL}/api/v1/gifts/my/sent?page=0&size=5")
SENT_STATUS="${SENT_RESPONSE: -3}"
if [ "$SENT_STATUS" = "200" ]; then
    echo -e "${GREEN}✅ 通过${NC}"
else
    echo -e "${RED}❌ 失败 ($SENT_STATUS)${NC}"
fi

# 测试9: 查询转赠统计
echo -n "9. 查询转赠统计 ... "
STATS_RESPONSE=$(curl -s -w '%{http_code}' -H "satoken: Bearer $ADMIN_TOKEN" "${BASE_URL}/api/v1/gifts/statistics")
STATS_STATUS="${STATS_RESPONSE: -3}"
if [ "$STATS_STATUS" = "200" ]; then
    echo -e "${GREEN}✅ 通过${NC}"
else
    echo -e "${RED}❌ 失败 ($STATS_STATUS)${NC}"
fi

# 测试10: 权限验证 - 无Token访问
echo -n "10. 权限验证测试 ... "
AUTH_RESPONSE=$(curl -s -w '%{http_code}' "${BASE_URL}/api/v1/gifts/my/sent")
AUTH_STATUS="${AUTH_RESPONSE: -3}"
if [ "$AUTH_STATUS" = "401" ]; then
    echo -e "${GREEN}✅ 通过${NC}"
else
    echo -e "${RED}❌ 失败 (期望401，实际$AUTH_STATUS)${NC}"
fi

echo ""
echo "======================"
echo "🎁 快速测试完成"
echo "======================"

# 统计结果
TOTAL_TESTS=10
PASSED_TESTS=0

# 重新计算通过的测试（简化版本）
if [ "$QUOTA_STATUS" = "200" ]; then ((PASSED_TESTS++)); fi
if [ "$PERMISSION_STATUS" = "200" ]; then ((PASSED_TESTS++)); fi
if [ "$VALIDATE_STATUS" = "200" ]; then ((PASSED_TESTS++)); fi
if [ "$SHARE_STATUS" = "200" ]; then ((PASSED_TESTS++)); fi
if [ "$CREATE_STATUS" = "200" ]; then ((PASSED_TESTS++)); fi
if [ ! -z "$GIFT_CODE" ] && [ "$DETAIL_STATUS" = "200" ]; then ((PASSED_TESTS++)); fi
if [ ! -z "$GIFT_CODE" ] && [ "$CANCEL_STATUS" = "200" ]; then ((PASSED_TESTS++)); fi
if [ "$SENT_STATUS" = "200" ]; then ((PASSED_TESTS++)); fi
if [ "$STATS_STATUS" = "200" ]; then ((PASSED_TESTS++)); fi
if [ "$AUTH_STATUS" = "401" ]; then ((PASSED_TESTS++)); fi

SUCCESS_RATE=$((PASSED_TESTS * 100 / TOTAL_TESTS))

echo "测试结果: $PASSED_TESTS/$TOTAL_TESTS 通过"
echo "成功率: ${SUCCESS_RATE}%"

if [ $SUCCESS_RATE -ge 90 ]; then
    echo -e "${GREEN}🎉 转赠模块核心功能正常！${NC}"
    echo -e "${GREEN}✅ 建议运行完整测试: ./gift-api-test.sh${NC}"
    exit 0
elif [ $SUCCESS_RATE -ge 70 ]; then
    echo -e "${YELLOW}⚠️ 转赠模块部分功能异常${NC}"
    echo -e "${YELLOW}⚠️ 建议检查失败的测试用例${NC}"
    exit 1
else
    echo -e "${RED}❌ 转赠模块存在严重问题${NC}"
    echo -e "${RED}❌ 请检查应用配置和数据库连接${NC}"
    exit 1
fi
