#!/bin/bash

# ======================================================
# 小鹅养成APP - 认证模块全面API测试脚本
# Java后端开发专家 精心设计
# ======================================================

# 颜色配置
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 服务器配置
BASE_URL="http://localhost:8080"
API_PREFIX="/api/v1/auth"

# 测试计数器
TOTAL_TESTS=0
PASSED_TESTS=0
FAILED_TESTS=0

# 全局变量存储token等信息
ACCESS_TOKEN=""
USER_ID=""

# 测试数据
TEST_USER_USERNAME="testuser_$(date +%s)"
TEST_USER_EMAIL="test_$(date +%s)@example.com"
TEST_USER_PASSWORD="password123"
TEST_USER_NICKNAME="测试用户"

# 函数：打印标题
print_title() {
    echo -e "\n${BLUE}============================================${NC}"
    echo -e "${BLUE} $1 ${NC}"
    echo -e "${BLUE}============================================${NC}\n"
}

# 函数：打印测试用例
print_test_case() {
    echo -e "${YELLOW}🧪 测试用例: $1${NC}"
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
}

# 函数：打印成功结果
print_success() {
    echo -e "${GREEN}✅ $1${NC}\n"
    PASSED_TESTS=$((PASSED_TESTS + 1))
}

# 函数：打印失败结果
print_error() {
    echo -e "${RED}❌ $1${NC}\n"
    FAILED_TESTS=$((FAILED_TESTS + 1))
}

# 函数：简化的curl执行
test_api() {
    local method="$1"
    local url="$2"
    local data="$3"
    local auth_header="$4"
    local test_name="$5"
    
    echo "📡 请求: $method $BASE_URL$url"
    
    local curl_cmd="curl -s -w '\n%{http_code}'"
    
    if [ "$method" = "POST" ]; then
        curl_cmd="$curl_cmd -X POST"
    fi
    
    if [ -n "$auth_header" ]; then
        curl_cmd="$curl_cmd -H 'Authorization: Bearer $auth_header'"
    fi
    
    if [ -n "$data" ]; then
        curl_cmd="$curl_cmd -H 'Content-Type: application/json' -d '$data'"
    fi
    
    curl_cmd="$curl_cmd '$BASE_URL$url'"
    
    local result=$(eval $curl_cmd)
    local http_code=$(echo "$result" | tail -n1)
    local response_body=$(echo "$result" | head -n -1)
    
    echo "📋 响应: $response_body"
    echo "📊 状态码: $http_code"
    echo
    
    # 导出结果供外部使用
    export LAST_RESPONSE="$response_body"
    export LAST_HTTP_CODE="$http_code"
}

# 函数：检查状态码
check_status() {
    local expected="$1"
    local test_name="$2"
    
    if [ "$LAST_HTTP_CODE" = "$expected" ]; then
        print_success "$test_name - HTTP状态码正确 ($LAST_HTTP_CODE)"
        return 0
    else
        print_error "$test_name - HTTP状态码错误，期望: $expected，实际: $LAST_HTTP_CODE"
        return 1
    fi
}

# 函数：检查响应字段
check_field() {
    local field_path="$1"
    local expected="$2"
    local test_name="$3"
    
    local actual=$(echo "$LAST_RESPONSE" | jq -r "$field_path" 2>/dev/null)
    
    if [ "$actual" = "$expected" ]; then
        print_success "$test_name - 字段验证通过: $field_path = $expected"
        return 0
    else
        print_error "$test_name - 字段验证失败: $field_path，期望: $expected，实际: $actual"
        return 1
    fi
}

# ======================================================
# 测试用例开始
# ======================================================

print_title "小鹅养成APP认证模块API测试开始"

# ======================================================
# 1. 检查用户名可用性测试
# ======================================================
print_title "1. 检查用户名可用性接口测试"

print_test_case "1.1 检查不存在的用户名（应该可用）"
test_api "GET" "$API_PREFIX/check-username?username=$TEST_USER_USERNAME" "" "" "检查用户名可用性"
check_status "200" "检查用户名可用性"
check_field ".code" "200" "检查用户名可用性代码"
check_field ".data" "true" "检查用户名可用性结果"

print_test_case "1.2 检查空用户名参数验证"
test_api "GET" "$API_PREFIX/check-username?username=" "" "" "空用户名验证"
if [ "$LAST_HTTP_CODE" != "200" ]; then
    print_success "空用户名验证 - 正确返回错误状态码"
else
    print_error "空用户名验证 - 应该返回错误状态码"
fi

# ======================================================
# 2. 检查邮箱可用性测试
# ======================================================
print_title "2. 检查邮箱可用性接口测试"

print_test_case "2.1 检查不存在的邮箱（应该可用）"
test_api "GET" "$API_PREFIX/check-email?email=$TEST_USER_EMAIL" "" "" "检查邮箱可用性"
check_status "200" "检查邮箱可用性"
check_field ".code" "200" "检查邮箱可用性代码"
check_field ".data" "true" "检查邮箱可用性结果"

# ======================================================
# 3. 检查手机号可用性测试
# ======================================================
print_title "3. 检查手机号可用性接口测试"

print_test_case "3.1 检查不存在的手机号（应该可用）"
TEST_PHONE="13800$(date +%s | tail -c 7)"
test_api "GET" "$API_PREFIX/check-phone?phone=$TEST_PHONE" "" "" "检查手机号可用性"
check_status "200" "检查手机号可用性"
check_field ".code" "200" "检查手机号可用性代码"
check_field ".data" "true" "检查手机号可用性结果"

# ======================================================
# 4. 用户注册测试
# ======================================================
print_title "4. 用户注册接口测试"

print_test_case "4.1 正常用户注册"
register_data='{"username":"'$TEST_USER_USERNAME'","email":"'$TEST_USER_EMAIL'","password":"'$TEST_USER_PASSWORD'","confirmPassword":"'$TEST_USER_PASSWORD'","nickname":"'$TEST_USER_NICKNAME'"}'

test_api "POST" "$API_PREFIX/register" "$register_data" "" "用户注册"
if check_status "200" "用户注册"; then
    check_field ".code" "200" "用户注册代码"
    check_field ".message" "注册成功" "用户注册消息"
    
    # 提取token
    ACCESS_TOKEN=$(echo "$LAST_RESPONSE" | jq -r '.data.token' 2>/dev/null)
    USER_ID=$(echo "$LAST_RESPONSE" | jq -r '.data.userId' 2>/dev/null)
    
    if [ "$ACCESS_TOKEN" != "null" ] && [ -n "$ACCESS_TOKEN" ]; then
        print_success "用户注册 - 成功获取访问令牌"
    else
        print_error "用户注册 - 未能获取访问令牌"
    fi
fi

print_test_case "4.2 重复用户名注册（应该失败）"
duplicate_data='{"username":"'$TEST_USER_USERNAME'","email":"another'$TEST_USER_EMAIL'","password":"'$TEST_USER_PASSWORD'","confirmPassword":"'$TEST_USER_PASSWORD'","nickname":"另一个测试用户"}'

test_api "POST" "$API_PREFIX/register" "$duplicate_data" "" "重复用户名注册"
if [ "$LAST_HTTP_CODE" = "400" ]; then
    print_success "重复用户名注册 - 正确返回400错误"
    check_field ".code" "6003" "重复用户名注册错误码"
else
    print_error "重复用户名注册 - 应该返回400错误，实际: $LAST_HTTP_CODE"
fi

# ======================================================
# 5. 用户登录测试
# ======================================================
print_title "5. 用户登录接口测试"

print_test_case "5.1 正常用户登录"
login_data='{"username":"'$TEST_USER_USERNAME'","password":"'$TEST_USER_PASSWORD'"}'

test_api "POST" "$API_PREFIX/login" "$login_data" "" "用户登录"
if check_status "200" "用户登录"; then
    check_field ".code" "200" "用户登录代码"
    check_field ".message" "登录成功" "用户登录消息"
    
    # 更新token
    NEW_TOKEN=$(echo "$LAST_RESPONSE" | jq -r '.data.token' 2>/dev/null)
    if [ "$NEW_TOKEN" != "null" ] && [ -n "$NEW_TOKEN" ]; then
        ACCESS_TOKEN="$NEW_TOKEN"
        print_success "用户登录 - 成功更新访问令牌"
    fi
fi

print_test_case "5.2 错误密码登录（应该失败）"
wrong_login_data='{"username":"'$TEST_USER_USERNAME'","password":"wrong_password"}'

test_api "POST" "$API_PREFIX/login" "$wrong_login_data" "" "错误密码登录"
if [ "$LAST_HTTP_CODE" = "400" ]; then
    print_success "错误密码登录 - 正确返回400错误"
    check_field ".code" "6006" "错误密码登录错误码"
else
    print_error "错误密码登录 - 应该返回400错误，实际: $LAST_HTTP_CODE"
fi

# ======================================================
# 6. 获取登录状态测试
# ======================================================
print_title "6. 获取登录状态接口测试"

print_test_case "6.1 未认证状态获取登录状态"
test_api "GET" "$API_PREFIX/status" "" "" "获取登录状态-未认证"
check_status "200" "获取登录状态-未认证"
check_field ".code" "200" "获取登录状态-未认证代码"
check_field ".data.isLogin" "false" "获取登录状态-未认证状态"

print_test_case "6.2 已认证状态获取登录状态"
test_api "GET" "$API_PREFIX/status" "" "$ACCESS_TOKEN" "获取登录状态-已认证"
check_status "200" "获取登录状态-已认证"
check_field ".code" "200" "获取登录状态-已认证代码"

# ======================================================
# 7. 获取当前用户信息测试
# ======================================================
print_title "7. 获取当前用户信息接口测试"

print_test_case "7.1 未认证获取用户信息（应该失败）"
test_api "GET" "$API_PREFIX/me" "" "" "获取用户信息-未认证"
if [ "$LAST_HTTP_CODE" = "401" ]; then
    print_success "获取用户信息-未认证 - 正确返回401错误"
else
    print_error "获取用户信息-未认证 - 应该返回401错误，实际: $LAST_HTTP_CODE"
fi

print_test_case "7.2 已认证获取用户信息"
test_api "GET" "$API_PREFIX/me" "" "$ACCESS_TOKEN" "获取用户信息-已认证"
if check_status "200" "获取用户信息-已认证"; then
    check_field ".code" "200" "获取用户信息-已认证代码"
    check_field ".data.username" "$TEST_USER_USERNAME" "获取用户信息-用户名验证"
fi

# ======================================================
# 8. 刷新Token测试
# ======================================================
print_title "8. 刷新Token接口测试"

print_test_case "8.1 未认证刷新Token（应该失败）"
test_api "POST" "$API_PREFIX/refresh" "" "" "刷新Token-未认证"
if [ "$LAST_HTTP_CODE" = "401" ]; then
    print_success "刷新Token-未认证 - 正确返回401错误"
else
    print_error "刷新Token-未认证 - 应该返回401错误，实际: $LAST_HTTP_CODE"
fi

print_test_case "8.2 已认证刷新Token"
test_api "POST" "$API_PREFIX/refresh" "" "$ACCESS_TOKEN" "刷新Token-已认证"
if check_status "200" "刷新Token-已认证"; then
    check_field ".code" "200" "刷新Token-已认证代码"
    check_field ".message" "Token刷新成功" "刷新Token消息"
fi

# ======================================================
# 9. 修改密码测试
# ======================================================
print_title "9. 修改密码接口测试"

print_test_case "9.1 未认证修改密码（应该失败）"
change_password_data='{"oldPassword":"'$TEST_USER_PASSWORD'","newPassword":"new_password123"}'

test_api "POST" "$API_PREFIX/change-password" "$change_password_data" "" "修改密码-未认证"
if [ "$LAST_HTTP_CODE" = "401" ]; then
    print_success "修改密码-未认证 - 正确返回401错误"
else
    print_error "修改密码-未认证 - 应该返回401错误，实际: $LAST_HTTP_CODE"
fi

print_test_case "9.2 已认证修改密码（正确场景）"
NEW_PASSWORD="new_password123"
correct_change_password_data='{"oldPassword":"'$TEST_USER_PASSWORD'","newPassword":"'$NEW_PASSWORD'"}'

test_api "POST" "$API_PREFIX/change-password" "$correct_change_password_data" "$ACCESS_TOKEN" "修改密码-正确场景"
if check_status "200" "修改密码-正确场景"; then
    check_field ".code" "200" "修改密码-正确场景代码"
    check_field ".message" "密码修改成功" "修改密码消息"
    TEST_USER_PASSWORD="$NEW_PASSWORD"
fi

# ======================================================
# 10. 验证密码修改后的登录
# ======================================================
print_title "10. 验证密码修改后的登录"

print_test_case "10.1 用新密码登录"
new_login_data='{"username":"'$TEST_USER_USERNAME'","password":"'$NEW_PASSWORD'"}'

test_api "POST" "$API_PREFIX/login" "$new_login_data" "" "新密码登录"
if check_status "200" "新密码登录"; then
    check_field ".code" "200" "新密码登录代码"
    check_field ".message" "登录成功" "新密码登录消息"
    
    # 更新token
    ACCESS_TOKEN=$(echo "$LAST_RESPONSE" | jq -r '.data.token' 2>/dev/null)
fi

# ======================================================
# 11. 用户登出测试
# ======================================================
print_title "11. 用户登出接口测试"

print_test_case "11.1 未认证用户登出（应该失败）"
test_api "POST" "$API_PREFIX/logout" "" "" "用户登出-未认证"
if [ "$LAST_HTTP_CODE" = "401" ]; then
    print_success "用户登出-未认证 - 正确返回401错误"
else
    print_error "用户登出-未认证 - 应该返回401错误，实际: $LAST_HTTP_CODE"
fi

print_test_case "11.2 已认证用户登出"
test_api "POST" "$API_PREFIX/logout" "" "$ACCESS_TOKEN" "用户登出-已认证"
if check_status "200" "用户登出-已认证"; then
    check_field ".code" "200" "用户登出-已认证代码"
    check_field ".message" "登出成功" "用户登出消息"
fi

# ======================================================
# 12. 验证登出后的状态
# ======================================================
print_title "12. 验证登出后的状态"

print_test_case "12.1 登出后验证token失效"
test_api "GET" "$API_PREFIX/me" "" "$ACCESS_TOKEN" "登出后获取用户信息"
if [ "$LAST_HTTP_CODE" = "401" ]; then
    print_success "登出后token失效 - 正确返回401错误"
else
    print_error "登出后token失效 - token应该已失效，实际状态码: $LAST_HTTP_CODE"
fi

# ======================================================
# 13. 边界条件和异常场景测试
# ======================================================
print_title "13. 边界条件和异常场景测试"

print_test_case "13.1 测试非JSON格式请求"
result=$(execute_curl "POST" "$API_PREFIX/register" "invalid-json-data" "-H 'Content-Type: application/json'" "非JSON格式请求")
response_body=$(echo "$result" | cut -d'|' -f1)
http_code=$(echo "$result" | cut -d'|' -f2)

if [ "$http_code" -eq 400 ]; then
    print_success "非JSON格式请求 - 正确返回400错误"
else
    print_error "非JSON格式请求 - 应该返回400错误，实际: $http_code"
fi

print_test_case "13.2 测试空请求体"
result=$(execute_curl "POST" "$API_PREFIX/register" "" "" "空请求体")
response_body=$(echo "$result" | cut -d'|' -f1)
http_code=$(echo "$result" | cut -d'|' -f2)

if [ "$http_code" -eq 400 ]; then
    print_success "空请求体 - 正确返回400错误"
else
    print_error "空请求体 - 应该返回400错误，实际: $http_code"
fi

print_test_case "13.3 测试无效的Authorization头"
invalid_auth_header="-H 'Authorization: Bearer invalid_token_12345'"
result=$(execute_curl "GET" "$API_PREFIX/me" "" "$invalid_auth_header" "无效Authorization头")
response_body=$(echo "$result" | cut -d'|' -f1)
http_code=$(echo "$result" | cut -d'|' -f2)

if [ "$http_code" -eq 401 ]; then
    print_success "无效Authorization头 - 正确返回401错误"
else
    print_error "无效Authorization头 - 应该返回401错误，实际: $http_code"
fi

# ======================================================
# 最终统计和报告
# ======================================================
print_title "测试完成 - 统计报告"

echo -e "${BLUE}📊 测试统计:${NC}"
echo -e "   总测试用例: ${TOTAL_TESTS}"
echo -e "   ${GREEN}✅ 通过: ${PASSED_TESTS}${NC}"
echo -e "   ${RED}❌ 失败: ${FAILED_TESTS}${NC}"

PASS_RATE=$((PASSED_TESTS * 100 / TOTAL_TESTS))
echo -e "   通过率: ${PASS_RATE}%"

if [ $FAILED_TESTS -eq 0 ]; then
    echo -e "\n${GREEN}🎉 所有测试用例都通过了！认证模块API工作正常。${NC}"
    exit 0
else
    echo -e "\n${YELLOW}⚠️  有 $FAILED_TESTS 个测试用例失败，请检查相关问题。${NC}"
    exit 1
fi 