#!/bin/bash

# 小鹅养成APP - C端用户两步注册流程测试脚本
# 测试新的分步骤注册流程

set -e

# 配置
API_BASE_URL="http://localhost:8080/api/v1/auth"
TEST_PHONE="13900199999"
CONTENT_TYPE="Content-Type: application/json"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 测试结果统计
TOTAL_TESTS=0
PASSED_TESTS=0
FAILED_TESTS=0

# 存储临时Token
TEMP_TOKEN=""

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
    ((PASSED_TESTS++))
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
    ((FAILED_TESTS++))
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

# 测试函数
run_test() {
    local test_name="$1"
    local curl_command="$2"
    local expected_pattern="$3"
    
    ((TOTAL_TESTS++))
    log_info "执行测试: $test_name"
    
    # 执行curl命令并捕获响应
    response=$(eval "$curl_command" 2>/dev/null)
    http_code=$(eval "$curl_command -w '%{http_code}'" -o /dev/null 2>/dev/null)
    
    echo "   请求: $curl_command"
    echo "   响应码: $http_code"
    echo "   响应体: $response"
    
    # 检查响应
    if [[ $response =~ $expected_pattern ]]; then
        log_success "$test_name - 通过"
        
        # 如果是创建账户成功，提取临时Token
        if [[ $test_name == *"创建账户"* && $response =~ \"tempToken\":\"([^\"]+)\" ]]; then
            TEMP_TOKEN="${BASH_REMATCH[1]}"
            log_info "提取到临时Token: ${TEMP_TOKEN:0:20}..."
        fi
    else
        log_error "$test_name - 失败"
        echo "   期望包含: $expected_pattern"
    fi
    
    echo "----------------------------------------"
}

# 开始测试
echo "🚀 开始执行小鹅养成APP C端用户两步注册流程测试"
echo "========================================"

# 测试1: 发送注册验证码
log_info "第一步：发送注册验证码"
run_test "发送注册验证码" \
    "curl -s -X POST '$API_BASE_URL/send-sms-code' \
    -H '$CONTENT_TYPE' \
    -d '{
        \"phone\": \"$TEST_PHONE\",
        \"codeType\": 2
    }'" \
    '"success":true'

# 等待1秒
sleep 1

# 获取验证码
log_info "获取数据库中的验证码..."
VERIFICATION_CODE=$(docker exec -i b5a3b2b61008 mysql -u root -p123456 -e "USE goose_app; SELECT code FROM sms_verification_codes WHERE phone = '$TEST_PHONE' AND code_type = 2 ORDER BY created_at DESC LIMIT 1;" 2>/dev/null | tail -n 1)
log_info "获取到验证码: $VERIFICATION_CODE"

# 测试2: 创建C端账户（第一步）
log_info "第二步：创建C端账户"
run_test "创建C端账户" \
    "curl -s -X POST '$API_BASE_URL/create-mobile-account' \
    -H '$CONTENT_TYPE' \
    -d '{
        \"phone\": \"$TEST_PHONE\",
        \"code\": \"$VERIFICATION_CODE\",
        \"nickname\": \"测试小鹅用户\",
        \"deviceInfo\": \"iPhone 15 Pro\"
    }'" \
    '"tempToken":'

# 测试3: 查询注册状态
if [ ! -z "$TEMP_TOKEN" ]; then
    log_info "第三步：查询注册状态"
    run_test "查询注册状态" \
        "curl -s -X GET '$API_BASE_URL/registration-status' \
        -H '$CONTENT_TYPE' \
        -H 'Temp-Token: $TEMP_TOKEN'" \
        '"needSetPassword":true'
fi

# 测试4: 设置密码（第二步）
if [ ! -z "$TEMP_TOKEN" ]; then
    log_info "第四步：设置登录密码"
    run_test "设置登录密码" \
        "curl -s -X POST '$API_BASE_URL/set-mobile-password' \
        -H '$CONTENT_TYPE' \
        -H 'Temp-Token: $TEMP_TOKEN' \
        -d '{
            \"password\": \"Password123\",
            \"confirmPassword\": \"Password123\",
            \"deviceInfo\": \"iPhone 15 Pro\"
        }'" \
        '"token":'
fi

# 测试5: 验证重复创建账户
log_info "第五步：验证重复创建账户"

# 先发送验证码
curl -s -X POST "$API_BASE_URL/send-sms-code" \
    -H "$CONTENT_TYPE" \
    -d "{
        \"phone\": \"$TEST_PHONE\",
        \"codeType\": 2
    }" > /dev/null

sleep 1

# 获取新验证码
NEW_CODE=$(docker exec -i b5a3b2b61008 mysql -u root -p123456 -e "USE goose_app; SELECT code FROM sms_verification_codes WHERE phone = '$TEST_PHONE' AND code_type = 2 ORDER BY created_at DESC LIMIT 1;" 2>/dev/null | tail -n 1)

run_test "重复创建账户检查" \
    "curl -s -X POST '$API_BASE_URL/create-mobile-account' \
    -H '$CONTENT_TYPE' \
    -d '{
        \"phone\": \"$TEST_PHONE\",
        \"code\": \"$NEW_CODE\",
        \"nickname\": \"重复用户\"
    }'" \
    '"success":false'

# 测试6: 测试手机验证码登录
log_info "第六步：测试手机验证码登录"

# 发送登录验证码
curl -s -X POST "$API_BASE_URL/send-sms-code" \
    -H "$CONTENT_TYPE" \
    -d "{
        \"phone\": \"$TEST_PHONE\",
        \"codeType\": 1
    }" > /dev/null

sleep 1

# 获取登录验证码
LOGIN_CODE=$(docker exec -i b5a3b2b61008 mysql -u root -p123456 -e "USE goose_app; SELECT code FROM sms_verification_codes WHERE phone = '$TEST_PHONE' AND code_type = 1 ORDER BY created_at DESC LIMIT 1;" 2>/dev/null | tail -n 1)

run_test "手机验证码登录" \
    "curl -s -X POST '$API_BASE_URL/login-by-phone' \
    -H '$CONTENT_TYPE' \
    -d '{
        \"phone\": \"$TEST_PHONE\",
        \"code\": \"$LOGIN_CODE\",
        \"deviceInfo\": \"iPhone 15 Pro\"
    }'" \
    '"token":'

# 输出测试结果
echo "========================================"
echo "📊 C端用户两步注册流程测试结果"
echo "========================================"
echo "总测试数: $TOTAL_TESTS"
echo -e "通过数量: ${GREEN}$PASSED_TESTS${NC}"
echo -e "失败数量: ${RED}$FAILED_TESTS${NC}"

if [ $FAILED_TESTS -eq 0 ]; then
    echo -e "${GREEN}🎉 所有测试通过！${NC}"
    success_rate=100
else
    success_rate=$((PASSED_TESTS * 100 / TOTAL_TESTS))
    echo -e "${YELLOW}⚠️  部分测试失败${NC}"
fi

echo "成功率: ${success_rate}%"

# 数据库验证
echo ""
echo "========================================"
echo "📋 数据库验证"
echo "========================================"

log_info "验证用户创建结果..."

# 检查用户是否创建成功
echo "用户信息:"
docker exec -i b5a3b2b61008 mysql -u root -p123456 -e "USE goose_app; SELECT id, username, phone, nickname, user_type, registration_status FROM users WHERE phone = '$TEST_PHONE';" 2>/dev/null

# 检查验证码记录
echo "验证码记录数:"
docker exec -i b5a3b2b61008 mysql -u root -p123456 -e "USE goose_app; SELECT COUNT(*) as sms_count FROM sms_verification_codes WHERE phone = '$TEST_PHONE';" 2>/dev/null | tail -n 1

# 检查登录日志
echo "登录日志记录数:"
docker exec -i b5a3b2b61008 mysql -u root -p123456 -e "USE goose_app; SELECT COUNT(*) as login_count FROM user_login_logs WHERE user_id = (SELECT id FROM users WHERE phone = '$TEST_PHONE');" 2>/dev/null | tail -n 1

echo ""
echo "🏁 C端用户两步注册流程测试完成！"

# 返回适当的退出码
if [ $FAILED_TESTS -eq 0 ]; then
    exit 0
else
    exit 1
fi
