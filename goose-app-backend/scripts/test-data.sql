-- 🦆 小鹅养成APP - 测试数据初始化脚本
-- 用于重构后验证测试

-- 清理现有测试数据 (可选)
-- DELETE FROM goose_purchases WHERE user_id IN (SELECT id FROM users WHERE username LIKE 'test%' OR username LIKE 'user%' OR username = 'admin');
-- DELETE FROM user_roles WHERE user_id IN (SELECT id FROM users WHERE username LIKE 'test%' OR username LIKE 'user%' OR username = 'admin');
-- DELETE FROM geese WHERE user_id IN (SELECT id FROM users WHERE username LIKE 'test%' OR username LIKE 'user%' OR username = 'admin') OR user_id IS NULL;
-- DELETE FROM users WHERE username LIKE 'test%' OR username LIKE 'user%' OR username = 'admin';

-- 插入测试用户 (密码: admin123 和 user123 的BCrypt哈希)
-- 注意: 这里使用明文密码，实际运行时会通过API注册来生成正确的加密密码
INSERT IGNORE INTO users (username, email, password, nickname, phone, created_at, updated_at) VALUES
-- 管理员用户 (密码: admin123)
('admin', '<EMAIL>', '$2a$10$N9qo8uLOickgx2ZMRZoMye6VK/7UShbtfA8p7v7jHZ8oF8ECSM8lC', '系统管理员', '13800000001', NOW(), NOW()),
-- 普通用户1 (密码: user123)  
('user1', '<EMAIL>', '$2a$10$N9qo8uLOickgx2ZMRZoMye6VK/7UShbtfA8p7v7jHZ8oF8ECSM8lC', '测试用户1', '13800000002', NOW(), NOW()),
-- 普通用户2 (密码: user123)
('user2', '<EMAIL>', '$2a$10$N9qo8uLOickgx2ZMRZoMye6VK/7UShbtfA8p7v7jHZ8oF8ECSM8lC', '测试用户2', '13800000003', NOW(), NOW());

-- 分配管理员角色
INSERT IGNORE INTO user_roles (user_id, role_name, created_at) 
SELECT id, 'ADMIN', NOW() FROM users WHERE username = 'admin';

-- 插入测试小鹅到商品池 (等待售卖状态)
INSERT IGNORE INTO geese (name, breed, gender, health, happiness, hunger, thirst, ownership_status, 
                         birth_date, created_at, updated_at, user_id, purchase_time, purchase_price) VALUES
-- 待售小鹅
('小白', '白鹅', 1, 100, 80, 20, 30, 0, '2024-01-01', NOW(), NOW(), NULL, NULL, 88.00),
('小黑', '黑鹅', 0, 95, 75, 25, 35, 0, '2024-01-02', NOW(), NOW(), NULL, NULL, 88.00),
('小灰', '灰鹅', 1, 98, 85, 15, 20, 0, '2024-01-03', NOW(), NOW(), NULL, NULL, 88.00),
('小花', '花鹅', 0, 92, 78, 30, 25, 0, '2024-01-04', NOW(), NOW(), NULL, NULL, 88.00),
('小雪', '雪鹅', 1, 88, 90, 10, 15, 0, '2024-01-05', NOW(), NOW(), NULL, NULL, 88.00);

-- 插入一些已售出的小鹅 (用于测试数据完整性)
INSERT IGNORE INTO geese (name, breed, gender, health, happiness, hunger, thirst, ownership_status, 
                         birth_date, created_at, updated_at, user_id, purchase_time, purchase_price) VALUES
-- 已归属给user1的小鹅
('小红', '红鹅', 1, 85, 95, 40, 45, 1, '2023-12-01', NOW(), NOW(), 
 (SELECT id FROM users WHERE username = 'user1'), '2024-01-10 10:30:00', 88.00);

-- 插入对应的购买记录
INSERT IGNORE INTO goose_purchases (user_id, goose_id, purchase_price, payment_method, purchase_time, created_at) 
SELECT 
    u.id,
    g.id,
    88.00,
    'WECHAT_PAY',
    '2024-01-10 10:30:00',
    NOW()
FROM users u, geese g 
WHERE u.username = 'user1' 
  AND g.name = '小红' 
  AND g.user_id = u.id;

-- 验证数据插入
SELECT '测试数据插入完成' as status;

-- 显示插入的数据统计
SELECT 
    '用户数据' as category,
    COUNT(*) as count,
    GROUP_CONCAT(username) as details
FROM users 
WHERE username IN ('admin', 'user1', 'user2')

UNION ALL

SELECT 
    '角色数据' as category,
    COUNT(*) as count,
    GROUP_CONCAT(CONCAT(u.username, ':', ur.role_name)) as details
FROM user_roles ur
JOIN users u ON ur.user_id = u.id
WHERE u.username IN ('admin', 'user1', 'user2')

UNION ALL

SELECT 
    '小鹅数据(待售)' as category,
    COUNT(*) as count,
    GROUP_CONCAT(name) as details
FROM geese 
WHERE ownership_status = 0

UNION ALL

SELECT 
    '小鹅数据(已归属)' as category,
    COUNT(*) as count,
    GROUP_CONCAT(CONCAT(name, '(', u.username, ')')) as details
FROM geese g
JOIN users u ON g.user_id = u.id
WHERE g.ownership_status = 1 AND u.username IN ('admin', 'user1', 'user2')

UNION ALL

SELECT 
    '购买记录' as category,
    COUNT(*) as count,
    GROUP_CONCAT(CONCAT(u.username, '->', g.name)) as details
FROM goose_purchases gp
JOIN users u ON gp.user_id = u.id
JOIN geese g ON gp.goose_id = g.id
WHERE u.username IN ('admin', 'user1', 'user2');

-- 显示详细的小鹅状态
SELECT 
    g.id,
    g.name,
    g.breed,
    g.ownership_status,
    CASE g.ownership_status 
        WHEN 0 THEN '待售'
        WHEN 1 THEN '已归属'
        ELSE '未知'
    END as status_desc,
    u.username as owner,
    g.purchase_time,
    g.purchase_price
FROM geese g
LEFT JOIN users u ON g.user_id = u.id
ORDER BY g.ownership_status, g.id; 