-- =====================================================
-- 小鹅归属权重构 - 数据库迁移脚本
-- 版本: 001
-- 创建时间: 2024-12-19
-- 描述: 实现小鹅归属权业务逻辑重构的数据库结构调整
-- =====================================================

-- 开始事务
START TRANSACTION;

-- =====================================================
-- 1. 新增用户角色表 (user_roles)
-- =====================================================

CREATE TABLE user_roles (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '角色ID',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    role_name VARCHAR(50) NOT NULL COMMENT '角色名称',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',

    INDEX idx_user_id (user_id),
    INDEX idx_role_name (role_name)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户角色表';

-- =====================================================
-- 2. 调整小鹅表结构 (geese)
-- =====================================================

-- 添加归属状态字段
ALTER TABLE geese
ADD COLUMN ownership_status TINYINT DEFAULT 0 COMMENT '归属状态：0-待售，1-已归属';

-- 添加购买时间字段
ALTER TABLE geese
ADD COLUMN purchase_time TIMESTAMP NULL COMMENT '购买时间';

-- 修改user_id字段为可空（允许待售状态）
ALTER TABLE geese
MODIFY COLUMN user_id BIGINT NULL COMMENT '所属用户ID（可为空）';

-- 添加索引优化查询性能
ALTER TABLE geese
ADD INDEX idx_ownership_status (ownership_status);

-- =====================================================
-- 3. 新增购买记录表 (goose_purchases)
-- =====================================================

CREATE TABLE goose_purchases (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '购买记录ID',
    user_id BIGINT NOT NULL COMMENT '购买用户ID',
    goose_id BIGINT NOT NULL COMMENT '购买的小鹅ID',
    product_id BIGINT NULL COMMENT '关联商品ID',
    purchase_price DECIMAL(10,2) NOT NULL COMMENT '购买价格',
    purchase_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '购买时间',
    status TINYINT DEFAULT 1 COMMENT '状态：1-成功，0-失败',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',

    INDEX idx_user_id (user_id),
    INDEX idx_goose_id (goose_id),
    INDEX idx_purchase_time (purchase_time),
    INDEX idx_status (status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='小鹅购买记录表';

-- =====================================================
-- 4. 数据迁移：更新现有小鹅为已归属状态
-- =====================================================

-- 更新现有小鹅的归属状态和购买时间
UPDATE geese SET
    ownership_status = 1,
    purchase_time = created_at
WHERE user_id IS NOT NULL;

-- =====================================================
-- 5. 插入默认管理员角色
-- =====================================================

-- 为admin用户（ID=2）添加管理员角色
INSERT INTO user_roles (user_id, role_name) VALUES (2, 'ADMIN');

-- 为其他用户添加普通用户角色
INSERT INTO user_roles (user_id, role_name) 
SELECT id, 'USER' FROM users WHERE id != 2;

-- =====================================================
-- 6. 验证数据完整性
-- =====================================================

-- 检查用户角色表数据
SELECT 'user_roles表记录数:' as check_item, COUNT(*) as count FROM user_roles;

-- 检查小鹅表新字段
SELECT 'geese表已归属小鹅数:' as check_item, COUNT(*) as count FROM geese WHERE ownership_status = 1;
SELECT 'geese表待售小鹅数:' as check_item, COUNT(*) as count FROM geese WHERE ownership_status = 0;

-- 检查购买记录表
SELECT 'goose_purchases表记录数:' as check_item, COUNT(*) as count FROM goose_purchases;

-- 提交事务
COMMIT;

-- =====================================================
-- 迁移完成提示
-- =====================================================

SELECT '数据库迁移完成！' as message, NOW() as completion_time;
