-- =====================================================
-- 小鹅归属权重构 - 数据库回滚脚本
-- 版本: 001_rollback
-- 创建时间: 2024-12-19
-- 描述: 回滚小鹅归属权重构的数据库结构变更
-- =====================================================

-- 开始事务
START TRANSACTION;

-- =====================================================
-- 1. 备份当前数据（可选）
-- =====================================================

-- 创建备份表（如果需要）
-- CREATE TABLE user_roles_backup AS SELECT * FROM user_roles;
-- CREATE TABLE goose_purchases_backup AS SELECT * FROM goose_purchases;

-- =====================================================
-- 2. 删除新增的表
-- =====================================================

-- 删除购买记录表
DROP TABLE IF EXISTS goose_purchases;

-- 删除用户角色表
DROP TABLE IF EXISTS user_roles;

-- =====================================================
-- 3. 回滚小鹅表结构变更
-- =====================================================

-- 删除新增的索引
ALTER TABLE geese DROP INDEX IF EXISTS idx_ownership_status;

-- 删除新增的字段
ALTER TABLE geese DROP COLUMN IF EXISTS ownership_status;
ALTER TABLE geese DROP COLUMN IF EXISTS purchase_time;

-- 恢复user_id字段为NOT NULL
ALTER TABLE geese MODIFY COLUMN user_id BIGINT NOT NULL COMMENT '所属用户ID';

-- =====================================================
-- 4. 验证回滚结果
-- =====================================================

-- 检查表是否已删除
SELECT 
    CASE 
        WHEN COUNT(*) = 0 THEN '✅ user_roles表已删除'
        ELSE '❌ user_roles表仍存在'
    END as rollback_status
FROM information_schema.tables 
WHERE table_schema = DATABASE() AND table_name = 'user_roles';

SELECT 
    CASE 
        WHEN COUNT(*) = 0 THEN '✅ goose_purchases表已删除'
        ELSE '❌ goose_purchases表仍存在'
    END as rollback_status
FROM information_schema.tables 
WHERE table_schema = DATABASE() AND table_name = 'goose_purchases';

-- 检查geese表字段是否已删除
SELECT 
    CASE 
        WHEN COUNT(*) = 0 THEN '✅ ownership_status字段已删除'
        ELSE '❌ ownership_status字段仍存在'
    END as rollback_status
FROM information_schema.columns 
WHERE table_schema = DATABASE() 
  AND table_name = 'geese' 
  AND column_name = 'ownership_status';

SELECT 
    CASE 
        WHEN COUNT(*) = 0 THEN '✅ purchase_time字段已删除'
        ELSE '❌ purchase_time字段仍存在'
    END as rollback_status
FROM information_schema.columns 
WHERE table_schema = DATABASE() 
  AND table_name = 'geese' 
  AND column_name = 'purchase_time';

-- 提交事务
COMMIT;

-- =====================================================
-- 回滚完成提示
-- =====================================================

SELECT '数据库回滚完成！' as message, NOW() as completion_time;
