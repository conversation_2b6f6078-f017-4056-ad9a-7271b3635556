#!/bin/bash

# 小鹅养成APP - 移动端认证API测试脚本
# 测试手机验证码登录、注册等新功能

set -e

# 配置
API_BASE_URL="http://localhost:8080/api/v1/auth"
TEST_PHONE="13800138000"
TEST_PHONE_2="13800138001"
CONTENT_TYPE="Content-Type: application/json"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 测试结果统计
TOTAL_TESTS=0
PASSED_TESTS=0
FAILED_TESTS=0

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
    ((PASSED_TESTS++))
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
    ((FAILED_TESTS++))
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

# 测试函数
run_test() {
    local test_name="$1"
    local curl_command="$2"
    local expected_pattern="$3"
    
    ((TOTAL_TESTS++))
    log_info "执行测试: $test_name"
    
    # 执行curl命令并捕获响应
    response=$(eval "$curl_command" 2>/dev/null)
    http_code=$(eval "$curl_command -w '%{http_code}'" -o /dev/null 2>/dev/null)
    
    echo "   请求: $curl_command"
    echo "   响应码: $http_code"
    echo "   响应体: $response"
    
    # 检查响应
    if [[ $response =~ $expected_pattern ]]; then
        log_success "$test_name - 通过"
    else
        log_error "$test_name - 失败"
        echo "   期望包含: $expected_pattern"
    fi
    
    echo "----------------------------------------"
}

# 开始测试
echo "🚀 开始执行小鹅养成APP移动端认证API测试"
echo "========================================"

# 测试1: 发送登录验证码
log_info "测试1: 发送登录验证码"
run_test "发送登录验证码" \
    "curl -s -X POST '$API_BASE_URL/send-sms-code' \
    -H '$CONTENT_TYPE' \
    -d '{
        \"phone\": \"$TEST_PHONE\",
        \"codeType\": 1
    }'" \
    '"success":true'

# 等待1秒避免频率限制
sleep 1

# 测试2: 发送注册验证码
log_info "测试2: 发送注册验证码"
run_test "发送注册验证码" \
    "curl -s -X POST '$API_BASE_URL/send-sms-code' \
    -H '$CONTENT_TYPE' \
    -d '{
        \"phone\": \"$TEST_PHONE_2\",
        \"codeType\": 2
    }'" \
    '"success":true'

# 测试3: 验证码格式验证
log_info "测试3: 验证码格式验证"
run_test "无效手机号格式" \
    "curl -s -X POST '$API_BASE_URL/send-sms-code' \
    -H '$CONTENT_TYPE' \
    -d '{
        \"phone\": \"1234567890\",
        \"codeType\": 1
    }'" \
    '"success":false'

# 测试4: 验证短信验证码（模拟验证码）
log_info "测试4: 验证短信验证码"
run_test "验证短信验证码" \
    "curl -s -X POST '$API_BASE_URL/verify-sms-code' \
    -H '$CONTENT_TYPE' \
    -d '{
        \"phone\": \"$TEST_PHONE\",
        \"code\": \"123456\",
        \"codeType\": 1
    }'" \
    '"data":'

# 测试5: 验证错误的验证码
log_info "测试5: 验证错误的验证码"
run_test "验证错误验证码" \
    "curl -s -X POST '$API_BASE_URL/verify-sms-code' \
    -H '$CONTENT_TYPE' \
    -d '{
        \"phone\": \"$TEST_PHONE\",
        \"code\": \"000000\",
        \"codeType\": 1
    }'" \
    '"data":false'

# 测试6: 手机号注册（需要先有验证码）
log_info "测试6: 手机号注册"

# 先发送注册验证码
curl -s -X POST "$API_BASE_URL/send-sms-code" \
    -H "$CONTENT_TYPE" \
    -d "{
        \"phone\": \"$TEST_PHONE_2\",
        \"codeType\": 2
    }" > /dev/null

sleep 1

# 然后尝试注册（注意：这里使用模拟验证码，实际环境中需要真实验证码）
run_test "手机号注册" \
    "curl -s -X POST '$API_BASE_URL/register-by-phone' \
    -H '$CONTENT_TYPE' \
    -d '{
        \"phone\": \"$TEST_PHONE_2\",
        \"code\": \"123456\",
        \"nickname\": \"测试用户\",
        \"deviceInfo\": \"iPhone 15 Pro\"
    }'" \
    '"userId":'

# 测试7: 重复注册同一手机号
log_info "测试7: 重复注册同一手机号"
run_test "重复注册检查" \
    "curl -s -X POST '$API_BASE_URL/register-by-phone' \
    -H '$CONTENT_TYPE' \
    -d '{
        \"phone\": \"$TEST_PHONE_2\",
        \"code\": \"123456\",
        \"nickname\": \"测试用户2\"
    }'" \
    '"success":false'

# 测试8: 手机号登录
log_info "测试8: 手机号验证码登录"

# 先发送登录验证码
curl -s -X POST "$API_BASE_URL/send-sms-code" \
    -H "$CONTENT_TYPE" \
    -d "{
        \"phone\": \"$TEST_PHONE_2\",
        \"codeType\": 1
    }" > /dev/null

sleep 1

run_test "手机号验证码登录" \
    "curl -s -X POST '$API_BASE_URL/login-by-phone' \
    -H '$CONTENT_TYPE' \
    -d '{
        \"phone\": \"$TEST_PHONE_2\",
        \"code\": \"123456\",
        \"deviceInfo\": \"iPhone 15 Pro\"
    }'" \
    '"token":'

# 测试9: 未注册手机号登录
log_info "测试9: 未注册手机号登录"
run_test "未注册手机号登录" \
    "curl -s -X POST '$API_BASE_URL/login-by-phone' \
    -H '$CONTENT_TYPE' \
    -d '{
        \"phone\": \"13900139000\",
        \"code\": \"123456\"
    }'" \
    '"success":false'

# 测试10: 频率限制测试
log_info "测试10: 发送频率限制测试"
for i in {1..3}; do
    curl -s -X POST "$API_BASE_URL/send-sms-code" \
        -H "$CONTENT_TYPE" \
        -d "{
            \"phone\": \"$TEST_PHONE\",
            \"codeType\": 1
        }" > /dev/null
done

run_test "频率限制检查" \
    "curl -s -X POST '$API_BASE_URL/send-sms-code' \
    -H '$CONTENT_TYPE' \
    -d '{
        \"phone\": \"$TEST_PHONE\",
        \"codeType\": 1
    }'" \
    '"频繁"'

# 测试11: 参数验证测试
log_info "测试11: 参数验证测试"
run_test "缺少必需参数" \
    "curl -s -X POST '$API_BASE_URL/send-sms-code' \
    -H '$CONTENT_TYPE' \
    -d '{
        \"codeType\": 1
    }'" \
    '"success":false'

run_test "无效验证码格式" \
    "curl -s -X POST '$API_BASE_URL/verify-sms-code' \
    -H '$CONTENT_TYPE' \
    -d '{
        \"phone\": \"$TEST_PHONE\",
        \"code\": \"12345\",
        \"codeType\": 1
    }'" \
    '"success":false'

# 输出测试结果统计
echo "========================================"
echo "📊 测试结果统计"
echo "========================================"
echo "总测试数: $TOTAL_TESTS"
echo -e "通过数量: ${GREEN}$PASSED_TESTS${NC}"
echo -e "失败数量: ${RED}$FAILED_TESTS${NC}"

if [ $FAILED_TESTS -eq 0 ]; then
    echo -e "${GREEN}🎉 所有测试通过！${NC}"
    success_rate=100
else
    success_rate=$((PASSED_TESTS * 100 / TOTAL_TESTS))
    echo -e "${YELLOW}⚠️  部分测试失败${NC}"
fi

echo "成功率: ${success_rate}%"

# 额外的数据库验证
echo ""
echo "========================================"
echo "📋 数据库验证"
echo "========================================"

log_info "验证数据库中的数据..."

# 检查短信验证码表
echo "短信验证码表记录数:"
docker exec -i b5a3b2b61008 mysql -u root -p123456 -e "USE goose_app; SELECT COUNT(*) as sms_codes_count FROM sms_verification_codes WHERE is_deleted = 0;" 2>/dev/null | tail -n 1

# 检查用户登录日志表
echo "用户登录日志表记录数:"
docker exec -i b5a3b2b61008 mysql -u root -p123456 -e "USE goose_app; SELECT COUNT(*) as login_logs_count FROM user_login_logs WHERE is_deleted = 0;" 2>/dev/null | tail -n 1

# 检查新注册的用户
echo "移动端用户数量:"
docker exec -i b5a3b2b61008 mysql -u root -p123456 -e "USE goose_app; SELECT COUNT(*) as mobile_users_count FROM users WHERE user_type = 1 AND is_deleted = 0;" 2>/dev/null | tail -n 1

echo ""
echo "🏁 测试完成！"

# 返回适当的退出码
if [ $FAILED_TESTS -eq 0 ]; then
    exit 0
else
    exit 1
fi
