# 🧪 小鹅养成APP - 测试指南

## 📋 测试概述

本项目包含完整的测试套件，包括单元测试、集成测试和API测试。所有测试都基于JUnit 5和Spring Boot Test框架。

## 🛠 测试环境配置

### 测试数据库

测试使用H2内存数据库，无需额外配置：

```yaml
# src/test/resources/application-test.yml
spring:
  datasource:
    url: jdbc:h2:mem:testdb
    username: sa
    password: 
    driver-class-name: org.h2.Driver
```

### 测试Redis

测试环境使用嵌入式Redis或Mock Redis，无需启动真实Redis服务。

## 🚀 运行测试

### 1. 运行所有测试

```bash
# 运行所有测试
mvn test

# 运行测试并生成报告
mvn clean test
```

### 2. 运行特定测试类

```bash
# 运行用户服务测试
mvn test -Dtest=UserServiceTest

# 运行认证控制器测试
mvn test -Dtest=AuthControllerTest

# 运行所有用户模块测试
mvn test -Dtest="com.gooseapp.user.**"
```

### 3. 运行特定测试方法

```bash
# 运行特定测试方法
mvn test -Dtest=UserServiceTest#testRegisterSuccess

# 运行多个测试方法
mvn test -Dtest=UserServiceTest#testRegisterSuccess+testLoginSuccess
```

### 4. 跳过测试

```bash
# 编译时跳过测试
mvn clean package -DskipTests

# 或者
mvn clean package -Dmaven.test.skip=true
```

## 📊 测试覆盖率

### 生成覆盖率报告

```bash
# 运行测试并生成覆盖率报告
mvn clean test jacoco:report
```

### 查看覆盖率报告

覆盖率报告生成在：`target/site/jacoco/index.html`

```bash
# 在浏览器中打开报告
open target/site/jacoco/index.html
```

### 覆盖率要求

- **总体覆盖率**: ≥ 80%
- **类覆盖率**: ≥ 90%
- **方法覆盖率**: ≥ 85%
- **行覆盖率**: ≥ 80%

## 🧪 测试类型

### 1. 单元测试

测试单个类或方法的功能：

```java
@ExtendWith(MockitoExtension.class)
class UserServiceTest {
    
    @Mock
    private UserRepository userRepository;
    
    @InjectMocks
    private UserServiceImpl userService;
    
    @Test
    void testRegisterSuccess() {
        // 测试用户注册成功场景
    }
}
```

### 2. 集成测试

测试多个组件协同工作：

```java
@SpringBootTest
@Transactional
class UserServiceIntegrationTest {
    
    @Autowired
    private UserService userService;
    
    @Test
    void testUserRegistrationFlow() {
        // 测试完整的用户注册流程
    }
}
```

### 3. Web层测试

测试Controller层：

```java
@WebMvcTest(AuthController.class)
class AuthControllerTest {
    
    @Autowired
    private MockMvc mockMvc;
    
    @MockBean
    private UserService userService;
    
    @Test
    void testRegisterEndpoint() throws Exception {
        // 测试注册接口
    }
}
```

## 📝 测试数据

### 测试用户数据

```java
// 标准测试用户
User testUser = new User();
testUser.setUsername("testuser");
testUser.setEmail("<EMAIL>");
testUser.setPassword("$2a$10$encodedPassword");
testUser.setNickname("测试用户");
testUser.setStatus(User.Status.NORMAL.getCode());
```

### 测试DTO数据

```java
// 注册请求数据
UserRegisterDTO registerDTO = new UserRegisterDTO();
registerDTO.setUsername("testuser");
registerDTO.setEmail("<EMAIL>");
registerDTO.setPassword("password123");
registerDTO.setConfirmPassword("password123");
registerDTO.setNickname("测试用户");
```

## 🔧 Mock配置

### Mock外部依赖

```java
@MockBean
private PasswordUtils passwordUtils;

@BeforeEach
void setUp() {
    when(passwordUtils.encode(anyString())).thenReturn("$2a$10$encodedPassword");
    when(passwordUtils.matches(anyString(), anyString())).thenReturn(true);
}
```

### Mock数据库操作

```java
@Mock
private UserRepository userRepository;

@Test
void testFindUser() {
    when(userRepository.findById(1L)).thenReturn(Optional.of(testUser));
    
    User result = userService.getUserById(1L);
    
    assertThat(result).isNotNull();
    verify(userRepository).findById(1L);
}
```

## 🚨 测试断言

### 基本断言

```java
// 使用AssertJ进行断言
assertThat(result).isNotNull();
assertThat(result.getUsername()).isEqualTo("testuser");
assertThat(result.getStatus()).isEqualTo(User.Status.NORMAL.getCode());
```

### 异常断言

```java
// 测试异常抛出
assertThatThrownBy(() -> userService.getUserById(999L))
    .isInstanceOf(BusinessException.class)
    .hasFieldOrPropertyWithValue("code", ErrorCode.USER_NOT_FOUND.getCode());
```

### 集合断言

```java
// 测试集合
assertThat(userList)
    .hasSize(3)
    .extracting(User::getUsername)
    .containsExactly("user1", "user2", "user3");
```

## 📋 测试检查清单

### 功能测试

- [ ] 正常流程测试
- [ ] 边界条件测试
- [ ] 异常情况测试
- [ ] 参数验证测试

### 业务逻辑测试

- [ ] 用户注册流程
- [ ] 用户登录验证
- [ ] 密码加密验证
- [ ] Token管理测试

### API接口测试

- [ ] 请求参数验证
- [ ] 响应格式验证
- [ ] 错误码验证
- [ ] 认证授权测试

### 数据库测试

- [ ] 数据持久化测试
- [ ] 事务回滚测试
- [ ] 约束验证测试
- [ ] 查询性能测试

## 🐛 测试调试

### 查看测试日志

```bash
# 运行测试并显示详细日志
mvn test -Dtest=UserServiceTest -Dlogging.level.com.gooseapp=DEBUG
```

### 测试失败分析

1. **查看测试报告**：`target/surefire-reports/`
2. **检查日志输出**：查看控制台输出
3. **验证Mock配置**：确认Mock行为正确
4. **检查测试数据**：验证测试数据完整性

### 常见测试问题

1. **Mock未生效**
   ```java
   // 确保Mock注解正确
   @MockBean  // Spring Boot测试
   @Mock      // 纯Mockito测试
   ```

2. **事务问题**
   ```java
   // 测试类添加事务注解
   @Transactional
   @Rollback  // 测试后回滚
   ```

3. **上下文加载失败**
   ```java
   // 检查测试配置
   @SpringBootTest
   @ActiveProfiles("test")
   ```

## 📈 持续集成

### GitHub Actions配置

```yaml
# .github/workflows/test.yml
name: Tests
on: [push, pull_request]
jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - name: Set up JDK 17
        uses: actions/setup-java@v2
        with:
          java-version: '17'
      - name: Run tests
        run: mvn clean test
      - name: Generate test report
        run: mvn jacoco:report
```

### 测试报告

- **Surefire报告**：`target/surefire-reports/`
- **JaCoCo报告**：`target/site/jacoco/`
- **测试日志**：`target/test.log`

## 🎯 最佳实践

### 1. 测试命名

```java
// 使用描述性的测试方法名
@Test
void shouldThrowExceptionWhenUserNotFound() { }

@Test
void shouldReturnUserWhenValidIdProvided() { }
```

### 2. 测试结构

```java
@Test
void testMethodName() {
    // Given - 准备测试数据
    UserRegisterDTO registerDTO = createTestRegisterDTO();
    
    // When - 执行测试操作
    User result = userService.register(registerDTO);
    
    // Then - 验证测试结果
    assertThat(result).isNotNull();
    assertThat(result.getUsername()).isEqualTo("testuser");
}
```

### 3. 测试隔离

```java
@BeforeEach
void setUp() {
    // 每个测试前重置状态
}

@AfterEach
void tearDown() {
    // 每个测试后清理资源
}
```

### 4. 测试数据管理

```java
// 使用Builder模式创建测试数据
public class TestDataBuilder {
    public static User.UserBuilder defaultUser() {
        return User.builder()
            .username("testuser")
            .email("<EMAIL>")
            .status(User.Status.NORMAL.getCode());
    }
}
```

---

**注意**: 运行测试前请确保已安装JDK 17和Maven 3.9+。测试过程中会自动创建和销毁测试数据，不会影响开发数据库。
