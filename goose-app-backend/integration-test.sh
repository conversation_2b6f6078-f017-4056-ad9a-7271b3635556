#!/bin/bash

# 小鹅养成APP - 集成测试自动化脚本
# 作者: Java后端开发专家
# 版本: 1.0.0
# 日期: 2025-06-03

echo "🦆 小鹅养成APP - 集成测试自动化脚本"
echo "========================================"

# 配置信息
DB_HOST="localhost"
DB_PORT="3306"
DB_NAME="goose_app"
DB_USER="root"
DB_PASSWORD="123456"
ADMIN_USERNAME="superAdmin"
ADMIN_PASSWORD="admin123"
APP_PORT="8080"
BASE_URL="http://localhost:${APP_PORT}"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查依赖
check_dependencies() {
    log_info "检查依赖环境..."
    
    # 检查Java
    if ! command -v java &> /dev/null; then
        log_error "Java未安装，请安装JDK 17+"
        exit 1
    fi
    
    # 检查Maven
    if ! command -v mvn &> /dev/null; then
        log_error "Maven未安装，请安装Maven 3.9+"
        exit 1
    fi
    
    # 检查curl
    if ! command -v curl &> /dev/null; then
        log_error "curl未安装，请安装curl"
        exit 1
    fi
    
    # 检查Docker MySQL
    if ! docker ps | grep -q mysql; then
        log_error "MySQL Docker容器未运行"
        log_info "请确保MySQL容器正在运行: docker run -d --name mysql -p 3306:3306 -e MYSQL_ROOT_PASSWORD=123456 mysql:8.0"
        exit 1
    fi
    
    log_success "依赖检查通过"
}

# 检查数据库连接
check_database() {
    log_info "检查数据库连接..."
    
    # 测试数据库连接
    if ! docker exec mysql mysql -u${DB_USER} -p${DB_PASSWORD} -e "SELECT 1" &> /dev/null; then
        log_error "数据库连接失败"
        exit 1
    fi
    
    # 检查数据库是否存在
    if ! docker exec mysql mysql -u${DB_USER} -p${DB_PASSWORD} -e "USE ${DB_NAME}; SELECT 1" &> /dev/null; then
        log_warning "数据库 ${DB_NAME} 不存在，请先执行数据库初始化脚本"
        log_info "执行: docker exec mysql mysql -u${DB_USER} -p${DB_PASSWORD} < scripts/init-db.sql"
        exit 1
    fi
    
    log_success "数据库连接正常"
}

# 编译项目
compile_project() {
    log_info "编译项目..."
    
    if ! mvn clean compile -q; then
        log_error "项目编译失败"
        exit 1
    fi
    
    log_success "项目编译成功"
}

# 启动应用
start_application() {
    log_info "启动Spring Boot应用..."
    
    # 检查端口是否被占用
    if lsof -i :${APP_PORT} &> /dev/null; then
        log_warning "端口 ${APP_PORT} 已被占用，尝试停止现有进程..."
        pkill -f "spring-boot:run" || true
        sleep 3
    fi
    
    # 后台启动应用
    nohup mvn spring-boot:run -Dspring-boot.run.profiles=dev > app.log 2>&1 &
    APP_PID=$!
    
    log_info "等待应用启动... (PID: ${APP_PID})"
    
    # 等待应用启动
    for i in {1..30}; do
        if curl -s "${BASE_URL}/actuator/health" | grep -q "UP"; then
            log_success "应用启动成功"
            return 0
        fi
        echo -n "."
        sleep 2
    done
    
    log_error "应用启动超时"
    cat app.log
    exit 1
}

# 停止应用
stop_application() {
    log_info "停止应用..."
    pkill -f "spring-boot:run" || true
    sleep 2
    log_success "应用已停止"
}

# API测试函数
test_api() {
    local method=$1
    local endpoint=$2
    local data=$3
    local expected_status=$4
    local token=$5
    
    local headers="Content-Type: application/json"
    if [ -n "$token" ]; then
        headers="${headers} -H Authorization: Bearer ${token}"
    fi
    
    local response
    if [ "$method" = "GET" ]; then
        response=$(curl -s -w "\n%{http_code}" -H "$headers" "${BASE_URL}${endpoint}")
    else
        response=$(curl -s -w "\n%{http_code}" -X "$method" -H "$headers" -d "$data" "${BASE_URL}${endpoint}")
    fi
    
    local body=$(echo "$response" | head -n -1)
    local status=$(echo "$response" | tail -n 1)
    
    if [ "$status" = "$expected_status" ]; then
        log_success "✅ $method $endpoint - 状态码: $status"
        echo "$body"
        return 0
    else
        log_error "❌ $method $endpoint - 期望: $expected_status, 实际: $status"
        echo "响应: $body"
        return 1
    fi
}

# 执行集成测试
run_integration_tests() {
    log_info "开始执行集成测试..."
    
    local test_count=0
    local pass_count=0
    
    # 测试1: 健康检查
    log_info "测试1: 健康检查"
    if test_api "GET" "/actuator/health" "" "200"; then
        ((pass_count++))
    fi
    ((test_count++))
    
    # 测试2: 管理员登录
    log_info "测试2: 管理员登录"
    local login_data="{\"username\":\"${ADMIN_USERNAME}\",\"password\":\"${ADMIN_PASSWORD}\"}"
    local login_response
    if login_response=$(test_api "POST" "/api/v1/auth/login" "$login_data" "200"); then
        # 提取token
        ADMIN_TOKEN=$(echo "$login_response" | grep -o '"token":"[^"]*"' | cut -d'"' -f4)
        if [ -n "$ADMIN_TOKEN" ]; then
            log_success "管理员Token获取成功: ${ADMIN_TOKEN:0:20}..."
            ((pass_count++))
        else
            log_error "无法提取管理员Token"
        fi
    fi
    ((test_count++))
    
    # 测试3: 获取当前用户信息
    if [ -n "$ADMIN_TOKEN" ]; then
        log_info "测试3: 获取当前用户信息"
        if test_api "GET" "/api/v1/auth/me" "" "200" "$ADMIN_TOKEN"; then
            ((pass_count++))
        fi
        ((test_count++))
    fi
    
    # 测试4: 查看待售小鹅
    log_info "测试4: 查看待售小鹅"
    if test_api "GET" "/api/v1/shop/geese/available" "" "200"; then
        ((pass_count++))
    fi
    ((test_count++))
    
    # 测试5: 管理员创建小鹅
    if [ -n "$ADMIN_TOKEN" ]; then
        log_info "测试5: 管理员创建小鹅"
        local create_goose_data='{
            "name": "测试小鹅",
            "breed": "白鹅",
            "gender": 1,
            "initialHealth": 100,
            "initialHunger": 50,
            "initialThirst": 50,
            "initialCleanliness": 100,
            "initialHappiness": 80,
            "initialLevel": 1,
            "initialExperience": 0,
            "imageUrl": "/images/test_goose.jpg",
            "videoUrl": "/videos/test_stream.m3u8"
        }'
        if test_api "POST" "/api/v1/admin/geese/create" "$create_goose_data" "200" "$ADMIN_TOKEN"; then
            ((pass_count++))
        fi
        ((test_count++))
    fi
    
    # 测试6: 用户注册
    log_info "测试6: 用户注册"
    local register_data='{
        "username": "testuser_'$(date +%s)'",
        "email": "test'$(date +%s)'@example.com",
        "password": "password123",
        "confirmPassword": "password123",
        "nickname": "测试用户"
    }'
    if test_api "POST" "/api/v1/auth/register" "$register_data" "200"; then
        ((pass_count++))
    fi
    ((test_count++))
    
    # 测试7: API文档访问
    log_info "测试7: API文档访问"
    if curl -s "${BASE_URL}/swagger-ui.html" | grep -q "swagger"; then
        log_success "✅ API文档访问正常"
        ((pass_count++))
    else
        log_error "❌ API文档访问失败"
    fi
    ((test_count++))
    
    # 输出测试结果
    echo ""
    log_info "========== 集成测试结果 =========="
    log_info "总测试数: $test_count"
    log_success "通过测试: $pass_count"
    log_error "失败测试: $((test_count - pass_count))"
    
    if [ $pass_count -eq $test_count ]; then
        log_success "🎉 所有集成测试通过！"
        return 0
    else
        log_error "❌ 部分测试失败"
        return 1
    fi
}

# 生成测试报告
generate_report() {
    local status=$1
    local report_file="integration-test-report-$(date +%Y%m%d_%H%M%S).md"
    
    cat > "$report_file" << EOF
# 小鹅养成APP集成测试报告

## 测试信息
- **测试时间**: $(date '+%Y-%m-%d %H:%M:%S')
- **测试环境**: 本地开发环境
- **数据库**: MySQL Docker (${DB_HOST}:${DB_PORT}/${DB_NAME})
- **应用地址**: ${BASE_URL}

## 测试配置
- **管理员账户**: ${ADMIN_USERNAME}
- **数据库用户**: ${DB_USER}
- **应用端口**: ${APP_PORT}

## 测试结果
$(if [ "$status" = "0" ]; then echo "✅ **测试状态**: 通过"; else echo "❌ **测试状态**: 失败"; fi)

## 测试日志
\`\`\`
$(tail -n 50 app.log 2>/dev/null || echo "无应用日志")
\`\`\`

## 建议
1. 定期执行集成测试确保系统稳定性
2. 监控应用日志发现潜在问题
3. 完善测试用例覆盖更多场景

---
*报告生成时间: $(date)*
EOF
    
    log_info "测试报告已生成: $report_file"
}

# 清理函数
cleanup() {
    log_info "清理测试环境..."
    stop_application
    rm -f app.log
}

# 主函数
main() {
    # 设置错误处理
    set -e
    trap cleanup EXIT
    
    echo "开始集成测试流程..."
    echo ""
    
    # 执行测试步骤
    check_dependencies
    check_database
    compile_project
    start_application
    
    # 等待应用完全启动
    sleep 5
    
    # 执行集成测试
    if run_integration_tests; then
        generate_report 0
        log_success "🎉 集成测试完成 - 全部通过"
        exit 0
    else
        generate_report 1
        log_error "❌ 集成测试完成 - 部分失败"
        exit 1
    fi
}

# 检查是否直接运行脚本
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
