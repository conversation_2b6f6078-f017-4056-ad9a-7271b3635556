#!/bin/bash

# 小鹅养成APP - 快速测试脚本
# 用于快速验证核心功能是否正常

echo "🦆 小鹅养成APP - 快速测试"
echo "=========================="

# 配置
BASE_URL="http://localhost:8080"

# 颜色定义
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
NC='\033[0m'

# 测试计数
TOTAL=0
PASSED=0

# 测试函数
test_endpoint() {
    local name=$1
    local url=$2
    local expected_status=$3
    
    ((TOTAL++))
    echo -n "测试 $name ... "
    
    local status=$(curl -s -o /dev/null -w "%{http_code}" "$url")
    
    if [ "$status" = "$expected_status" ]; then
        echo -e "${GREEN}✅ 通过${NC} ($status)"
        ((PASSED++))
    else
        echo -e "${RED}❌ 失败${NC} (期望: $expected_status, 实际: $status)"
    fi
}

# 检查应用是否启动
echo "检查应用状态..."
if ! curl -s "$BASE_URL/actuator/health" >/dev/null; then
    echo -e "${RED}❌ 应用未启动，请先启动应用${NC}"
    echo "启动命令: mvn spring-boot:run"
    exit 1
fi

echo -e "${GREEN}✅ 应用已启动${NC}"
echo ""

# 执行快速测试
echo "执行快速功能测试..."

# 基础健康检查
test_endpoint "健康检查" "$BASE_URL/actuator/health" "200"

# API文档访问
test_endpoint "API文档" "$BASE_URL/swagger-ui.html" "200"

# 商城接口（无需认证）
test_endpoint "待售小鹅列表" "$BASE_URL/api/v1/shop/geese/available" "200"
test_endpoint "小鹅品种列表" "$BASE_URL/api/v1/shop/geese/breeds" "200"

# 认证接口测试
echo ""
echo "测试用户认证..."

# 注册测试用户
REGISTER_DATA='{"username":"quicktest","email":"<EMAIL>","password":"password123","confirmPassword":"password123","nickname":"快速测试用户"}'
REGISTER_STATUS=$(curl -s -o /dev/null -w "%{http_code}" -X POST -H "Content-Type: application/json" -d "$REGISTER_DATA" "$BASE_URL/api/v1/auth/register")

if [ "$REGISTER_STATUS" = "200" ]; then
    echo -e "${GREEN}✅ 用户注册成功${NC}"
    
    # 登录获取Token
    LOGIN_DATA='{"username":"quicktest","password":"password123"}'
    LOGIN_RESPONSE=$(curl -s -X POST -H "Content-Type: application/json" -d "$LOGIN_DATA" "$BASE_URL/api/v1/auth/login")
    
    if echo "$LOGIN_RESPONSE" | grep -q "token"; then
        echo -e "${GREEN}✅ 用户登录成功${NC}"
        
        # 提取Token（简化处理）
        if command -v jq >/dev/null 2>&1; then
            TOKEN=$(echo "$LOGIN_RESPONSE" | jq -r '.data.token // empty')
            if [ ! -z "$TOKEN" ]; then
                echo -e "${GREEN}✅ Token获取成功${NC}"
                
                # 测试需要认证的接口
                AUTH_STATUS=$(curl -s -o /dev/null -w "%{http_code}" -H "satoken: Bearer $TOKEN" "$BASE_URL/api/v1/users/profile")
                if [ "$AUTH_STATUS" = "200" ]; then
                    echo -e "${GREEN}✅ 认证接口测试成功${NC}"
                    ((PASSED++))
                else
                    echo -e "${RED}❌ 认证接口测试失败${NC}"
                fi
                ((TOTAL++))
            fi
        fi
    else
        echo -e "${RED}❌ 用户登录失败${NC}"
    fi
else
    echo -e "${YELLOW}⚠️ 用户注册失败 (可能用户已存在)${NC}"
    
    # 尝试直接登录
    LOGIN_DATA='{"username":"quicktest","password":"password123"}'
    LOGIN_RESPONSE=$(curl -s -X POST -H "Content-Type: application/json" -d "$LOGIN_DATA" "$BASE_URL/api/v1/auth/login")
    
    if echo "$LOGIN_RESPONSE" | grep -q "token"; then
        echo -e "${GREEN}✅ 用户登录成功${NC}"
    else
        echo -e "${RED}❌ 用户登录失败${NC}"
    fi
fi

# 输出结果
echo ""
echo "=========================="
echo "快速测试结果:"
echo "总测试数: $TOTAL"
echo -e "通过测试: ${GREEN}$PASSED${NC}"
echo -e "失败测试: ${RED}$((TOTAL - PASSED))${NC}"

if [ $PASSED -eq $TOTAL ]; then
    echo -e "${GREEN}🎉 所有快速测试通过！${NC}"
    echo ""
    echo "建议执行完整测试:"
    echo "./comprehensive-integration-test.sh"
    exit 0
else
    echo -e "${RED}❌ 部分测试失败，请检查应用状态${NC}"
    exit 1
fi
