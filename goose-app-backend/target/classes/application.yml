# 小鹅养成APP后端配置文件
server:
  port: 8080
  servlet:
    context-path: /
    encoding:
      charset: UTF-8
      enabled: true
      force: true

spring:
  profiles:
    active: dev
  application:
    name: goose-app-backend

  # 数据源配置
  datasource:
    url: ***************************************************************************************************************************************************
    username: goose_user
    password: ENC(Z/u5gq3M3xlj0h8w3Yp+sO8i7N/L7x9bC4b/7FvX6/cR5gH8i6Y8vX8z8X8Y8p7m)
    driver-class-name: com.mysql.cj.jdbc.Driver
    hikari:
      maximum-pool-size: 20
      minimum-idle: 5
      connection-timeout: 30000
      idle-timeout: 600000
      max-lifetime: 1800000

  # MyBatis Plus配置
  mybatis-plus:
    configuration:
      # 开启驼峰命名转换
      map-underscore-to-camel-case: true
      # 开启SQL日志
      log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
    global-config:
      db-config:
        # 主键类型
        id-type: auto
        # 逻辑删除配置
        logic-delete-field: isDeleted
        logic-delete-value: 1
        logic-not-delete-value: 0
        # 字段验证策略
        insert-strategy: not_null
        update-strategy: not_null
        select-strategy: not_empty

  # Redis配置
  data:
    redis:
      host: localhost
      port: 6379
      password:
      database: 0
      timeout: 5000ms
      lettuce:
        pool:
          max-active: 8
          max-idle: 8
          min-idle: 0
          max-wait: -1ms

  # Jackson配置
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: Asia/Shanghai
    default-property-inclusion: non_null
    serialization:
      write-dates-as-timestamps: false
      fail-on-empty-beans: false
    deserialization:
      fail-on-unknown-properties: false

# Sa-Token配置
sa-token:
  # token名称 (同时也是header名称)
  token-name: satoken
  # token有效期，单位s 默认30天, -1代表永不过期
  timeout: 2592000
  # token临时有效期 (指定时间内无操作就视为token过期) 单位: 秒
  activity-timeout: -1
  # 是否允许同一账号并发登录 (为true时允许一起登录, 为false时新登录挤掉旧登录)
  is-concurrent: true
  # 在多人登录同一账号时，是否共用一个token (为true时所有登录共用一个token, 为false时每次登录新建一个token)
  is-share: true
  # token风格
  token-style: uuid
  # 是否输出操作日志
  is-log: false
  # 是否从cookie中读取token
  is-read-cookie: false
  # 是否从header中读取token
  is-read-header: true
  # token前缀 (保持Bearer前缀)
  token-prefix: Bearer
  # jwt秘钥
  jwt-secret-key: ENC(fA/w7gH3b+y4F9zG7cE1aB6dF8gH2jK5l4mN0oP3qR1sT2uV5wX8yZ0aB3cE6gH9)

# 日志配置
logging:
  level:
    com.gooseapp: DEBUG
    org.springframework.web: INFO
    org.hibernate.SQL: DEBUG
    org.hibernate.type.descriptor.sql.BasicBinder: TRACE
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
  file:
    name: logs/goose-app.log

# 应用自定义配置
app:
  name: 小鹅养成APP
  version: 1.0.0
  description: 一款有趣的虚拟宠物养成应用

  # 密码加密配置
  password:
    salt: ENC(pL9kH8jG6fD5gH2jK5l4mN0oP3qR1sT2uV5wX8yZ0aB3cE6gH9fA/w7gH3b+y4F)
    strength: 10

  # 文件上传配置
  upload:
    path: /uploads/
    max-file-size: 10MB
    max-request-size: 50MB
    allowed-types: jpg,jpeg,png,gif,mp4,mov

  # 业务配置
  business:
    # 互动冷却时间（分钟）
    interaction:
      feed-cooldown: 60
      water-cooldown: 45
      clean-cooldown: 120
      play-cooldown: 30

    # 转赠配置
    gift:
      expire-hours: 72
      max-per-day: 5

# Jasypt配置
jasypt:
  encryptor:
    password: ${JASYPT_ENCRYPTOR_PASSWORD}
    algorithm: PBEWITHHMACSHA512ANDAES_256
    iv-generator-classname: org.jasypt.iv.RandomIvGenerator
    salt-generator-classname: org.jasypt.salt.RandomSaltGenerator
    string-output-type: base64

