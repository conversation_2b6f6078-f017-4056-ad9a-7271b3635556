-- 小鹅养成APP架构重构 - 添加BaseEntity标准字段
-- 执行时间: 2024-12-19
-- 说明: 为所有表添加标准字段：createUser、updateUser、isDeleted

USE goose_app;

-- 1. 为users表添加缺失字段
ALTER TABLE users 
ADD COLUMN IF NOT EXISTS create_user BIGINT COMMENT '创建用户ID',
ADD COLUMN IF NOT EXISTS update_user BIGINT COMMENT '更新用户ID',
ADD COLUMN IF NOT EXISTS is_deleted TINYINT DEFAULT 0 COMMENT '逻辑删除：0-未删除，1-已删除';

-- 2. 为geese表添加缺失字段
ALTER TABLE geese 
ADD COLUMN IF NOT EXISTS create_user BIGINT COMMENT '创建用户ID',
ADD COLUMN IF NOT EXISTS update_user BIGINT COMMENT '更新用户ID',
ADD COLUMN IF NOT EXISTS is_deleted TINYINT DEFAULT 0 COMMENT '逻辑删除：0-未删除，1-已删除';

-- 3. 为gifts表添加缺失字段
ALTER TABLE gifts 
ADD COLUMN IF NOT EXISTS create_user BIGINT COMMENT '创建用户ID',
ADD COLUMN IF NOT EXISTS update_user BIGINT COMMENT '更新用户ID',
ADD COLUMN IF NOT EXISTS is_deleted TINYINT DEFAULT 0 COMMENT '逻辑删除：0-未删除，1-已删除';

-- 4. 为goose_purchases表添加缺失字段
ALTER TABLE goose_purchases 
ADD COLUMN IF NOT EXISTS create_user BIGINT COMMENT '创建用户ID',
ADD COLUMN IF NOT EXISTS update_user BIGINT COMMENT '更新用户ID',
ADD COLUMN IF NOT EXISTS is_deleted TINYINT DEFAULT 0 COMMENT '逻辑删除：0-未删除，1-已删除';

-- 5. 为user_roles表添加缺失字段
ALTER TABLE user_roles 
ADD COLUMN IF NOT EXISTS create_user BIGINT COMMENT '创建用户ID',
ADD COLUMN IF NOT EXISTS update_user BIGINT COMMENT '更新用户ID',
ADD COLUMN IF NOT EXISTS is_deleted TINYINT DEFAULT 0 COMMENT '逻辑删除：0-未删除，1-已删除';

-- 6. 为interactions表添加缺失字段
ALTER TABLE interactions 
ADD COLUMN IF NOT EXISTS create_user BIGINT COMMENT '创建用户ID',
ADD COLUMN IF NOT EXISTS update_user BIGINT COMMENT '更新用户ID',
ADD COLUMN IF NOT EXISTS is_deleted TINYINT DEFAULT 0 COMMENT '逻辑删除：0-未删除，1-已删除';

-- 7. 为products表添加缺失字段（如果存在）
ALTER TABLE products 
ADD COLUMN IF NOT EXISTS create_user BIGINT COMMENT '创建用户ID',
ADD COLUMN IF NOT EXISTS update_user BIGINT COMMENT '更新用户ID',
ADD COLUMN IF NOT EXISTS is_deleted TINYINT DEFAULT 0 COMMENT '逻辑删除：0-未删除，1-已删除';

-- 8. 为orders表添加缺失字段（如果存在）
ALTER TABLE orders 
ADD COLUMN IF NOT EXISTS create_user BIGINT COMMENT '创建用户ID',
ADD COLUMN IF NOT EXISTS update_user BIGINT COMMENT '更新用户ID',
ADD COLUMN IF NOT EXISTS is_deleted TINYINT DEFAULT 0 COMMENT '逻辑删除：0-未删除，1-已删除';

-- 9. 为order_items表添加缺失字段（如果存在）
ALTER TABLE order_items 
ADD COLUMN IF NOT EXISTS create_user BIGINT COMMENT '创建用户ID',
ADD COLUMN IF NOT EXISTS update_user BIGINT COMMENT '更新用户ID',
ADD COLUMN IF NOT EXISTS is_deleted TINYINT DEFAULT 0 COMMENT '逻辑删除：0-未删除，1-已删除';

-- 10. 初始化现有数据的标准字段
UPDATE users SET create_user = 1, update_user = 1, is_deleted = 0 WHERE create_user IS NULL;
UPDATE geese SET create_user = 1, update_user = 1, is_deleted = 0 WHERE create_user IS NULL;
UPDATE gifts SET create_user = 1, update_user = 1, is_deleted = 0 WHERE create_user IS NULL;
UPDATE goose_purchases SET create_user = 1, update_user = 1, is_deleted = 0 WHERE create_user IS NULL;
UPDATE user_roles SET create_user = 1, update_user = 1, is_deleted = 0 WHERE create_user IS NULL;
UPDATE interactions SET create_user = 1, update_user = 1, is_deleted = 0 WHERE create_user IS NULL;

-- 11. 为标准字段添加索引
ALTER TABLE users ADD INDEX IF NOT EXISTS idx_is_deleted (is_deleted);
ALTER TABLE geese ADD INDEX IF NOT EXISTS idx_is_deleted (is_deleted);
ALTER TABLE gifts ADD INDEX IF NOT EXISTS idx_is_deleted (is_deleted);
ALTER TABLE goose_purchases ADD INDEX IF NOT EXISTS idx_is_deleted (is_deleted);
ALTER TABLE user_roles ADD INDEX IF NOT EXISTS idx_is_deleted (is_deleted);
ALTER TABLE interactions ADD INDEX IF NOT EXISTS idx_is_deleted (is_deleted);

-- 12. 验证迁移结果
SELECT 'users' as table_name, COUNT(*) as total_records FROM users;
SELECT 'geese' as table_name, COUNT(*) as total_records FROM geese;
SELECT 'gifts' as table_name, COUNT(*) as total_records FROM gifts;
SELECT 'goose_purchases' as table_name, COUNT(*) as total_records FROM goose_purchases;
SELECT 'user_roles' as table_name, COUNT(*) as total_records FROM user_roles;
SELECT 'interactions' as table_name, COUNT(*) as total_records FROM interactions;

COMMIT;
