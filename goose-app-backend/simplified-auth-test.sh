#!/bin/bash

# ======================================================
# 小鹅养成APP - 认证模块简化API测试脚本
# Java后端开发专家 精心设计
# ======================================================

# 颜色配置
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 服务器配置
BASE_URL="http://localhost:8080"
API_PREFIX="/api/v1/auth"

# 测试计数器
TOTAL_TESTS=0
PASSED_TESTS=0
FAILED_TESTS=0

# 测试数据
TEST_USER_USERNAME="testuser_$(date +%s)"
TEST_USER_EMAIL="test_$(date +%s)@example.com"
TEST_USER_PASSWORD="password123"
TEST_USER_NICKNAME="测试用户"
ACCESS_TOKEN=""

# 打印函数
print_title() {
    echo -e "\n${BLUE}============================================${NC}"
    echo -e "${BLUE} $1 ${NC}"
    echo -e "${BLUE}============================================${NC}\n"
}

print_test() {
    echo -e "${YELLOW}🧪 测试: $1${NC}"
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
}

print_success() {
    echo -e "${GREEN}✅ 通过: $1${NC}"
    PASSED_TESTS=$((PASSED_TESTS + 1))
}

print_error() {
    echo -e "${RED}❌ 失败: $1${NC}"
    FAILED_TESTS=$((FAILED_TESTS + 1))
}

# 测试函数
test_get() {
    local url="$1"
    local description="$2"
    local auth_token="$3"
    
    print_test "$description"
    echo "📡 GET $BASE_URL$url"
    
    if [ -n "$auth_token" ]; then
        response=$(curl -s -w "\nHTTP_CODE:%{http_code}" \
            -H "Authorization: Bearer $auth_token" \
            "$BASE_URL$url")
    else
        response=$(curl -s -w "\nHTTP_CODE:%{http_code}" "$BASE_URL$url")
    fi
    
    http_code=$(echo "$response" | grep "HTTP_CODE:" | cut -d: -f2)
    body=$(echo "$response" | grep -v "HTTP_CODE:")
    
    echo "📋 响应: $body"
    echo "📊 状态码: $http_code"
    echo
    
    echo "$body" > /tmp/last_response.json
    echo "$http_code" > /tmp/last_http_code
}

test_post() {
    local url="$1"
    local data="$2"
    local description="$3"
    local auth_token="$4"
    
    print_test "$description"
    echo "📡 POST $BASE_URL$url"
    echo "📝 数据: $data"
    
    if [ -n "$auth_token" ]; then
        response=$(curl -s -w "\nHTTP_CODE:%{http_code}" \
            -X POST \
            -H "Content-Type: application/json" \
            -H "Authorization: Bearer $auth_token" \
            -d "$data" \
            "$BASE_URL$url")
    else
        response=$(curl -s -w "\nHTTP_CODE:%{http_code}" \
            -X POST \
            -H "Content-Type: application/json" \
            -d "$data" \
            "$BASE_URL$url")
    fi
    
    http_code=$(echo "$response" | grep "HTTP_CODE:" | cut -d: -f2)
    body=$(echo "$response" | grep -v "HTTP_CODE:")
    
    echo "📋 响应: $body"
    echo "📊 状态码: $http_code"
    echo
    
    echo "$body" > /tmp/last_response.json
    echo "$http_code" > /tmp/last_http_code
}

check_status() {
    local expected="$1"
    local description="$2"
    
    local actual=$(cat /tmp/last_http_code)
    
    if [ "$actual" = "$expected" ]; then
        print_success "$description - 状态码正确 ($actual)"
        return 0
    else
        print_error "$description - 状态码错误，期望: $expected，实际: $actual"
        return 1
    fi
}

check_json_field() {
    local field="$1"
    local expected="$2"
    local description="$3"
    
    local actual=$(cat /tmp/last_response.json | jq -r "$field" 2>/dev/null)
    
    if [ "$actual" = "$expected" ]; then
        print_success "$description - 字段验证通过: $field = $expected"
        return 0
    else
        print_error "$description - 字段验证失败，期望: $expected，实际: $actual"
        return 1
    fi
}

# ======================================================
# 开始测试
# ======================================================

print_title "小鹅养成APP认证模块API测试"

# 1. 检查用户名可用性
print_title "1. 用户名检查接口"
test_get "$API_PREFIX/check-username?username=$TEST_USER_USERNAME" "检查不存在的用户名"
check_status "200" "用户名检查"
check_json_field ".code" "200" "用户名检查响应码"
check_json_field ".data" "true" "用户名应该可用"

# 2. 检查邮箱可用性
print_title "2. 邮箱检查接口"
test_get "$API_PREFIX/check-email?email=$TEST_USER_EMAIL" "检查不存在的邮箱"
check_status "200" "邮箱检查"
check_json_field ".code" "200" "邮箱检查响应码"
check_json_field ".data" "true" "邮箱应该可用"

# 3. 检查手机号可用性
print_title "3. 手机号检查接口"
TEST_PHONE="13800$(date +%s | tail -c 7)"
test_get "$API_PREFIX/check-phone?phone=$TEST_PHONE" "检查不存在的手机号"
check_status "200" "手机号检查"
check_json_field ".code" "200" "手机号检查响应码"
check_json_field ".data" "true" "手机号应该可用"

# 4. 用户注册
print_title "4. 用户注册接口"
register_data='{"username":"'$TEST_USER_USERNAME'","email":"'$TEST_USER_EMAIL'","password":"'$TEST_USER_PASSWORD'","confirmPassword":"'$TEST_USER_PASSWORD'","nickname":"'$TEST_USER_NICKNAME'"}'

test_post "$API_PREFIX/register" "$register_data" "正常用户注册"
if check_status "200" "用户注册"; then
    check_json_field ".code" "200" "注册响应码"
    check_json_field ".message" "注册成功" "注册消息"
    
    # 提取token
    ACCESS_TOKEN=$(cat /tmp/last_response.json | jq -r '.data.token' 2>/dev/null)
    if [ "$ACCESS_TOKEN" != "null" ] && [ -n "$ACCESS_TOKEN" ]; then
        print_success "成功获取访问令牌"
    else
        print_error "未能获取访问令牌"
    fi
fi

# 测试重复注册
duplicate_data='{"username":"'$TEST_USER_USERNAME'","email":"another'$TEST_USER_EMAIL'","password":"'$TEST_USER_PASSWORD'","confirmPassword":"'$TEST_USER_PASSWORD'","nickname":"重复用户"}'
test_post "$API_PREFIX/register" "$duplicate_data" "重复用户名注册"
if [ "$(cat /tmp/last_http_code)" = "400" ]; then
    print_success "重复注册正确返回400错误"
    check_json_field ".code" "6003" "重复注册错误码"
else
    print_error "重复注册应该返回400错误"
fi

# 5. 用户登录
print_title "5. 用户登录接口"
login_data='{"username":"'$TEST_USER_USERNAME'","password":"'$TEST_USER_PASSWORD'"}'

test_post "$API_PREFIX/login" "$login_data" "正常用户登录"
if check_status "200" "用户登录"; then
    check_json_field ".code" "200" "登录响应码"
    check_json_field ".message" "登录成功" "登录消息"
    
    # 更新token
    NEW_TOKEN=$(cat /tmp/last_response.json | jq -r '.data.token' 2>/dev/null)
    if [ "$NEW_TOKEN" != "null" ] && [ -n "$NEW_TOKEN" ]; then
        ACCESS_TOKEN="$NEW_TOKEN"
        print_success "成功更新访问令牌"
    fi
fi

# 测试错误密码
wrong_login_data='{"username":"'$TEST_USER_USERNAME'","password":"wrong_password"}'
test_post "$API_PREFIX/login" "$wrong_login_data" "错误密码登录"
if [ "$(cat /tmp/last_http_code)" = "400" ]; then
    print_success "错误密码正确返回400错误"
    check_json_field ".code" "6006" "错误密码错误码"
else
    print_error "错误密码应该返回400错误"
fi

# 6. 获取登录状态
print_title "6. 登录状态接口"
test_get "$API_PREFIX/status" "未认证获取登录状态"
check_status "200" "登录状态检查"
check_json_field ".data.isLogin" "false" "未认证登录状态"

test_get "$API_PREFIX/status" "已认证获取登录状态" "$ACCESS_TOKEN"
check_status "200" "已认证登录状态检查"

# 7. 获取当前用户信息
print_title "7. 用户信息接口"
test_get "$API_PREFIX/me" "未认证获取用户信息"
if [ "$(cat /tmp/last_http_code)" = "401" ]; then
    print_success "未认证获取用户信息正确返回401"
else
    print_error "未认证获取用户信息应该返回401"
fi

test_get "$API_PREFIX/me" "已认证获取用户信息" "$ACCESS_TOKEN"
if check_status "200" "获取用户信息"; then
    check_json_field ".code" "200" "用户信息响应码"
    check_json_field ".data.username" "$TEST_USER_USERNAME" "用户名验证"
fi

# 8. 刷新Token
print_title "8. Token刷新接口"
test_post "$API_PREFIX/refresh" "" "未认证刷新Token"
if [ "$(cat /tmp/last_http_code)" = "401" ]; then
    print_success "未认证刷新Token正确返回401"
else
    print_error "未认证刷新Token应该返回401"
fi

test_post "$API_PREFIX/refresh" "" "已认证刷新Token" "$ACCESS_TOKEN"
if check_status "200" "Token刷新"; then
    check_json_field ".code" "200" "Token刷新响应码"
    check_json_field ".message" "Token刷新成功" "Token刷新消息"
fi

# 9. 修改密码
print_title "9. 密码修改接口"
change_password_data='{"oldPassword":"'$TEST_USER_PASSWORD'","newPassword":"new_password123"}'

test_post "$API_PREFIX/change-password" "$change_password_data" "未认证修改密码"
if [ "$(cat /tmp/last_http_code)" = "401" ]; then
    print_success "未认证修改密码正确返回401"
else
    print_error "未认证修改密码应该返回401"
fi

test_post "$API_PREFIX/change-password" "$change_password_data" "已认证修改密码" "$ACCESS_TOKEN"
if check_status "200" "密码修改"; then
    check_json_field ".code" "200" "密码修改响应码"
    check_json_field ".message" "密码修改成功" "密码修改消息"
    TEST_USER_PASSWORD="new_password123"
fi

# 10. 验证新密码登录
print_title "10. 新密码登录验证"
new_login_data='{"username":"'$TEST_USER_USERNAME'","password":"new_password123"}'
test_post "$API_PREFIX/login" "$new_login_data" "新密码登录"
if check_status "200" "新密码登录"; then
    check_json_field ".code" "200" "新密码登录响应码"
    ACCESS_TOKEN=$(cat /tmp/last_response.json | jq -r '.data.token' 2>/dev/null)
fi

# 11. 用户登出
print_title "11. 用户登出接口"
test_post "$API_PREFIX/logout" "" "未认证用户登出"
if [ "$(cat /tmp/last_http_code)" = "401" ]; then
    print_success "未认证登出正确返回401"
else
    print_error "未认证登出应该返回401"
fi

test_post "$API_PREFIX/logout" "" "已认证用户登出" "$ACCESS_TOKEN"
if check_status "200" "用户登出"; then
    check_json_field ".code" "200" "登出响应码"
    check_json_field ".message" "登出成功" "登出消息"
fi

# 12. 验证登出后状态
print_title "12. 登出后状态验证"
test_get "$API_PREFIX/me" "登出后获取用户信息" "$ACCESS_TOKEN"
if [ "$(cat /tmp/last_http_code)" = "401" ]; then
    print_success "登出后Token失效，正确返回401"
else
    print_error "登出后Token应该失效"
fi

# ======================================================
# 测试结果统计
# ======================================================
print_title "测试结果统计"

echo -e "${BLUE}📊 测试统计报告:${NC}"
echo -e "   总测试用例: ${TOTAL_TESTS}"
echo -e "   ${GREEN}✅ 通过: ${PASSED_TESTS}${NC}"
echo -e "   ${RED}❌ 失败: ${FAILED_TESTS}${NC}"

if [ $TOTAL_TESTS -gt 0 ]; then
    PASS_RATE=$((PASSED_TESTS * 100 / TOTAL_TESTS))
    echo -e "   通过率: ${PASS_RATE}%"
fi

echo -e "\n${BLUE}📋 测试覆盖范围:${NC}"
echo -e "   ✓ 用户名/邮箱/手机号可用性检查"
echo -e "   ✓ 用户注册（正常和异常场景）"
echo -e "   ✓ 用户登录（正常和异常场景）"
echo -e "   ✓ 登录状态检查"
echo -e "   ✓ 用户信息获取"
echo -e "   ✓ Token刷新"
echo -e "   ✓ 密码修改"
echo -e "   ✓ 用户登出"
echo -e "   ✓ 认证状态验证"

if [ $FAILED_TESTS -eq 0 ]; then
    echo -e "\n${GREEN}🎉 所有测试用例都通过了！认证模块API工作正常。${NC}"
    echo -e "${GREEN}✨ 接口测试覆盖完整，逻辑验证正确！${NC}"
    exit 0
else
    echo -e "\n${YELLOW}⚠️  有 $FAILED_TESTS 个测试用例失败。${NC}"
    echo -e "${YELLOW}📝 建议检查失败的接口实现和错误处理逻辑。${NC}"
    exit 1
fi

# 清理临时文件
rm -f /tmp/last_response.json /tmp/last_http_code 