package com.gooseapp.user.service.impl;

import com.gooseapp.user.dto.LoginResponse;
import com.gooseapp.user.entity.User;
import com.gooseapp.user.mapper.UserMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Optional;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.*;

 @ExtendWith(MockitoExtension.class)
 class UserStatsServiceImplTest {

     @Mock
     private UserMapper userRepository;

     @InjectMocks
    private UserStatsServiceImpl userStatsService;

    private User testUser;

    @BeforeEach
    void setUp() {
        testUser = new User();
        testUser.setId(1L);
        testUser.setTotalGeese(10);
        testUser.setTotalDays(5);
        testUser.setTotalFeeds(20);
    }

    @Test
    void getUserStats_shouldReturnCorrectStats() {
        // Arrange
        when(Optional.of(userRepository.selectById(1L))).thenReturn(Optional.of(testUser));

         // Act
         LoginResponse.UserStats stats = userStatsService.getUserStats(1L);

         // Assert
         assertEquals(10, stats.getTotalGeese());
         assertEquals(5, stats.getTotalDays());
         assertEquals(20, stats.getTotalFeeds());
        verify(userRepository, times(1)).selectById(1L);
    }

    @Test
    void updateUserStats_shouldCallRepository() {
        // Arrange
        Long userId = 1L;
        Integer totalGeese = 15;
        Integer totalDays = 6;
        Integer totalFeeds = 25;

        // Act
        userStatsService.updateUserStats(userId, totalGeese, totalDays, totalFeeds);

        // Assert
        verify(userRepository, times(1)).updateUserStats(userId, totalGeese, totalDays, totalFeeds);
    }
}
