package com.gooseapp.user.service.impl;

import com.gooseapp.user.entity.UserLoginLog;
import com.gooseapp.user.mapper.UserLoginLogMapper;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;

@ExtendWith(MockitoExtension.class)
class UserLoginLogServiceImplTest {

    @Mock
    private UserLoginLogMapper userLoginLogMapper;

    @InjectMocks
    private UserLoginLogServiceImpl userLoginLogService;

    @Test
    void recordLoginLog_shouldSaveCorrectLog() {
        // Arrange
        Long userId = 1L;
        Integer loginType = 1;
        Integer loginResult = 1;
        String ipAddress = "127.0.0.1";
        String userAgent = "Test Agent";
        String deviceInfo = "Test Device";
        String failureReason = null;

        ArgumentCaptor<UserLoginLog> logCaptor = ArgumentCaptor.forClass(UserLoginLog.class);

        // Act
        userLoginLogService.recordLoginLog(userId, loginType, loginResult, ipAddress, userAgent, deviceInfo, failureReason);

        // Assert
        verify(userLoginLogMapper, times(1)).insert(logCaptor.capture());
        UserLoginLog capturedLog = logCaptor.getValue();

        assertEquals(userId, capturedLog.getUserId());
        assertEquals(loginType, capturedLog.getLoginType());
        assertEquals(loginResult, capturedLog.getLoginResult());
        assertEquals(ipAddress, capturedLog.getIpAddress());
        assertEquals(userAgent, capturedLog.getUserAgent());
        assertEquals(deviceInfo, capturedLog.getDeviceInfo());
        assertEquals(failureReason, capturedLog.getFailureReason());
    }
}