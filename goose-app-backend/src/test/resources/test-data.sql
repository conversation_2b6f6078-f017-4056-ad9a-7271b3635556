-- 测试数据初始化脚本
-- 用于单元测试和集成测试

-- 插入测试用户数据
INSERT INTO users (id, username, email, password, nickname, phone, avatar, gender, birthday, status, created_at, updated_at) VALUES
(1, 'testuser', '<EMAIL>', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8imdqMNq4NjewGdPiS6qk2FQRwDgO', '测试用户', '13800138000', '👤', 1, '1990-01-01', 1, NOW(), NOW()),
(2, 'testuser2', '<EMAIL>', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8imdqMNq4NjewGdPiS6qk2FQRwDgO', '测试用户2', '13800138001', '👤', 2, '1992-05-15', 1, NOW(), NOW());

-- 插入测试小鹅数据
INSERT INTO geese (id, user_id, name, breed, gender, age, health, hunger, thirst, cleanliness, happiness, level, experience, image_url, status, created_at, updated_at) VALUES
(1, 1, '小白', '白鹅', 1, 2, 95, 30, 25, 90, 85, 2, 150, '/images/geese/white_male.jpg', 1, NOW(), NOW()),
(2, 1, '小花', '花鹅', 2, 3, 88, 45, 40, 85, 90, 3, 280, '/images/geese/flower_female.jpg', 1, NOW(), NOW()),
(3, 1, '小灰', '灰鹅', 1, 1, 60, 85, 90, 20, 70, 1, 80, '/images/geese/gray_male.jpg', 1, NOW(), NOW()),
(4, 2, '小黑', '黑鹅', 2, 4, 92, 35, 30, 95, 88, 4, 380, '/images/geese/black_female.jpg', 1, NOW(), NOW());

-- 插入测试互动记录
INSERT INTO interactions (id, user_id, goose_id, interaction_type, value_change, experience_gained, cooldown_minutes, created_at) VALUES
(1, 1, 1, 'feed', 15, 5, 60, NOW() - INTERVAL 2 HOUR),
(2, 1, 1, 'water', 10, 3, 30, NOW() - INTERVAL 1 HOUR),
(3, 1, 1, 'clean', 20, 8, 120, NOW() - INTERVAL 3 HOUR),
(4, 1, 2, 'feed', 15, 5, 60, NOW() - INTERVAL 1 HOUR),
(5, 1, 2, 'play', 8, 6, 45, NOW() - INTERVAL 30 MINUTE),
(6, 1, 3, 'feed', 15, 5, 60, NOW() - INTERVAL 4 HOUR),
(7, 1, 3, 'water', 10, 3, 30, NOW() - INTERVAL 2 HOUR),
(8, 2, 4, 'feed', 15, 5, 60, NOW() - INTERVAL 1 HOUR),
(9, 2, 4, 'clean', 20, 8, 120, NOW() - INTERVAL 2 HOUR);

-- 插入测试商品数据
INSERT INTO products (id, name, description, breed, price, original_price, category, age_range, health_range, image_url, tags, stock, is_featured, is_limited, status, created_at, updated_at) VALUES
(1, '小白鹅·豆豆', '温顺可爱的小白鹅，适合新手养成', '白鹅', 199.00, 299.00, '新手推荐', '1-2周', '85-95%', '/images/white_goose_1.jpg', '["新手友好", "温顺", "可爱"]', 50, TRUE, FALSE, 1, NOW(), NOW()),
(2, '小灰鹅·灰灰', '聪明活泼的小灰鹅，互动性强', '灰鹅', 299.00, 399.00, '热门推荐', '2-3周', '88-98%', '/images/gray_goose_1.jpg', '["聪明", "活泼", "互动"]', 30, TRUE, FALSE, 1, NOW(), NOW()),
(3, '小花鹅·花花', '稀有品种小花鹅，颜值超高', '花鹅', 399.00, 499.00, '限量版', '3-4周', '90-100%', '/images/flower_goose_1.jpg', '["稀有", "高颜值", "限量"]', 10, TRUE, TRUE, 1, NOW(), NOW());

-- 插入测试订单数据
INSERT INTO orders (id, order_no, user_id, total_amount, discount_amount, final_amount, payment_method, payment_status, order_status, remark, created_at, updated_at) VALUES
(1, 'ORDER20241219001', 1, 199.00, 0.00, 199.00, 'wechat', 1, 2, '测试订单', NOW() - INTERVAL 1 DAY, NOW()),
(2, 'ORDER20241219002', 2, 299.00, 20.00, 279.00, 'alipay', 1, 2, '测试订单2', NOW() - INTERVAL 2 DAY, NOW());

-- 插入测试订单项数据
INSERT INTO order_items (id, order_id, product_id, product_name, product_image, price, quantity, total_price, created_at) VALUES
(1, 1, 1, '小白鹅·豆豆', '/images/white_goose_1.jpg', 199.00, 1, 199.00, NOW() - INTERVAL 1 DAY),
(2, 2, 2, '小灰鹅·灰灰', '/images/gray_goose_1.jpg', 299.00, 1, 299.00, NOW() - INTERVAL 2 DAY);

-- 插入测试转赠记录
INSERT INTO gifts (id, gift_code, sender_id, receiver_id, goose_id, message, share_method, status, expires_at, created_at) VALUES
(1, 'GIFT20241219001', 1, NULL, 1, '送给你一只可爱的小鹅！', 'wechat', 0, NOW() + INTERVAL 7 DAY, NOW()),
(2, 'GIFT20241219002', 2, 1, 4, '希望你喜欢这只小黑鹅', 'link', 1, NOW() + INTERVAL 7 DAY, NOW() - INTERVAL 1 DAY);
