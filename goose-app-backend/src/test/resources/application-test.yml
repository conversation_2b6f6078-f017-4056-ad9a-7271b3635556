# 测试环境配置
spring:
  # 数据源配置 - 使用H2内存数据库
  datasource:
    url: jdbc:h2:mem:testdb;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE
    username: sa
    password: 
    driver-class-name: org.h2.Driver
  
  # JPA配置
  jpa:
    hibernate:
      ddl-auto: create-drop
    show-sql: true
    properties:
      hibernate:
        format_sql: true
        dialect: org.hibernate.dialect.H2Dialect
  
  # H2控制台配置
  h2:
    console:
      enabled: true
      path: /h2-console
  
  # Redis配置 - 使用嵌入式Redis
  data:
    redis:
      host: localhost
      port: 6370  # 使用不同端口避免冲突
      database: 1

# Sa-Token测试配置
sa-token:
  token-name: satoken-test
  timeout: 3600  # 1小时
  is-log: false
  jwt-secret-key: test-secret-key

# 日志配置
logging:
  level:
    com.gooseapp: DEBUG
    org.springframework.web: INFO
    org.hibernate.SQL: DEBUG
    org.hibernate.type.descriptor.sql.BasicBinder: TRACE
  pattern:
    console: "%d{HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n"

# 应用测试配置
app:
  password:
    strength: 4  # 降低密码强度要求以便测试
  
  business:
    interaction:
      feed-cooldown: 1    # 1分钟
      water-cooldown: 1   # 1分钟
      clean-cooldown: 1   # 1分钟
      play-cooldown: 1    # 1分钟
