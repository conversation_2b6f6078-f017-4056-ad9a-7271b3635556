-- 小鹅养成APP数据库初始化脚本
-- 创建时间: 2024-12-19
-- 版本: v1.0

-- 创建数据库
CREATE DATABASE IF NOT EXISTS goose_app
DEFAULT CHARACTER SET utf8mb4
DEFAULT COLLATE utf8mb4_unicode_ci;

USE goose_app;

-- 小鹅表
CREATE TABLE IF NOT EXISTS geese (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '小鹅ID',
    user_id BIGINT NOT NULL COMMENT '所属用户ID',
    name VARCHAR(50) NOT NULL COMMENT '小鹅名称',
    breed VARCHAR(30) NOT NULL COMMENT '品种',
    gender TINYINT NOT NULL COMMENT '性别：1-雄性，2-雌性',
    age INT DEFAULT 0 COMMENT '年龄（周）',
    health INT DEFAULT 100 COMMENT '健康度（0-100）',
    hunger INT DEFAULT 50 COMMENT '饥饿度（0-100）',
    thirst INT DEFAULT 50 COMMENT '口渴度（0-100）',
    cleanliness INT DEFAULT 100 COMMENT '清洁度（0-100）',
    happiness INT DEFAULT 80 COMMENT '快乐度（0-100）',
    level INT DEFAULT 1 COMMENT '等级',
    experience INT DEFAULT 0 COMMENT '经验值',
    image_url VARCHAR(255) COMMENT '小鹅图片URL',
    video_url VARCHAR(255) COMMENT '直播视频URL',
    last_feed_time TIMESTAMP NULL COMMENT '最后喂食时间',
    last_water_time TIMESTAMP NULL COMMENT '最后喂水时间',
    last_clean_time TIMESTAMP NULL COMMENT '最后清洁时间',
    status TINYINT DEFAULT 1 COMMENT '状态：0-已转赠，1-正常，2-生病',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',

    INDEX idx_user_id (user_id),
    INDEX idx_breed (breed),
    INDEX idx_status (status),
    INDEX idx_level (level),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='小鹅表';

-- 互动记录表
CREATE TABLE IF NOT EXISTS interactions (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '互动记录ID',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    goose_id BIGINT NOT NULL COMMENT '小鹅ID',
    interaction_type VARCHAR(20) NOT NULL COMMENT '互动类型：feed-喂食，water-喂水，clean-清洁，play-玩耍',
    value_change INT DEFAULT 0 COMMENT '数值变化',
    experience_gained INT DEFAULT 0 COMMENT '获得经验',
    cooldown_minutes INT DEFAULT 0 COMMENT '冷却时间（分钟）',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',

    INDEX idx_user_id (user_id),
    INDEX idx_goose_id (goose_id),
    INDEX idx_interaction_type (interaction_type),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='互动记录表';

-- 商品表
CREATE TABLE IF NOT EXISTS products (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '商品ID',
    name VARCHAR(100) NOT NULL COMMENT '商品名称',
    description TEXT COMMENT '商品描述',
    breed VARCHAR(30) NOT NULL COMMENT '小鹅品种',
    price DECIMAL(10,2) NOT NULL COMMENT '价格',
    original_price DECIMAL(10,2) COMMENT '原价',
    category VARCHAR(30) NOT NULL COMMENT '分类',
    age_range VARCHAR(20) COMMENT '年龄范围',
    health_range VARCHAR(20) COMMENT '健康度范围',
    image_url VARCHAR(255) COMMENT '商品图片URL',
    images JSON COMMENT '商品图片列表',
    tags JSON COMMENT '标签列表',
    stock INT DEFAULT 0 COMMENT '库存数量',
    sales_count INT DEFAULT 0 COMMENT '销售数量',
    rating DECIMAL(3,2) DEFAULT 5.00 COMMENT '评分（1-5）',
    is_featured BOOLEAN DEFAULT FALSE COMMENT '是否推荐',
    is_limited BOOLEAN DEFAULT FALSE COMMENT '是否限量',
    status TINYINT DEFAULT 1 COMMENT '状态：0-下架，1-上架',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',

    INDEX idx_category (category),
    INDEX idx_breed (breed),
    INDEX idx_price (price),
    INDEX idx_status (status),
    INDEX idx_is_featured (is_featured),
    INDEX idx_sales_count (sales_count),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='商品表';

-- 订单表
CREATE TABLE IF NOT EXISTS orders (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '订单ID',
    order_no VARCHAR(32) NOT NULL UNIQUE COMMENT '订单号',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    total_amount DECIMAL(10,2) NOT NULL COMMENT '订单总金额',
    discount_amount DECIMAL(10,2) DEFAULT 0.00 COMMENT '优惠金额',
    final_amount DECIMAL(10,2) NOT NULL COMMENT '实付金额',
    payment_method VARCHAR(20) COMMENT '支付方式',
    payment_status TINYINT DEFAULT 0 COMMENT '支付状态：0-待支付，1-已支付，2-已退款',
    order_status TINYINT DEFAULT 0 COMMENT '订单状态：0-待支付，1-已支付，2-已完成，3-已取消',
    remark TEXT COMMENT '订单备注',
    paid_at TIMESTAMP NULL COMMENT '支付时间',
    completed_at TIMESTAMP NULL COMMENT '完成时间',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',

    INDEX idx_order_no (order_no),
    INDEX idx_user_id (user_id),
    INDEX idx_payment_status (payment_status),
    INDEX idx_order_status (order_status),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='订单表';

-- 订单项表
CREATE TABLE IF NOT EXISTS order_items (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '订单项ID',
    order_id BIGINT NOT NULL COMMENT '订单ID',
    product_id BIGINT NOT NULL COMMENT '商品ID',
    product_name VARCHAR(100) NOT NULL COMMENT '商品名称',
    product_image VARCHAR(255) COMMENT '商品图片',
    price DECIMAL(10,2) NOT NULL COMMENT '商品单价',
    quantity INT NOT NULL DEFAULT 1 COMMENT '购买数量',
    total_price DECIMAL(10,2) NOT NULL COMMENT '小计金额',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',

    INDEX idx_order_id (order_id),
    INDEX idx_product_id (product_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='订单项表';

-- 转赠记录表
CREATE TABLE IF NOT EXISTS gifts (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '转赠记录ID',
    gift_code VARCHAR(32) NOT NULL UNIQUE COMMENT '转赠码',
    sender_id BIGINT NOT NULL COMMENT '赠送者ID',
    receiver_id BIGINT NULL COMMENT '接收者ID',
    goose_id BIGINT NOT NULL COMMENT '小鹅ID',
    message TEXT COMMENT '祝福语',
    share_method VARCHAR(20) NOT NULL COMMENT '分享方式：wechat-微信，link-链接',
    status TINYINT DEFAULT 0 COMMENT '状态：0-待领取，1-已领取，2-已过期',
    expires_at TIMESTAMP NOT NULL COMMENT '过期时间',
    received_at TIMESTAMP NULL COMMENT '领取时间',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',

    INDEX idx_gift_code (gift_code),
    INDEX idx_sender_id (sender_id),
    INDEX idx_receiver_id (receiver_id),
    INDEX idx_status (status),
    INDEX idx_expires_at (expires_at),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='转赠记录表';

-- 插入初始商品数据
INSERT INTO products (name, description, breed, price, original_price, category, age_range, health_range, image_url, tags, stock, is_featured, is_limited) VALUES
('小白鹅·豆豆', '温顺可爱的小白鹅，适合新手养成', '白鹅', 199.00, 299.00, '新手推荐', '1-2周', '85-95%', '/images/white_goose_1.jpg', '["新手友好", "温顺", "可爱"]', 50, TRUE, FALSE),
('小灰鹅·灰灰', '聪明活泼的小灰鹅，互动性强', '灰鹅', 299.00, 399.00, '热门推荐', '2-3周', '88-98%', '/images/gray_goose_1.jpg', '["聪明", "活泼", "互动"]', 30, TRUE, FALSE),
('小花鹅·花花', '稀有品种小花鹅，颜值超高', '花鹅', 399.00, 499.00, '限量版', '3-4周', '90-100%', '/images/flower_goose_1.jpg', '["稀有", "高颜值", "限量"]', 10, TRUE, TRUE),
('小黑鹅·小黑', '神秘优雅的小黑鹅，成长潜力大', '黑鹅', 159.00, 199.00, '潜力股', '1周', '80-90%', '/images/black_goose_1.jpg', '["神秘", "优雅", "潜力"]', 40, FALSE, FALSE),
('小金鹅·金金', '珍贵的金色小鹅，极其稀有', '金鹅', 999.00, 1299.00, '珍藏版', '4-5周', '95-100%', '/images/golden_goose_1.jpg', '["珍贵", "稀有", "收藏"]', 5, TRUE, TRUE),
('小棕鹅·棕棕', '朴实可靠的棕色小鹅，性价比高', '棕鹅', 129.00, 159.00, '经济实惠', '1-2周', '80-90%', '/images/brown_goose_1.jpg', '["实惠", "可靠", "经济"]', 60, FALSE, FALSE);

-- 插入测试小鹅数据（为测试用户创建）
INSERT INTO geese (user_id, name, breed, gender, age, health, hunger, thirst, cleanliness, happiness, level, experience, image_url, status) VALUES
(1, '小白', '白鹅', 1, 2, 95, 30, 25, 90, 85, 2, 150, '/images/geese/white_male.jpg', 1),
(1, '小花', '花鹅', 2, 3, 88, 45, 40, 85, 90, 3, 280, '/images/geese/flower_female.jpg', 1),
(1, '小灰', '灰鹅', 1, 1, 92, 60, 55, 95, 80, 1, 80, '/images/geese/gray_male.jpg', 1);

-- 插入测试互动记录
INSERT INTO interactions (user_id, goose_id, interaction_type, value_change, experience_gained, cooldown_minutes) VALUES
(1, 1, 'feed', 15, 5, 60),
(1, 1, 'water', 10, 3, 30),
(1, 1, 'clean', 20, 8, 120),
(1, 2, 'feed', 15, 5, 60),
(1, 2, 'play', 8, 6, 45),
(1, 3, 'feed', 15, 5, 60),
(1, 3, 'water', 10, 3, 30);
