-- 创建转赠表
-- 版本: V1.4
-- 描述: 小鹅转赠功能数据表
-- 作者: 全栈开发工程师
-- 创建时间: 2024-12-19

CREATE TABLE IF NOT EXISTS gifts (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '转赠记录ID',
    gift_code VARCHAR(32) NOT NULL UNIQUE COMMENT '转赠码（唯一）',
    sender_id BIGINT NOT NULL COMMENT '赠送者用户ID',
    receiver_id BIGINT NULL COMMENT '接收者用户ID（可为空，待接收时为空）',
    goose_id BIGINT NOT NULL COMMENT '小鹅ID',
    message TEXT COMMENT '转赠留言',
    status INT NOT NULL DEFAULT 0 COMMENT '状态：0-待接收，1-已接收，2-已完成，3-已过期，4-已取消',
    expires_at DATETIME NOT NULL COMMENT '过期时间',
    received_at DATETIME NULL COMMENT '接收时间',
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    -- 索引
    INDEX idx_gift_code (gift_code),
    INDEX idx_sender_id (sender_id),
    INDEX idx_receiver_id (receiver_id),
    INDEX idx_goose_id (goose_id),
    INDEX idx_status (status),
    INDEX idx_expires_at (expires_at),
    INDEX idx_created_at (created_at),
    INDEX idx_sender_status (sender_id, status),
    INDEX idx_receiver_status (receiver_id, status),
    
    -- 外键约束
    FOREIGN KEY (sender_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (receiver_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (goose_id) REFERENCES geese(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='转赠记录表';

-- 创建转赠统计视图
CREATE OR REPLACE VIEW gift_statistics AS
SELECT 
    u.id as user_id,
    u.username,
    u.nickname,
    COUNT(CASE WHEN g.sender_id = u.id THEN 1 END) as total_sent,
    COUNT(CASE WHEN g.receiver_id = u.id THEN 1 END) as total_received,
    COUNT(CASE WHEN g.sender_id = u.id AND g.status = 0 THEN 1 END) as pending_sent,
    COUNT(CASE WHEN g.receiver_id = u.id AND g.status = 0 THEN 1 END) as pending_received,
    COUNT(CASE WHEN g.sender_id = u.id AND g.status = 1 THEN 1 END) as received_sent,
    COUNT(CASE WHEN g.receiver_id = u.id AND g.status = 1 THEN 1 END) as received_received,
    COUNT(CASE WHEN g.sender_id = u.id AND g.status = 2 THEN 1 END) as completed_sent,
    COUNT(CASE WHEN g.sender_id = u.id AND g.status = 3 THEN 1 END) as expired_sent,
    COUNT(CASE WHEN g.sender_id = u.id AND g.status = 4 THEN 1 END) as cancelled_sent
FROM users u
LEFT JOIN gifts g ON (g.sender_id = u.id OR g.receiver_id = u.id)
GROUP BY u.id, u.username, u.nickname;

-- 插入示例数据（仅在开发环境）
-- INSERT INTO gifts (gift_code, sender_id, receiver_id, goose_id, message, status, expires_at) VALUES
-- ('GIFT20241219120000TEST', 1, NULL, 1, '测试转赠消息', 0, DATE_ADD(NOW(), INTERVAL 72 HOUR)),
-- ('GIFT20241219120001TEST', 1, 2, 2, '已接收的转赠', 1, DATE_ADD(NOW(), INTERVAL 72 HOUR)),
-- ('GIFT20241219120002TEST', 2, NULL, 3, '即将过期的转赠', 0, DATE_ADD(NOW(), INTERVAL 1 HOUR));
