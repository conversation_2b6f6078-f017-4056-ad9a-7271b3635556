-- 小鹅养成APP - 移动端认证系统扩展
-- 执行时间: 2025-01-06
-- 说明: 添加手机验证码登录支持和用户类型分类

USE goose_app;

-- 1. 为users表添加移动端认证相关字段
ALTER TABLE users
ADD COLUMN user_type TINYINT DEFAULT 1 COMMENT '用户类型：1-C端用户(移动端)，2-管理后台用户',
ADD COLUMN login_type TINYINT DEFAULT 1 COMMENT '登录方式：1-用户名密码，2-手机验证码',
ADD COLUMN phone_verified TINYINT DEFAULT 0 COMMENT '手机号是否已验证：0-未验证，1-已验证',
ADD COLUMN last_login_time DATETIME COMMENT '最后登录时间',
ADD COLUMN last_login_ip VARCHAR(45) COMMENT '最后登录IP',
ADD COLUMN login_count INT DEFAULT 0 COMMENT '登录次数统计';

-- 2. 创建手机验证码表
CREATE TABLE IF NOT EXISTS sms_verification_codes (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    phone VARCHAR(20) NOT NULL COMMENT '手机号',
    code VARCHAR(10) NOT NULL COMMENT '验证码',
    code_type TINYINT NOT NULL DEFAULT 1 COMMENT '验证码类型：1-登录，2-注册，3-重置密码，4-绑定手机',
    expires_at DATETIME NOT NULL COMMENT '过期时间',
    used TINYINT DEFAULT 0 COMMENT '是否已使用：0-未使用，1-已使用',
    ip_address VARCHAR(45) COMMENT '请求IP地址',
    user_agent TEXT COMMENT '用户代理信息',
    
    -- BaseEntity标准字段
    status TINYINT DEFAULT 1 COMMENT '状态：0-禁用，1-正常',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    create_user BIGINT COMMENT '创建用户ID',
    update_user BIGINT COMMENT '更新用户ID',
    is_deleted TINYINT DEFAULT 0 COMMENT '逻辑删除：0-未删除，1-已删除',
    
    -- 索引
    INDEX idx_phone (phone),
    INDEX idx_phone_code (phone, code),
    INDEX idx_expires_at (expires_at),
    INDEX idx_created_at (created_at),
    INDEX idx_is_deleted (is_deleted)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='短信验证码表';

-- 3. 创建用户登录日志表
CREATE TABLE IF NOT EXISTS user_login_logs (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    login_type TINYINT NOT NULL COMMENT '登录方式：1-用户名密码，2-手机验证码',
    login_time DATETIME NOT NULL COMMENT '登录时间',
    ip_address VARCHAR(45) COMMENT '登录IP地址',
    user_agent TEXT COMMENT '用户代理信息',
    device_info VARCHAR(500) COMMENT '设备信息',
    login_result TINYINT NOT NULL COMMENT '登录结果：1-成功，0-失败',
    failure_reason VARCHAR(200) COMMENT '失败原因',
    
    -- BaseEntity标准字段
    status TINYINT DEFAULT 1 COMMENT '状态：0-禁用，1-正常',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    create_user BIGINT COMMENT '创建用户ID',
    update_user BIGINT COMMENT '更新用户ID',
    is_deleted TINYINT DEFAULT 0 COMMENT '逻辑删除：0-未删除，1-已删除',
    
    -- 索引
    INDEX idx_user_id (user_id),
    INDEX idx_login_time (login_time),
    INDEX idx_ip_address (ip_address),
    INDEX idx_login_result (login_result),
    INDEX idx_is_deleted (is_deleted),
    
    -- 外键约束
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户登录日志表';

-- 4. 为users表的phone字段添加唯一索引（如果不存在）
ALTER TABLE users ADD UNIQUE INDEX uk_phone (phone);

-- 5. 为users表添加新字段的索引
ALTER TABLE users ADD INDEX idx_user_type (user_type);
ALTER TABLE users ADD INDEX idx_login_type (login_type);
ALTER TABLE users ADD INDEX idx_phone_verified (phone_verified);
ALTER TABLE users ADD INDEX idx_last_login_time (last_login_time);

-- 6. 初始化现有用户数据
UPDATE users SET 
    user_type = 1,  -- 默认为C端用户
    login_type = 1, -- 默认为用户名密码登录
    phone_verified = CASE WHEN phone IS NOT NULL AND phone != '' THEN 1 ELSE 0 END,
    login_count = 0
WHERE user_type IS NULL;

-- 7. 创建管理员用户（如果不存在）
INSERT IGNORE INTO users (
    username, email, password, nickname, avatar, user_type, login_type, 
    phone_verified, status, created_at, updated_at, create_user, update_user, is_deleted
) VALUES (
    'superAdmin', 
    '<EMAIL>', 
    '$2a$10$N.zmdr9k7uOsxVQDApz2OuhrR6EdFHqiy.pVguKu5BylyaN1p6stu', -- admin123
    '超级管理员', 
    '👑', 
    2, -- 管理后台用户
    1, -- 用户名密码登录
    0, -- 不需要手机验证
    1, -- 正常状态
    NOW(), 
    NOW(), 
    1, 
    1, 
    0
);

-- 8. 创建测试C端用户（用于测试手机验证码登录）
INSERT IGNORE INTO users (
    username, email, password, nickname, avatar, phone, user_type, login_type, 
    phone_verified, status, created_at, updated_at, create_user, update_user, is_deleted
) VALUES (
    'testMobileUser', 
    '<EMAIL>', 
    '$2a$10$N.zmdr9k7uOsxVQDApz2OuhrR6EdFHqiy.pVguKu5BylyaN1p6stu', -- admin123
    '测试移动用户', 
    '📱', 
    '13800138000',
    1, -- C端用户
    2, -- 手机验证码登录
    1, -- 手机已验证
    1, -- 正常状态
    NOW(), 
    NOW(), 
    1, 
    1, 
    0
);

-- 9. 验证迁移结果
SELECT 'users表用户类型分布' as description;
SELECT 
    user_type,
    CASE user_type 
        WHEN 1 THEN 'C端用户(移动端)'
        WHEN 2 THEN '管理后台用户'
        ELSE '未知类型'
    END as user_type_desc,
    COUNT(*) as count
FROM users 
WHERE is_deleted = 0
GROUP BY user_type;

SELECT 'users表登录方式分布' as description;
SELECT 
    login_type,
    CASE login_type 
        WHEN 1 THEN '用户名密码'
        WHEN 2 THEN '手机验证码'
        ELSE '未知方式'
    END as login_type_desc,
    COUNT(*) as count
FROM users 
WHERE is_deleted = 0
GROUP BY login_type;

SELECT 'sms_verification_codes表' as table_name, COUNT(*) as total_records FROM sms_verification_codes;
SELECT 'user_login_logs表' as table_name, COUNT(*) as total_records FROM user_login_logs;

COMMIT;
