-- 小鹅养成APP - C端用户注册流程改进
-- 执行时间: 2025-01-06
-- 说明: 优化C端用户注册流程，支持分步骤注册

USE goose_app;

-- 1. 修改users表，password字段改为可空（仅影响新用户）
ALTER TABLE users MODIFY COLUMN password VARCHAR(255) NULL COMMENT '登录密码（C端用户可为空）';

-- 2. 添加注册状态字段（如果不存在）
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
     WHERE table_name = 'users'
     AND table_schema = 'goose_app'
     AND column_name = 'registration_status') = 0,
    'ALTER TABLE users ADD COLUMN registration_status TINYINT DEFAULT 2 COMMENT ''注册状态：1-已创建账户待设密码，2-注册完成''',
    'SELECT ''registration_status column already exists'' as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 3. 添加临时访问相关字段（如果不存在）
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
     WHERE table_name = 'users'
     AND table_schema = 'goose_app'
     AND column_name = 'temp_token') = 0,
    'ALTER TABLE users ADD COLUMN temp_token VARCHAR(255) COMMENT ''临时访问Token''',
    'SELECT ''temp_token column already exists'' as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
     WHERE table_name = 'users'
     AND table_schema = 'goose_app'
     AND column_name = 'temp_token_expires_at') = 0,
    'ALTER TABLE users ADD COLUMN temp_token_expires_at DATETIME COMMENT ''临时Token过期时间''',
    'SELECT ''temp_token_expires_at column already exists'' as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 4. 为新字段添加索引（如果不存在）
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS
     WHERE table_name = 'users'
     AND table_schema = 'goose_app'
     AND index_name = 'idx_registration_status') = 0,
    'ALTER TABLE users ADD INDEX idx_registration_status (registration_status)',
    'SELECT ''idx_registration_status already exists'' as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS
     WHERE table_name = 'users'
     AND table_schema = 'goose_app'
     AND index_name = 'idx_temp_token') = 0,
    'ALTER TABLE users ADD INDEX idx_temp_token (temp_token)',
    'SELECT ''idx_temp_token already exists'' as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS
     WHERE table_name = 'users'
     AND table_schema = 'goose_app'
     AND index_name = 'idx_temp_token_expires') = 0,
    'ALTER TABLE users ADD INDEX idx_temp_token_expires (temp_token_expires_at)',
    'SELECT ''idx_temp_token_expires already exists'' as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 5. 更新现有用户的注册状态（已有密码的用户标记为注册完成）
UPDATE users 
SET registration_status = 2 
WHERE password IS NOT NULL AND password != '';

-- 6. 创建临时Token清理表（用于定时清理过期Token）
CREATE TABLE IF NOT EXISTS temp_token_cleanup_log (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    cleanup_time DATETIME NOT NULL COMMENT '清理时间',
    cleaned_count INT DEFAULT 0 COMMENT '清理的Token数量',
    
    -- BaseEntity标准字段
    status TINYINT DEFAULT 1 COMMENT '状态：0-禁用，1-正常',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    create_user BIGINT COMMENT '创建用户ID',
    update_user BIGINT COMMENT '更新用户ID',
    is_deleted TINYINT DEFAULT 0 COMMENT '逻辑删除：0-未删除，1-已删除',
    
    INDEX idx_cleanup_time (cleanup_time),
    INDEX idx_is_deleted (is_deleted)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='临时Token清理日志表';

-- 7. 验证迁移结果
SELECT 'users表注册状态分布' as description;
SELECT 
    registration_status,
    CASE registration_status 
        WHEN 1 THEN '已创建账户待设密码'
        WHEN 2 THEN '注册完成'
        ELSE '未知状态'
    END as status_desc,
    COUNT(*) as count
FROM users 
WHERE is_deleted = 0
GROUP BY registration_status;

SELECT 'users表密码状态分布' as description;
SELECT
    CASE
        WHEN password IS NULL OR password = '' THEN '无密码'
        ELSE '有密码'
    END as password_status,
    COUNT(*) as count
FROM users
WHERE is_deleted = 0
GROUP BY CASE
    WHEN password IS NULL OR password = '' THEN '无密码'
    ELSE '有密码'
END;

-- 8. 创建测试数据：未完成注册的C端用户
INSERT IGNORE INTO users (
    username, email, phone, nickname, avatar, user_type, login_type, 
    phone_verified, registration_status, password, status, 
    created_at, updated_at, create_user, update_user, is_deleted
) VALUES (
    'temp_user_test', 
    '<EMAIL>', 
    '13800138999',
    '待设密码用户', 
    '⏳', 
    1, -- C端用户
    2, -- 手机验证码登录
    1, -- 手机已验证
    1, -- 已创建账户待设密码
    NULL, -- 无密码
    1, -- 正常状态
    NOW(), 
    NOW(), 
    1, 
    1, 
    0
);

COMMIT;
