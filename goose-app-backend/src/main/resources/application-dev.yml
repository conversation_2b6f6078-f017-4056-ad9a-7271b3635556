# 开发环境配置
server:
  port: 8080

spring:
  # 开发工具配置
  devtools:
    restart:
      enabled: true
      additional-paths: src/main/java
    livereload:
      enabled: true

  # 数据源配置
  datasource:
    url: ***************************************************************************************************************************************************
    username: root
    password: 123456

  # JPA配置
  jpa:
    hibernate:
      ddl-auto: update
    show-sql: true
    properties:
      hibernate:
        format_sql: true

  # Redis配置
  data:
    redis:
      host: localhost
      port: 6379
      database: 0

# 日志配置
logging:
  level:
    com.gooseapp: DEBUG
    org.springframework.web: DEBUG
    org.hibernate.SQL: DEBUG
    org.hibernate.type.descriptor.sql.BasicBinder: TRACE
    org.springframework.security: DEBUG

# Sa-Token开发环境配置
sa-token:
  is-log: true
  timeout: 86400  # 开发环境token有效期1天
  # 开发环境使用明文密钥
  jwt-secret-key: "coding"

# 开发环境业务配置
app:
  # 开发环境密码强度降低
  password:
    strength: 4

  # 开发环境互动冷却时间缩短（方便测试）
  business:
    interaction:
      feed-cooldown: 5    # 5分钟
      water-cooldown: 3   # 3分钟
      clean-cooldown: 10  # 10分钟
      play-cooldown: 2    # 2分钟
