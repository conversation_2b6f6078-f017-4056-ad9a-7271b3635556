package com.gooseapp.user.enums;

/**
 * 用户角色枚举
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-12-19
 */
public enum Role {
    
    /**
     * 管理员角色
     */
    ADMIN("ADMIN", "管理员", "拥有系统所有权限，可以创建小鹅、管理商品池等"),
    
    /**
     * 普通用户角色
     */
    USER("USER", "普通用户", "可以购买小鹅、养成小鹅、查看个人信息等");

    /**
     * 角色代码
     */
    private final String code;
    
    /**
     * 角色名称
     */
    private final String name;
    
    /**
     * 角色描述
     */
    private final String description;

    /**
     * 构造函数
     * 
     * @param code 角色代码
     * @param name 角色名称
     * @param description 角色描述
     */
    Role(String code, String name, String description) {
        this.code = code;
        this.name = name;
        this.description = description;
    }

    /**
     * 获取角色代码
     * 
     * @return 角色代码
     */
    public String getCode() {
        return code;
    }

    /**
     * 获取角色名称
     * 
     * @return 角色名称
     */
    public String getName() {
        return name;
    }

    /**
     * 获取角色描述
     * 
     * @return 角色描述
     */
    public String getDescription() {
        return description;
    }

    /**
     * 根据代码获取角色
     * 
     * @param code 角色代码
     * @return 角色枚举，如果未找到则返回USER
     */
    public static Role fromCode(String code) {
        if (code == null) {
            return USER;
        }
        
        for (Role role : values()) {
            if (role.code.equals(code)) {
                return role;
            }
        }
        return USER;
    }

    /**
     * 检查是否为管理员角色
     * 
     * @return 如果是管理员角色返回true，否则返回false
     */
    public boolean isAdmin() {
        return this == ADMIN;
    }

    /**
     * 检查是否为普通用户角色
     * 
     * @return 如果是普通用户角色返回true，否则返回false
     */
    public boolean isUser() {
        return this == USER;
    }

    @Override
    public String toString() {
        return String.format("Role{code='%s', name='%s', description='%s'}", code, name, description);
    }
}
