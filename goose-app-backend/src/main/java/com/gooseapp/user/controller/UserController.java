package com.gooseapp.user.controller;

import cn.dev33.satoken.annotation.SaCheckLogin;
import cn.dev33.satoken.stp.StpUtil;
import com.gooseapp.common.response.ApiResponse;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.gooseapp.common.response.ApiResponse;
import com.gooseapp.user.dto.LoginResponse;
import com.gooseapp.user.dto.UserProfileDTO;
import com.gooseapp.user.service.UserService;
import com.gooseapp.user.service.UserStatsService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 用户控制器
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-12-19
 */
@Slf4j
@RestController
@RequestMapping("/api/v1/users")
@RequiredArgsConstructor
@SaCheckLogin
@Tag(name = "用户管理", description = "用户信息管理相关接口")
public class UserController {

    private final UserService userService;
    private final UserStatsService userStatsService;

    /**
     * 获取用户详细信息
     */
    @GetMapping("/profile")
    @Operation(summary = "获取用户信息", description = "获取当前登录用户的详细信息")
    public ApiResponse<UserProfileDTO> getUserProfile() {
        Long userId = StpUtil.getLoginIdAsLong();
        
        log.info("获取用户信息: userId={}", userId);
        
        UserProfileDTO userProfile = userService.getUserProfile(userId);
        
        return ApiResponse.success("查询成功", userProfile);
    }

    /**
     * 更新用户信息
     */
    @PutMapping("/profile")
    @Operation(summary = "更新用户信息", description = "更新当前登录用户的个人信息")
    public ApiResponse<UserProfileDTO> updateUserProfile(
            @Valid @RequestBody UserProfileDTO profileDTO) {
        
        Long userId = StpUtil.getLoginIdAsLong();
        
        log.info("更新用户信息: userId={}", userId);
        
        UserProfileDTO updatedProfile = userService.updateUserProfile(userId, profileDTO);
        
        return ApiResponse.success("更新成功", updatedProfile);
    }

    /**
     * 获取用户统计信息
     */
    @GetMapping("/stats")
    @Operation(summary = "获取用户统计", description = "获取用户的小鹅养成统计信息")
    public ApiResponse<LoginResponse.UserStats> getUserStats() {
        Long userId = StpUtil.getLoginIdAsLong();
        
        log.info("获取用户统计: userId={}", userId);
        
        LoginResponse.UserStats stats = userStatsService.getUserStats(userId);
        
        return ApiResponse.success("查询成功", stats);
    }

    /**
     * 根据ID获取用户信息（公开信息）
     */
    @GetMapping("/{userId}")
    @Operation(summary = "获取指定用户信息", description = "根据用户ID获取用户的公开信息")
    public ApiResponse<UserPublicDTO> getUserById(
            @Parameter(description = "用户ID") @PathVariable Long userId) {
        
        log.info("获取用户公开信息: targetUserId={}, currentUserId={}", 
                userId, StpUtil.getLoginIdAsLong());
        
        UserProfileDTO userProfile = userService.getUserProfile(userId);
        
        // 转换为公开信息（隐藏敏感信息）
        UserPublicDTO publicDTO = convertToPublicDTO(userProfile);
        
        return ApiResponse.success("查询成功", publicDTO);
    }

    /**
     * 搜索用户
     */
    @GetMapping("/search")
    @Operation(summary = "搜索用户", description = "根据关键词搜索用户")
    public ApiResponse<IPage<UserPublicDTO>> searchUsers(
            @Parameter(description = "搜索关键词") @RequestParam String keyword,
            @Parameter(description = "页码") @RequestParam(defaultValue = "1") int page,
            @Parameter(description = "每页大小") @RequestParam(defaultValue = "10") int size) {
        
        log.info("搜索用户: keyword={}, page={}, size={}", keyword, page, size);
        
        IPage<UserProfileDTO> userProfilePage = userService.searchUsers(keyword, page, size);
        
        // 使用IPage.convert转换分页记录
        IPage<UserPublicDTO> publicUserPage = userProfilePage.convert(this::convertToPublicDTO);
        
        return ApiResponse.success("查询成功", publicUserPage);
    }

    /**
     * 获取用户排行榜
     */
    @GetMapping("/ranking")
    @Operation(summary = "获取用户排行榜", description = "获取用户养成排行榜")
    public ApiResponse<List<UserRankingDTO>> getUserRanking(
            @Parameter(description = "排行类型：geese-小鹅数量，days-养成天数") 
            @RequestParam(defaultValue = "geese") String type) {
        
        log.info("获取用户排行榜: type={}", type);
        
        // 这里暂时返回空列表，后续在小鹅模块中实现
        List<UserRankingDTO> ranking = List.of();
        
        return ApiResponse.success("查询成功", ranking);
    }

    /**
     * 转换为公开信息DTO
     */
    private UserPublicDTO convertToPublicDTO(UserProfileDTO profileDTO) {
        UserPublicDTO publicDTO = new UserPublicDTO();
        publicDTO.setId(profileDTO.getId());
        publicDTO.setUsername(profileDTO.getUsername());
        publicDTO.setNickname(profileDTO.getNickname());
        publicDTO.setAvatar(profileDTO.getAvatar());
        publicDTO.setGender(profileDTO.getGender());
        publicDTO.setGenderDescription(profileDTO.getGenderDescription());
        publicDTO.setTotalGeese(profileDTO.getTotalGeese());
        publicDTO.setTotalDays(profileDTO.getTotalDays());
        publicDTO.setCreatedAt(profileDTO.getCreatedAt());
        return publicDTO;
    }

    /**
     * 用户公开信息DTO
     */
    public static class UserPublicDTO {
        private Long id;
        private String username;
        private String nickname;
        private String avatar;
        private Integer gender;
        private String genderDescription;
        private Integer totalGeese;
        private Integer totalDays;
        private java.time.LocalDateTime createdAt;
        
        // Getters and Setters
        public Long getId() { return id; }
        public void setId(Long id) { this.id = id; }
        
        public String getUsername() { return username; }
        public void setUsername(String username) { this.username = username; }
        
        public String getNickname() { return nickname; }
        public void setNickname(String nickname) { this.nickname = nickname; }
        
        public String getAvatar() { return avatar; }
        public void setAvatar(String avatar) { this.avatar = avatar; }
        
        public Integer getGender() { return gender; }
        public void setGender(Integer gender) { this.gender = gender; }
        
        public String getGenderDescription() { return genderDescription; }
        public void setGenderDescription(String genderDescription) { this.genderDescription = genderDescription; }
        
        public Integer getTotalGeese() { return totalGeese; }
        public void setTotalGeese(Integer totalGeese) { this.totalGeese = totalGeese; }
        
        public Integer getTotalDays() { return totalDays; }
        public void setTotalDays(Integer totalDays) { this.totalDays = totalDays; }
        
        public java.time.LocalDateTime getCreatedAt() { return createdAt; }
        public void setCreatedAt(java.time.LocalDateTime createdAt) { this.createdAt = createdAt; }
    }

    /**
     * 用户排行榜DTO
     */
    public static class UserRankingDTO {
        private Long id;
        private String username;
        private String nickname;
        private String avatar;
        private Integer rank;
        private Integer value;
        private String type;
        
        // Getters and Setters
        public Long getId() { return id; }
        public void setId(Long id) { this.id = id; }
        
        public String getUsername() { return username; }
        public void setUsername(String username) { this.username = username; }
        
        public String getNickname() { return nickname; }
        public void setNickname(String nickname) { this.nickname = nickname; }
        
        public String getAvatar() { return avatar; }
        public void setAvatar(String avatar) { this.avatar = avatar; }
        
        public Integer getRank() { return rank; }
        public void setRank(Integer rank) { this.rank = rank; }
        
        public Integer getValue() { return value; }
        public void setValue(Integer value) { this.value = value; }
        
        public String getType() { return type; }
        public void setType(String type) { this.type = type; }
    }
}
