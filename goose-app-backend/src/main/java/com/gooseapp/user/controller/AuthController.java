package com.gooseapp.user.controller;

import cn.dev33.satoken.annotation.SaCheckLogin;
import cn.dev33.satoken.stp.StpUtil;
import com.gooseapp.common.response.ApiResponse;
import com.gooseapp.common.exception.BusinessException;
import com.gooseapp.user.dto.*;
import com.gooseapp.user.entity.User;
import com.gooseapp.user.service.UserService;
import com.gooseapp.user.service.SmsService;
import com.gooseapp.user.service.UserStatsService;
import java.time.LocalDateTime;
import java.util.Map;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

/**
 * 认证控制器
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-12-19
 */
@Slf4j
@RestController
@RequestMapping("/api/v1/auth")
@RequiredArgsConstructor
@Tag(name = "认证管理", description = "用户注册、登录、登出等认证相关接口")
public class AuthController {

    private final UserService userService;
    private final SmsService smsService;
    private final UserStatsService userStatsService;

    /**
     * 用户注册
     */
    @PostMapping("/register")
    @Operation(summary = "用户注册", description = "新用户注册账号")
    public ApiResponse<LoginResponse> register(
            @Valid @RequestBody UserRegisterDTO registerDTO) {
        
        log.info("用户注册请求: username={}, email={}", registerDTO.getUsername(), registerDTO.getEmail());
        
        // 注册用户
        User user = userService.register(registerDTO);
        
        // 自动登录
        UserLoginDTO loginDTO = new UserLoginDTO();
        loginDTO.setUsername(registerDTO.getUsername());
        loginDTO.setPassword(registerDTO.getPassword());
        LoginResponse loginResponse = userService.login(loginDTO);
        
        return ApiResponse.success("注册成功", loginResponse);
    }

    /**
     * 用户登录
     */
    @PostMapping("/login")
    @Operation(summary = "用户登录", description = "用户账号密码登录")
    public ApiResponse<LoginResponse> login(
            @Valid @RequestBody UserLoginDTO loginDTO) {
        
        log.info("用户登录请求: username={}", loginDTO.getUsername());
        
        LoginResponse loginResponse = userService.login(loginDTO);
        
        return ApiResponse.success("登录成功", loginResponse);
    }

    /**
     * 用户登出
     */
    @PostMapping("/logout")
    @SaCheckLogin
    @Operation(summary = "用户登出", description = "用户退出登录")
    public ApiResponse<Void> logout() {
        Long userId = StpUtil.getLoginIdAsLong();
        
        log.info("用户登出请求: userId={}", userId);
        
        userService.logout(userId);
        
        return ApiResponse.success("登出成功");
    }

    /**
     * 刷新Token
     */
    @PostMapping("/refresh")
    @SaCheckLogin
    @Operation(summary = "刷新Token", description = "刷新用户访问令牌")
    public ApiResponse<TokenRefreshResponse> refreshToken() {
        Long userId = StpUtil.getLoginIdAsLong();
        
        log.info("Token刷新请求: userId={}", userId);
        
        TokenRefreshResponse response = userService.refreshToken(userId);
        
        return ApiResponse.success("Token刷新成功", response);
    }

    /**
     * 获取当前用户信息
     */
    @GetMapping("/me")
    @SaCheckLogin
    @Operation(summary = "获取当前用户信息", description = "获取当前登录用户的详细信息")
    public ApiResponse<UserProfileDTO> getCurrentUser() {
        Long userId = StpUtil.getLoginIdAsLong();
        
        log.info("获取当前用户信息: userId={}", userId);
        
        UserProfileDTO userProfile = userService.getUserProfile(userId);
        
        return ApiResponse.success("查询成功", userProfile);
    }

    /**
     * 检查用户名是否可用
     */
    @GetMapping("/check-username")
    @Operation(summary = "检查用户名", description = "检查用户名是否已被使用")
    public ApiResponse<Boolean> checkUsername(
            @Parameter(description = "用户名") @RequestParam String username) {
        
        log.info("检查用户名: username={}", username);
        
        boolean exists = userService.existsByUsername(username);
        
        return ApiResponse.success("查询成功", !exists);
    }

    /**
     * 检查邮箱是否可用
     */
    @GetMapping("/check-email")
    @Operation(summary = "检查邮箱", description = "检查邮箱是否已被使用")
    public ApiResponse<Boolean> checkEmail(
            @Parameter(description = "邮箱") @RequestParam String email) {
        
        log.info("检查邮箱: email={}", email);
        
        boolean exists = userService.existsByEmail(email);
        
        return ApiResponse.success("查询成功", !exists);
    }

    /**
     * 检查手机号是否可用
     */
    @GetMapping("/check-phone")
    @Operation(summary = "检查手机号", description = "检查手机号是否已被使用")
    public ApiResponse<Boolean> checkPhone(
            @Parameter(description = "手机号") @RequestParam String phone) {
        
        log.info("检查手机号: phone={}", phone);
        
        boolean exists = userService.existsByPhone(phone);
        
        return ApiResponse.success("查询成功", !exists);
    }

    /**
     * 修改密码
     */
    @PostMapping("/change-password")
    @SaCheckLogin
    @Operation(summary = "修改密码", description = "用户修改登录密码")
    public ApiResponse<Void> changePassword(
            @Valid @RequestBody ChangePasswordDTO changePasswordDTO) {
        
        Long userId = StpUtil.getLoginIdAsLong();
        
        log.info("修改密码请求: userId={}", userId);
        
        userService.changePassword(userId, changePasswordDTO.getOldPassword(), 
                changePasswordDTO.getNewPassword());
        
        return ApiResponse.success("密码修改成功");
    }

    /**
     * 获取登录状态
     */
    @GetMapping("/status")
    @Operation(summary = "获取登录状态", description = "检查用户是否已登录")
    public ApiResponse<LoginStatusDTO> getLoginStatus() {
        boolean isLogin = StpUtil.isLogin();
        
        LoginStatusDTO status = new LoginStatusDTO();
        status.setIsLogin(isLogin);
        
        if (isLogin) {
            Long userId = StpUtil.getLoginIdAsLong();
            status.setUserId(userId);
            status.setTokenTimeout(StpUtil.getTokenTimeout());
        }
        
        return ApiResponse.success("查询成功", status);
    }

    /**
     * 发送短信验证码
     */
    @PostMapping("/send-sms-code")
    @Operation(summary = "发送短信验证码", description = "发送手机验证码用于登录或注册")
    public ApiResponse<Void> sendSmsCode(
            @Valid @RequestBody SendSmsCodeRequest request,
            jakarta.servlet.http.HttpServletRequest httpRequest) {

        String ipAddress = getClientIpAddress(httpRequest);
        String userAgent = httpRequest.getHeader("User-Agent");

        log.info("发送短信验证码请求: phone={}, codeType={}, ip={}",
                request.getPhone(), request.getCodeType(), ipAddress);

        smsService.sendSmsCode(request, ipAddress, userAgent);

        return ApiResponse.success("验证码发送成功");
    }

    /**
     * 验证短信验证码
     */
    @PostMapping("/verify-sms-code")
    @Operation(summary = "验证短信验证码", description = "验证手机验证码是否正确")
    public ApiResponse<Boolean> verifySmsCode(
            @Valid @RequestBody VerifySmsCodeRequest request) {

        log.info("验证短信验证码请求: phone={}, code={}, codeType={}",
                request.getPhone(), request.getCode(), request.getCodeType());

        boolean isValid = smsService.verifySmsCode(request);

        return ApiResponse.success("验证完成", isValid);
    }

    /**
     * 手机号验证码登录
     */
    @PostMapping("/login-by-phone")
    @Operation(summary = "手机号验证码登录", description = "使用手机号和验证码进行登录")
    public ApiResponse<LoginResponse> loginByPhone(
            @Valid @RequestBody PhoneLoginRequest request,
            jakarta.servlet.http.HttpServletRequest httpRequest) {

        String ipAddress = getClientIpAddress(httpRequest);
        String userAgent = httpRequest.getHeader("User-Agent");

        log.info("手机号验证码登录请求: phone={}, ip={}", request.getPhone(), ipAddress);

        LoginResponse loginResponse = userService.loginByPhone(
                request.getPhone(),
                request.getCode(),
                request.getDeviceInfo(),
                ipAddress,
                userAgent
        );

        return ApiResponse.success("登录成功", loginResponse);
    }

    /**
     * 手机号注册
     */
    @PostMapping("/register-by-phone")
    @Operation(summary = "手机号注册", description = "使用手机号和验证码进行注册")
    public ApiResponse<LoginResponse> registerByPhone(
            @Valid @RequestBody PhoneRegisterRequest request,
            jakarta.servlet.http.HttpServletRequest httpRequest) {

        String ipAddress = getClientIpAddress(httpRequest);
        String userAgent = httpRequest.getHeader("User-Agent");

        log.info("手机号注册请求: phone={}, ip={}", request.getPhone(), ipAddress);

        // 注册用户
        User user = userService.registerByPhone(
                request.getPhone(),
                request.getCode(),
                request.getProcessedNickname(),
                request.getDeviceInfo(),
                ipAddress,
                userAgent
        );

        // 注册成功后直接执行登录（不再验证验证码）
        StpUtil.login(user.getId());
        String token = StpUtil.getTokenValue();
        long expiresIn = StpUtil.getTokenTimeout();

        // 更新用户登录信息
        userService.updateUserLoginInfo(user.getId(), ipAddress);

        // 构建登录响应
        LoginResponse loginResponse = LoginResponse.builder()
                .userId(user.getId())
                .username(user.getUsername())
                .nickname(user.getNickname())
                .avatar(user.getAvatar())
                .email(user.getEmail())
                .token(token)
                .tokenType("Bearer")
                .expiresIn(expiresIn)
                .loginTime(LocalDateTime.now())
                .stats(userStatsService.getUserStats(user.getId()))
                .build();

        return ApiResponse.success("注册成功", loginResponse);
    }

    /**
     * 获取客户端IP地址
     */
    private String getClientIpAddress(jakarta.servlet.http.HttpServletRequest request) {
        String xForwardedFor = request.getHeader("X-Forwarded-For");
        if (xForwardedFor != null && !xForwardedFor.isEmpty() && !"unknown".equalsIgnoreCase(xForwardedFor)) {
            return xForwardedFor.split(",")[0].trim();
        }

        String xRealIp = request.getHeader("X-Real-IP");
        if (xRealIp != null && !xRealIp.isEmpty() && !"unknown".equalsIgnoreCase(xRealIp)) {
            return xRealIp;
        }

        return request.getRemoteAddr();
    }

    /**
     * C端用户创建账户（第一步：仅创建账户，不设置密码）
     */
    @PostMapping("/create-mobile-account")
    @Operation(summary = "C端用户创建账户", description = "使用手机号和验证码创建C端用户账户，不设置密码")
    public ApiResponse<MobileAccountResponse> createMobileAccount(
            @Valid @RequestBody CreateMobileAccountRequest request,
            jakarta.servlet.http.HttpServletRequest httpRequest) {

        String ipAddress = getClientIpAddress(httpRequest);
        String userAgent = httpRequest.getHeader("User-Agent");

        log.info("C端用户创建账户请求: phone={}, ip={}", request.getPhone(), ipAddress);

        MobileAccountResponse response = userService.createMobileAccount(
                request.getPhone(),
                request.getCode(),
                request.getProcessedNickname(),
                request.getDeviceInfo(),
                ipAddress,
                userAgent
        );

        return ApiResponse.success("账户创建成功", response);
    }

    /**
     * C端用户设置密码（第二步：设置登录密码并完成注册）
     */
    @PostMapping("/set-mobile-password")
    @Operation(summary = "C端用户设置密码", description = "为已创建的C端用户账户设置登录密码并完成注册")
    public ApiResponse<LoginResponse> setMobilePassword(
            @Valid @RequestBody SetMobilePasswordRequest request,
            jakarta.servlet.http.HttpServletRequest httpRequest) {

        String ipAddress = getClientIpAddress(httpRequest);
        String userAgent = httpRequest.getHeader("User-Agent");

        // 验证两次密码是否一致
        if (!request.isPasswordMatched()) {
            throw new BusinessException(6701, "两次输入的密码不一致");
        }

        // 从临时Token中获取用户ID（这里需要从请求头或参数中获取）
        String tempToken = httpRequest.getHeader("Temp-Token");
        if (tempToken == null || tempToken.isEmpty()) {
            throw new BusinessException(6701, "缺少临时访问Token");
        }

        // 验证临时Token
        if (!userService.isValidTempToken(tempToken)) {
            throw new BusinessException(6701, "临时Token无效或已过期");
        }

        // 获取用户
        User user = userService.findByTempToken(tempToken);

        log.info("C端用户设置密码请求: userId={}, ip={}", user.getId(), ipAddress);

        LoginResponse response = userService.setMobilePassword(
                user.getId(),
                request.getPassword(),
                request.getDeviceInfo(),
                ipAddress,
                userAgent
        );

        return ApiResponse.success("密码设置成功", response);
    }

    /**
     * 查询注册状态
     */
    @GetMapping("/registration-status")
    @Operation(summary = "查询注册状态", description = "根据临时Token查询C端用户的注册状态")
    public ApiResponse<Map<String, Object>> getRegistrationStatus(
            jakarta.servlet.http.HttpServletRequest httpRequest) {

        String tempToken = httpRequest.getHeader("Temp-Token");
        if (tempToken == null || tempToken.isEmpty()) {
            throw new BusinessException(6701, "缺少临时访问Token");
        }

        // 验证临时Token
        if (!userService.isValidTempToken(tempToken)) {
            throw new BusinessException(6701, "临时Token无效或已过期");
        }

        // 获取用户
        User user = userService.findByTempToken(tempToken);

        Map<String, Object> status = Map.of(
                "userId", user.getId(),
                "username", user.getUsername(),
                "nickname", user.getNickname(),
                "phone", user.getPhone(),
                "registrationStatus", user.getRegistrationStatus(),
                "registrationStatusDesc", user.getRegistrationStatusDescription(),
                "tempTokenExpiresAt", user.getTempTokenExpiresAt(),
                "needSetPassword", user.isAccountCreated()
        );

        return ApiResponse.success("查询成功", status);
    }

    /**
     * 修改密码请求DTO
     */
    public static class ChangePasswordDTO {
        @jakarta.validation.constraints.NotBlank(message = "原密码不能为空")
        private String oldPassword;
        
        @jakarta.validation.constraints.NotBlank(message = "新密码不能为空")
        @jakarta.validation.constraints.Size(min = 6, max = 20, message = "新密码长度必须在6-20个字符之间")
        private String newPassword;
        
        public String getOldPassword() {
            return oldPassword;
        }
        
        public void setOldPassword(String oldPassword) {
            this.oldPassword = oldPassword;
        }
        
        public String getNewPassword() {
            return newPassword;
        }
        
        public void setNewPassword(String newPassword) {
            this.newPassword = newPassword;
        }
    }

    /**
     * 登录状态DTO
     */
    public static class LoginStatusDTO {
        private Boolean isLogin;
        private Long userId;
        private Long tokenTimeout;
        
        public Boolean getIsLogin() {
            return isLogin;
        }
        
        public void setIsLogin(Boolean isLogin) {
            this.isLogin = isLogin;
        }
        
        public Long getUserId() {
            return userId;
        }
        
        public void setUserId(Long userId) {
            this.userId = userId;
        }
        
        public Long getTokenTimeout() {
            return tokenTimeout;
        }
        
        public void setTokenTimeout(Long tokenTimeout) {
            this.tokenTimeout = tokenTimeout;
        }
    }
}
