package com.gooseapp.user.service;

import com.gooseapp.user.dto.UserRoleDTO;
import com.gooseapp.user.entity.UserRole;
import com.gooseapp.user.enums.Role;

import java.util.List;
import java.util.Map;

/**
 * 用户角色服务接口
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-12-19
 */
public interface RoleService {

    /**
     * 为用户分配角色
     * 
     * @param userId 用户ID
     * @param role 角色
     * @return 用户角色信息
     */
    UserRoleDTO assignRole(Long userId, Role role);

    /**
     * 移除用户角色
     * 
     * @param userId 用户ID
     * @param role 角色
     */
    void removeRole(Long userId, Role role);

    /**
     * 获取用户的所有角色
     * 
     * @param userId 用户ID
     * @return 用户角色列表
     */
    List<UserRoleDTO> getUserRoles(Long userId);

    /**
     * 获取用户的主要角色
     * 
     * @param userId 用户ID
     * @return 主要角色，如果没有角色则返回USER
     */
    Role getUserPrimaryRole(Long userId);

    /**
     * 检查用户是否具有指定角色
     * 
     * @param userId 用户ID
     * @param role 角色
     * @return 如果用户具有该角色返回true，否则返回false
     */
    boolean hasRole(Long userId, Role role);

    /**
     * 检查用户是否为管理员
     * 
     * @param userId 用户ID
     * @return 如果用户是管理员返回true，否则返回false
     */
    boolean isUserAdmin(Long userId);

    /**
     * 检查用户是否为普通用户
     * 
     * @param userId 用户ID
     * @return 如果用户是普通用户返回true，否则返回false
     */
    boolean isUserNormal(Long userId);

    /**
     * 获取所有管理员用户ID列表
     * 
     * @return 管理员用户ID列表
     */
    List<Long> getAllAdminUserIds();

    /**
     * 获取拥有指定角色的用户ID列表
     * 
     * @param role 角色
     * @return 用户ID列表
     */
    List<Long> getUserIdsByRole(Role role);

    /**
     * 批量为用户分配角色
     * 
     * @param userIds 用户ID列表
     * @param role 角色
     * @return 成功分配的用户角色列表
     */
    List<UserRoleDTO> batchAssignRole(List<Long> userIds, Role role);

    /**
     * 批量移除用户角色
     * 
     * @param userIds 用户ID列表
     * @param role 角色
     */
    void batchRemoveRole(List<Long> userIds, Role role);

    /**
     * 清除用户的所有角色
     * 
     * @param userId 用户ID
     */
    void clearUserRoles(Long userId);

    /**
     * 为新用户初始化默认角色
     * 
     * @param userId 用户ID
     * @return 用户角色信息
     */
    UserRoleDTO initializeDefaultRole(Long userId);

    /**
     * 获取角色统计信息
     * 
     * @return 角色统计信息，key为角色名称，value为用户数量
     */
    Map<String, Long> getRoleStatistics();

    /**
     * 验证角色操作权限
     * 
     * @param operatorUserId 操作者用户ID
     * @param targetUserId 目标用户ID
     * @param targetRole 目标角色
     * @return 如果有权限返回true，否则返回false
     */
    boolean validateRoleOperationPermission(Long operatorUserId, Long targetUserId, Role targetRole);

    /**
     * 获取用户角色变更历史
     * 
     * @param userId 用户ID
     * @return 角色变更历史列表
     */
    List<UserRoleDTO> getUserRoleHistory(Long userId);
}
