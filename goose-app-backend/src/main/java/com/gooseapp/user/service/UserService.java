package com.gooseapp.user.service;

import com.gooseapp.user.dto.*;
import com.gooseapp.user.entity.User;
import com.baomidou.mybatisplus.core.metadata.IPage;

/**
 * 用户服务接口
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-12-19
 */
public interface UserService {

    /**
     * 用户注册
     * 
     * @param registerDTO 注册信息
     * @return 用户信息
     */
    User register(UserRegisterDTO registerDTO);

    /**
     * 用户登录
     * 
     * @param loginDTO 登录信息
     * @return 登录响应
     */
    LoginResponse login(UserLoginDTO loginDTO);

    /**
     * 用户登出
     * 
     * @param userId 用户ID
     */
    void logout(Long userId);

    /**
     * 刷新Token
     * 
     * @param userId 用户ID
     * @return Token刷新响应
     */
    TokenRefreshResponse refreshToken(Long userId);

    /**
     * 根据ID获取用户信息
     * 
     * @param userId 用户ID
     * @return 用户信息
     */
    User getUserById(Long userId);

    /**
     * 获取用户详细信息
     * 
     * @param userId 用户ID
     * @return 用户详细信息
     */
    UserProfileDTO getUserProfile(Long userId);

    /**
     * 更新用户信息
     * 
     * @param userId 用户ID
     * @param profileDTO 用户信息
     * @return 更新后的用户信息
     */
    UserProfileDTO updateUserProfile(Long userId, UserProfileDTO profileDTO);

    /**
     * 修改密码
     * 
     * @param userId 用户ID
     * @param oldPassword 旧密码
     * @param newPassword 新密码
     */
    void changePassword(Long userId, String oldPassword, String newPassword);

    /**
     * 重置密码
     * 
     * @param email 邮箱
     * @param newPassword 新密码
     */
    void resetPassword(String email, String newPassword);

    /**
     * 检查用户名是否存在
     * 
     * @param username 用户名
     * @return 是否存在
     */
    boolean existsByUsername(String username);

    /**
     * 检查邮箱是否存在
     * 
     * @param email 邮箱
     * @return 是否存在
     */
    boolean existsByEmail(String email);

    /**
     * 检查手机号是否存在
     * 
     * @param phone 手机号
     * @return 是否存在
     */
    boolean existsByPhone(String phone);

    /**
     * 根据用户名或邮箱查找用户
     * 
     * @param usernameOrEmail 用户名或邮箱
     * @return 用户信息
     */
    User findByUsernameOrEmail(String usernameOrEmail);

    /**
     * 验证用户密码
     * 
     * @param user 用户信息
     * @param password 密码
     * @return 是否验证通过
     */
    boolean validatePassword(User user, String password);

    /**
     * 禁用用户
     * 
     * @param userId 用户ID
     */
    void disableUser(Long userId);

    /**
     * 启用用户
     * 
     * @param userId 用户ID
     */
    void enableUser(Long userId);

    /**
     * 获取用户列表
     * 
     * @param page 页码
     * @param size 每页大小
     * @return 用户列表
     */
    IPage<UserProfileDTO> getUserList(int page, int size);

    /**
     * 搜索用户
     *
     * @param keyword 关键词
     * @param page 页码
     * @param size 每页大小
     * @return 用户列表
     */
    IPage<UserProfileDTO> searchUsers(String keyword, int page, int size);

    /**
     * 手机号验证码登录
     *
     * @param phone 手机号
     * @param code 验证码
     * @param deviceInfo 设备信息
     * @param ipAddress IP地址
     * @param userAgent 用户代理
     * @return 登录响应
     */
    LoginResponse loginByPhone(String phone, String code, String deviceInfo, String ipAddress, String userAgent);

    /**
     * 手机号注册
     *
     * @param phone 手机号
     * @param code 验证码
     * @param nickname 昵称
     * @param deviceInfo 设备信息
     * @param ipAddress IP地址
     * @param userAgent 用户代理
     * @return 用户信息
     */
    User registerByPhone(String phone, String code, String nickname, String deviceInfo, String ipAddress, String userAgent);

    /**
     * 根据手机号查找用户
     *
     * @param phone 手机号
     * @return 用户信息
     */
    User findByPhone(String phone);


    /**
     * 更新用户登录信息
     *
     * @param userId 用户ID
     * @param ipAddress IP地址
     */
    void updateUserLoginInfo(Long userId, String ipAddress);

    /**
     * C端用户创建账户（仅创建账户，不设置密码）
     *
     * @param phone 手机号
     * @param code 验证码
     * @param nickname 昵称
     * @param deviceInfo 设备信息
     * @param ipAddress IP地址
     * @param userAgent 用户代理
     * @return 账户响应信息
     */
    MobileAccountResponse createMobileAccount(String phone, String code, String nickname, String deviceInfo, String ipAddress, String userAgent);

    /**
     * C端用户设置密码
     *
     * @param userId 用户ID
     * @param password 密码
     * @param deviceInfo 设备信息
     * @param ipAddress IP地址
     * @param userAgent 用户代理
     * @return 登录响应
     */
    LoginResponse setMobilePassword(Long userId, String password, String deviceInfo, String ipAddress, String userAgent);

    /**
     * 根据临时Token查找用户
     *
     * @param tempToken 临时Token
     * @return 用户信息
     */
    User findByTempToken(String tempToken);

    /**
     * 验证临时Token是否有效
     *
     * @param tempToken 临时Token
     * @return 是否有效
     */
    boolean isValidTempToken(String tempToken);

    /**
     * 清理过期的临时Token
     */
    void cleanExpiredTempTokens();

}
