package com.gooseapp.user.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gooseapp.common.constant.ErrorCode;
import com.gooseapp.common.exception.BusinessException;
import com.gooseapp.user.dto.UserRoleDTO;
import com.gooseapp.user.entity.UserRole;
import com.gooseapp.user.enums.Role;
import com.gooseapp.user.mapper.UserMapper;
import com.gooseapp.user.mapper.UserRoleMapper;
import com.gooseapp.user.service.RoleService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 用户角色服务实现类 - MyBatis Plus版本
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-12-19
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class RoleServiceImpl extends ServiceImpl<UserRoleMapper, UserRole> implements RoleService {

    private final UserMapper userMapper;

    @Override
    @Transactional
    public UserRoleDTO assignRole(Long userId, Role role) {
        log.debug("为用户[{}]分配角色[{}]", userId, role);

        // 验证用户是否存在
        if (!userMapper.existsById(userId)) {
            throw new BusinessException(ErrorCode.USER_NOT_FOUND, "用户不存在");
        }

        // 检查用户是否已经拥有该角色
        if (baseMapper.existsByUserIdAndRoleName(userId, role)) {
            log.warn("用户[{}]已经拥有角色[{}]", userId, role);
            throw new BusinessException(ErrorCode.ROLE_ALREADY_EXISTS, "用户已经拥有该角色");
        }

        // 创建用户角色
        UserRole userRole = new UserRole(userId, role);
        // ✅ 使用ServiceImpl的save方法
        save(userRole);

        log.info("成功为用户[{}]分配角色[{}]", userId, role);
        return UserRoleDTO.fromEntity(userRole);
    }

    @Override
    @Transactional
    public void removeRole(Long userId, Role role) {
        log.debug("移除用户[{}]的角色[{}]", userId, role);

        // 检查用户是否拥有该角色
        if (!baseMapper.existsByUserIdAndRoleName(userId, role)) {
            throw new BusinessException(ErrorCode.ROLE_NOT_FOUND, "用户没有该角色");
        }

        // 如果是管理员角色，需要确保至少保留一个管理员
        if (role == Role.ADMIN) {
            long adminCount = baseMapper.countByRoleName(Role.ADMIN);
            if (adminCount <= 1) {
                throw new BusinessException(ErrorCode.OPERATION_NOT_ALLOWED, "系统至少需要保留一个管理员");
            }
        }

        baseMapper.deleteByUserIdAndRoleName(userId, role);
        log.info("成功移除用户[{}]的角色[{}]", userId, role);
    }

    @Override
    public List<UserRoleDTO> getUserRoles(Long userId) {
        List<UserRole> userRoles = baseMapper.findByUserId(userId);
        return userRoles.stream()
                .map(UserRoleDTO::fromEntity)
                .collect(Collectors.toList());
    }

    @Override
    public Role getUserPrimaryRole(Long userId) {
        List<UserRole> userRoles = baseMapper.findUserRolesByPriority(userId);
        
        if (userRoles.isEmpty()) {
            return Role.USER; // 默认为普通用户
        }
        
        return userRoles.get(0).getRoleName();
    }

    @Override
    public boolean hasRole(Long userId, Role role) {
        return baseMapper.existsByUserIdAndRoleName(userId, role);
    }

    @Override
    public boolean isUserAdmin(Long userId) {
        return baseMapper.isUserAdmin(userId);
    }

    @Override
    public boolean isUserNormal(Long userId) {
        return hasRole(userId, Role.USER) && !isUserAdmin(userId);
    }

    @Override
    public List<Long> getAllAdminUserIds() {
        return baseMapper.findAllAdminUserIds();
    }

    @Override
    public List<Long> getUserIdsByRole(Role role) {
        return baseMapper.findByRoleName(role)
                .stream()
                .map(UserRole::getUserId)
                .collect(Collectors.toList());
    }

    @Override
    @Transactional
    public List<UserRoleDTO> batchAssignRole(List<Long> userIds, Role role) {
        log.debug("批量为用户{}分配角色[{}]", userIds, role);

        List<UserRoleDTO> result = new ArrayList<>();
        
        for (Long userId : userIds) {
            try {
                // 验证用户是否存在
                if (!userMapper.existsById(userId)) {
                    log.warn("用户[{}]不存在，跳过角色分配", userId);
                    continue;
                }

                // 检查用户是否已经拥有该角色
                if (baseMapper.existsByUserIdAndRoleName(userId, role)) {
                    log.debug("用户[{}]已经拥有角色[{}]，跳过", userId, role);
                    continue;
                }

                // 创建用户角色
                UserRole userRole = new UserRole(userId, role);
                // ✅ 使用ServiceImpl的save方法
                save(userRole);
                result.add(UserRoleDTO.fromEntity(userRole));

            } catch (Exception e) {
                log.error("为用户[{}]分配角色[{}]失败: {}", userId, role, e.getMessage());
            }
        }

        log.info("批量角色分配完成，成功分配{}个用户", result.size());
        return result;
    }

    @Override
    @Transactional
    public void batchRemoveRole(List<Long> userIds, Role role) {
        log.debug("批量移除用户{}的角色[{}]", userIds, role);

        for (Long userId : userIds) {
            try {
                if (baseMapper.existsByUserIdAndRoleName(userId, role)) {
                    // 如果是管理员角色，需要特殊检查
                    if (role == Role.ADMIN) {
                        long adminCount = baseMapper.countByRoleName(Role.ADMIN);
                        if (adminCount <= userIds.size()) {
                            log.warn("无法移除所有管理员角色，系统至少需要保留一个管理员");
                            continue;
                        }
                    }
                    
                    baseMapper.deleteByUserIdAndRoleName(userId, role);
                    log.debug("成功移除用户[{}]的角色[{}]", userId, role);
                }
            } catch (Exception e) {
                log.error("移除用户[{}]的角色[{}]失败: {}", userId, role, e.getMessage());
            }
        }

        log.info("批量角色移除完成");
    }

    @Override
    @Transactional
    public void clearUserRoles(Long userId) {
        log.debug("清除用户[{}]的所有角色", userId);

        // 检查是否是最后一个管理员
        if (isUserAdmin(userId)) {
            long adminCount = baseMapper.countByRoleName(Role.ADMIN);
            if (adminCount <= 1) {
                throw new BusinessException(ErrorCode.OPERATION_NOT_ALLOWED, "不能清除最后一个管理员的角色");
            }
        }

        baseMapper.deleteByUserId(userId);
        log.info("成功清除用户[{}]的所有角色", userId);
    }

    @Override
    @Transactional
    public UserRoleDTO initializeDefaultRole(Long userId) {
        log.debug("为用户[{}]初始化默认角色", userId);

        // 验证用户是否存在
        if (!userMapper.existsById(userId)) {
            throw new BusinessException(ErrorCode.USER_NOT_FOUND, "用户不存在");
        }

        // 检查用户是否已经有角色
        List<UserRole> existingRoles = baseMapper.findByUserId(userId);
        if (!existingRoles.isEmpty()) {
            log.debug("用户[{}]已经有角色，无需初始化", userId);
            return UserRoleDTO.fromEntity(existingRoles.get(0));
        }

        // 创建默认的USER角色
        UserRole userRole = new UserRole(userId, Role.USER);
        // ✅ 使用ServiceImpl的save方法
        save(userRole);

        log.info("成功为用户[{}]初始化默认角色[USER]", userId);
        return UserRoleDTO.fromEntity(userRole);
    }

    @Override
    public Map<String, Long> getRoleStatistics() {
        Map<String, Long> statistics = new HashMap<>();
        
        // 获取每个角色的用户数量
        for (Role role : Role.values()) {
            long count = baseMapper.countByRoleName(role);
            statistics.put(role.getName(), count);
        }
        
        return statistics;
    }

    @Override
    public boolean validateRoleOperationPermission(Long operatorUserId, Long targetUserId, Role targetRole) {
        // 不能操作自己的角色
        if (operatorUserId.equals(targetUserId)) {
            return false;
        }
        
        // 只有管理员才能操作角色
        if (!isUserAdmin(operatorUserId)) {
            return false;
        }
        
        // 管理员可以操作所有角色
        return true;
    }

    @Override
    public List<UserRoleDTO> getUserRoleHistory(Long userId) {
        // 由于当前设计没有历史表，返回当前角色列表
        // 在实际项目中，可以考虑添加用户角色变更历史表
        return getUserRoles(userId);
    }
}
