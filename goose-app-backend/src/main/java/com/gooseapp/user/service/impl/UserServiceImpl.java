package com.gooseapp.user.service.impl;

import cn.dev33.satoken.stp.StpUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.gooseapp.common.constant.ErrorCode;
import com.gooseapp.common.exception.BusinessException;
import com.gooseapp.common.utils.PasswordUtils;
import com.gooseapp.user.dto.*;
import com.gooseapp.user.entity.User;
import com.gooseapp.user.entity.UserLoginLog;
import com.gooseapp.user.entity.SmsVerificationCode;
import com.gooseapp.user.mapper.UserMapper;
import com.gooseapp.user.service.UserLoginLogService;
import com.gooseapp.user.service.UserService;
import com.gooseapp.user.service.SmsService;
import com.gooseapp.user.service.UserStatsService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 用户服务实现类 - MyBatis Plus版本
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-12-19
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class UserServiceImpl implements UserService {

    private final PasswordUtils passwordUtils;
    private final SmsService smsService;
    private final UserMapper userMapper;
    private final UserStatsService userStatsService;
    private final UserLoginLogService userLoginLogService;

    @Override
    @Transactional
    public User register(UserRegisterDTO registerDTO) {
        log.info("用户注册开始: username={}, email={}", registerDTO.getUsername(), registerDTO.getEmail());
        
        // 验证密码一致性
        if (!registerDTO.isPasswordMatch()) {
            throw new BusinessException(ErrorCode.PASSWORD_NOT_MATCH);
        }
        
        // 验证密码格式
        if (!passwordUtils.isValidPassword(registerDTO.getPassword())) {
            throw new BusinessException(ErrorCode.VALIDATION_ERROR, "密码格式不正确，必须包含字母和数字，长度6-20位");
        }
        
        // 检查用户名是否已存在
        if (existsByUsername(registerDTO.getUsername())) {
            throw new BusinessException(ErrorCode.USERNAME_ALREADY_EXISTS);
        }
        
        // 检查邮箱是否已存在
        if (existsByEmail(registerDTO.getEmail())) {
            throw new BusinessException(ErrorCode.EMAIL_ALREADY_EXISTS);
        }
        
        // 检查手机号是否已存在（如果提供）
        if (registerDTO.getPhone() != null && !registerDTO.getPhone().isEmpty()) {
            if (existsByPhone(registerDTO.getPhone())) {
                throw new BusinessException(ErrorCode.PHONE_ALREADY_EXISTS);
            }
        }
        
        // 创建用户实体
        User user = new User();
        user.setUsername(registerDTO.getUsername());
        user.setEmail(registerDTO.getEmail());
        user.setPassword(passwordUtils.encode(registerDTO.getPassword()));
        user.setNickname(registerDTO.getProcessedNickname());
        user.setPhone(registerDTO.getPhone());
        user.setGender(registerDTO.getGender());
        user.setStatus(User.Status.NORMAL.getCode());
        
        // 设置默认头像
        user.setAvatar("👤");
        
        userMapper.insert(user);

        log.info("用户注册成功: userId={}, username={}", user.getId(), user.getUsername());
        return user;
    }

    @Override
    @Transactional
    public LoginResponse login(UserLoginDTO loginDTO) {
        log.info("用户登录开始: username={}", loginDTO.getUsername());
        
        // 查找用户
        User user = findByUsernameOrEmail(loginDTO.getUsername());
        if (user == null) {
            throw new BusinessException(ErrorCode.USER_NOT_FOUND);
        }
        
        // 检查用户状态
        if (user.isDisabled()) {
            throw new BusinessException(ErrorCode.USER_DISABLED);
        }
        
        // 验证密码
        if (!validatePassword(user, loginDTO.getPassword())) {
            throw new BusinessException(ErrorCode.PASSWORD_ERROR);
        }
        
        // 执行登录
        StpUtil.login(user.getId());
        String token = StpUtil.getTokenValue();
        long expiresIn = StpUtil.getTokenTimeout();
        
        // 构建响应
        LoginResponse response = LoginResponse.builder()
                .userId(user.getId())
                .username(user.getUsername())
                .nickname(user.getNickname())
                .avatar(user.getAvatar())
                .email(user.getEmail())
                .token(token)
                .tokenType("Bearer")
                .expiresIn(expiresIn)
                .loginTime(LocalDateTime.now())
                .stats(userStatsService.getUserStats(user.getId()))
                .build();
        
        log.info("用户登录成功: userId={}, username={}", user.getId(), user.getUsername());
        return response;
    }

    @Override
    @Transactional
    public void logout(Long userId) {
        log.info("用户登出: userId={}", userId);
        
        // 执行登出
        StpUtil.logout(userId);
        
        log.info("用户登出成功: userId={}", userId);
    }

    @Override
    public TokenRefreshResponse refreshToken(Long userId) {
        log.info("刷新Token: userId={}", userId);
        
        // 检查用户是否存在
        User user = getUserById(userId);
        if (user.isDisabled()) {
            throw new BusinessException(ErrorCode.USER_DISABLED);
        }
        
        // 刷新Token
        StpUtil.renewTimeout(2592000); // 30天
        String newToken = StpUtil.getTokenValue();
        long expiresIn = StpUtil.getTokenTimeout();
        
        TokenRefreshResponse response = TokenRefreshResponse.builder()
                .token(newToken)
                .tokenType("Bearer")
                .expiresIn(expiresIn)
                .refreshTime(LocalDateTime.now())
                .build();
        
        log.info("Token刷新成功: userId={}", userId);
        return response;
    }

    @Override
    public User getUserById(Long userId) {
        User user = userMapper.selectById(userId);
        if (user == null) {
            throw new BusinessException(ErrorCode.USER_NOT_FOUND);
        }
        return user;
    }

    @Override
    public UserProfileDTO getUserProfile(Long userId) {
        User user = getUserById(userId);
        return convertToProfileDTO(user);
    }

    @Override
    @Transactional
    public UserProfileDTO updateUserProfile(Long userId, UserProfileDTO profileDTO) {
        log.info("更新用户信息: userId={}", userId);
        
        User user = getUserById(userId);
        
        // 更新允许修改的字段
        if (profileDTO.getNickname() != null) {
            user.setNickname(profileDTO.getNickname());
        }
        if (profileDTO.getAvatar() != null) {
            user.setAvatar(profileDTO.getAvatar());
        }
        if (profileDTO.getPhone() != null) {
            // 检查手机号是否已被其他用户使用
            if (!profileDTO.getPhone().equals(user.getPhone()) && existsByPhone(profileDTO.getPhone())) {
                throw new BusinessException(ErrorCode.PHONE_ALREADY_EXISTS);
            }
            user.setPhone(profileDTO.getPhone());
        }
        if (profileDTO.getGender() != null) {
            user.setGender(profileDTO.getGender());
        }
        if (profileDTO.getBirthday() != null) {
            user.setBirthday(profileDTO.getBirthday());
        }
        
        userMapper.updateById(user);
        
        log.info("用户信息更新成功: userId={}", userId);
        return convertToProfileDTO(user);
    }

    @Override
    @Transactional
    public void changePassword(Long userId, String oldPassword, String newPassword) {
        log.info("修改密码: userId={}", userId);
        
        User user = getUserById(userId);
        
        // 验证旧密码
        if (!validatePassword(user, oldPassword)) {
            throw new BusinessException(ErrorCode.PASSWORD_ERROR, "旧密码错误");
        }
        
        // 验证新密码格式
        if (!passwordUtils.isValidPassword(newPassword)) {
            throw new BusinessException(ErrorCode.VALIDATION_ERROR, "新密码格式不正确，必须包含字母和数字，长度6-20位");
        }
        
        // 检查新密码不能与旧密码相同
        if (passwordUtils.matches(newPassword, user.getPassword())) {
            throw new BusinessException(ErrorCode.VALIDATION_ERROR, "新密码不能与旧密码相同");
        }
        
        // 更新密码
        user.setPassword(passwordUtils.encode(newPassword));
        userMapper.updateById(user);
        
        log.info("密码修改成功: userId={}", userId);
    }

    @Override
    @Transactional
    public void resetPassword(String email, String newPassword) {
        log.info("重置密码: email={}", email);
        
        // 根据邮箱查找用户
        // 注意：findByEmail 是一个自定义方法，它应该在UserMapper.xml或通过注解定义
        User user = userMapper.findByEmail(email).orElse(null);
        if (user == null) {
            throw new BusinessException(ErrorCode.USER_NOT_FOUND, "用户不存在");
        }
        
        // 验证新密码格式
        if (!passwordUtils.isValidPassword(newPassword)) {
            throw new BusinessException(ErrorCode.VALIDATION_ERROR, "密码格式不正确，必须包含字母和数字，长度6-20位");
        }
        
        // 更新密码
        user.setPassword(passwordUtils.encode(newPassword));
        userMapper.updateById(user);
        
        log.info("密码重置成功: email={}, userId={}", email, user.getId());
    }

    @Override
    public boolean existsByUsername(String username) {
        return userMapper.existsByUsername(username);
    }

    @Override
    public boolean existsByEmail(String email) {
        return userMapper.existsByEmail(email);
    }

    @Override
    public boolean existsByPhone(String phone) {
        if (phone == null || phone.isEmpty()) {
            return false;
        }
        return userMapper.existsByPhone(phone);
    }

    @Override
    public User findByUsernameOrEmail(String usernameOrEmail) {
        return userMapper.findByUsernameOrEmail(usernameOrEmail, usernameOrEmail).orElse(null);
    }

    @Override
    public boolean validatePassword(User user, String password) {
        return passwordUtils.matches(password, user.getPassword());
    }

    @Override
    @Transactional
    public void disableUser(Long userId) {
        log.info("禁用用户: userId={}", userId);
        
        User user = getUserById(userId);
        user.setStatus(User.Status.DISABLED.getCode());
        userMapper.updateById(user);
        
        // 强制下线用户
        StpUtil.logout(userId);
        
        log.info("用户禁用成功: userId={}", userId);
    }

    @Override
    @Transactional
    public void enableUser(Long userId) {
        log.info("启用用户: userId={}", userId);
        
        User user = getUserById(userId);
        user.setStatus(User.Status.NORMAL.getCode());
        userMapper.updateById(user);
        
        log.info("用户启用成功: userId={}", userId);
    }

    @Override
    public IPage<UserProfileDTO> getUserList(int page, int size) {
        Page<User> userPage = new Page<>(page, size);
        IPage<User> resultPage = userMapper.findAllWithPagination(userPage);

        return resultPage.convert(this::convertToProfileDTO);
    }

    @Override
    public IPage<UserProfileDTO> searchUsers(String keyword, int page, int size) {
        Page<User> userPage = new Page<>(page, size);
        IPage<User> resultPage = userMapper.searchUsersPage(userPage, keyword);

        return resultPage.convert(this::convertToProfileDTO);
    }


    @Override
    @Transactional
    public LoginResponse loginByPhone(String phone, String code, String deviceInfo, String ipAddress, String userAgent) {
        log.info("手机号验证码登录开始: phone={}", phone);

        // 验证短信验证码
        if (!smsService.verifyAndConsumeSmsCode(phone, code, SmsVerificationCode.CodeType.LOGIN.getCode())) {
            // 记录失败日志
            User user = findByPhone(phone);
            if (user != null) {
                userLoginLogService.recordLoginLog(user.getId(), User.LoginType.PHONE_SMS.getCode(),
                        UserLoginLog.LoginResult.FAILURE.getCode(), ipAddress, userAgent, deviceInfo, "验证码错误");
            }
            throw new BusinessException(ErrorCode.VALIDATION_ERROR, "验证码错误或已过期");
        }

        // 查找用户
        User user = findByPhone(phone);
        if (user == null) {
            throw new BusinessException(ErrorCode.USER_NOT_FOUND, "手机号未注册");
        }

        // 检查用户状态
        if (user.isDisabled()) {
            userLoginLogService.recordLoginLog(user.getId(), User.LoginType.PHONE_SMS.getCode(),
                    UserLoginLog.LoginResult.FAILURE.getCode(), ipAddress, userAgent, deviceInfo, "用户已被禁用");
            throw new BusinessException(ErrorCode.USER_DISABLED);
        }

        // 执行登录
        StpUtil.login(user.getId());
        String token = StpUtil.getTokenValue();
        long expiresIn = StpUtil.getTokenTimeout();

        // 更新用户登录信息
        updateUserLoginInfo(user.getId(), ipAddress);

        // 记录成功登录日志
        userLoginLogService.recordLoginLog(user.getId(), User.LoginType.PHONE_SMS.getCode(),
                UserLoginLog.LoginResult.SUCCESS.getCode(), ipAddress, userAgent, deviceInfo, null);

        // 构建响应
        LoginResponse response = LoginResponse.builder()
                .userId(user.getId())
                .username(user.getUsername())
                .nickname(user.getNickname())
                .avatar(user.getAvatar())
                .email(user.getEmail())
                .token(token)
                .tokenType("Bearer")
                .expiresIn(expiresIn)
                .loginTime(LocalDateTime.now())
                .stats(userStatsService.getUserStats(user.getId()))
                .build();

        log.info("手机号验证码登录成功: phone={}, userId={}", phone, user.getId());
        return response;
    }

    @Override
    @Transactional
    public User registerByPhone(String phone, String code, String nickname, String deviceInfo, String ipAddress, String userAgent) {
        log.info("手机号注册开始: phone={}", phone);

        // 验证短信验证码
        if (!smsService.verifyAndConsumeSmsCode(phone, code, SmsVerificationCode.CodeType.REGISTER.getCode())) {
            throw new BusinessException(ErrorCode.VALIDATION_ERROR, "验证码错误或已过期");
        }

        // 检查手机号是否已注册
        if (existsByPhone(phone)) {
            throw new BusinessException(ErrorCode.PHONE_ALREADY_EXISTS);
        }

        // 创建用户实体
        User user = new User();
        user.setPhone(phone);
        user.setUsername("user_" + phone); // 使用手机号生成用户名
        user.setPassword(passwordUtils.encode("123456")); // 设置默认密码，手机登录用户不需要密码
        user.setEmail(phone + "@mobile.gooseapp.com"); // 生成虚拟邮箱
        user.setNickname(nickname != null && !nickname.trim().isEmpty() ?
                nickname.trim() : "小鹅用户" + phone.substring(phone.length() - 4));
        user.setUserType(User.UserType.MOBILE_USER.getCode());
        user.setLoginType(User.LoginType.PHONE_SMS.getCode());
        user.setPhoneVerified(1);
        user.setStatus(User.Status.NORMAL.getCode());
        user.setAvatar("📱");

        // 保存用户
        userMapper.insert(user);

        // 更新用户登录信息
        updateUserLoginInfo(user.getId(), ipAddress);

        // 记录注册登录日志
        userLoginLogService.recordLoginLog(user.getId(), User.LoginType.PHONE_SMS.getCode(),
                UserLoginLog.LoginResult.SUCCESS.getCode(), ipAddress, userAgent, deviceInfo, null);

        log.info("手机号注册成功: phone={}, userId={}", phone, user.getId());
        return user;
    }

    @Override
    public User findByPhone(String phone) {
        if (phone == null || phone.isEmpty()) {
            return null;
        }
        return userMapper.findByPhone(phone).orElse(null);
    }


    @Override
    @Transactional
    public void updateUserLoginInfo(Long userId, String ipAddress) {
        User user = getUserById(userId);
        user.setLastLoginTime(LocalDateTime.now());
        user.setLastLoginIp(ipAddress);
        user.setLoginCount(user.getLoginCount() + 1);

        userMapper.updateById(user);

        log.debug("用户登录信息更新成功: userId={}, loginCount={}", userId, user.getLoginCount());
    }

    @Override
    @Transactional
    public MobileAccountResponse createMobileAccount(String phone, String code, String nickname, String deviceInfo, String ipAddress, String userAgent) {
        log.info("C端用户创建账户开始: phone={}", phone);

        // 验证短信验证码
        if (!smsService.verifyAndConsumeSmsCode(phone, code, SmsVerificationCode.CodeType.REGISTER.getCode())) {
            throw new BusinessException(6701, "验证码错误或已过期");
        }

        // 检查手机号是否已注册
        if (existsByPhone(phone)) {
            throw new BusinessException(ErrorCode.PHONE_ALREADY_EXISTS);
        }

        // 创建用户实体（不设置密码）
        User user = new User();
        user.setPhone(phone);
        user.setUsername("user_" + phone);
        user.setEmail(phone + "@mobile.gooseapp.com");
        user.setNickname(nickname != null && !nickname.trim().isEmpty() ?
                nickname.trim() : "小鹅用户" + phone.substring(phone.length() - 4));
        user.setUserType(User.UserType.MOBILE_USER.getCode());
        user.setLoginType(User.LoginType.PHONE_SMS.getCode());
        user.setPhoneVerified(1);
        user.setRegistrationStatus(User.RegistrationStatus.ACCOUNT_CREATED.getCode()); // 待设密码状态
        user.setStatus(User.Status.NORMAL.getCode());
        user.setAvatar("📱");

        // 生成临时Token
        user.generateTempToken();

        // 保存用户
        userMapper.insert(user);

        // 记录创建账户日志
        userLoginLogService.recordLoginLog(user.getId(), User.LoginType.PHONE_SMS.getCode(),
                UserLoginLog.LoginResult.SUCCESS.getCode(), ipAddress, userAgent, deviceInfo, "创建账户");

        // 构建响应
        MobileAccountResponse response = MobileAccountResponse.builder()
                .userId(user.getId())
                .username(user.getUsername())
                .nickname(user.getNickname())
                .avatar(user.getAvatar())
                .email(user.getEmail())
                .phone(user.getPhone())
                .registrationStatus(user.getRegistrationStatus())
                .registrationStatusDesc(user.getRegistrationStatusDescription())
                .tempToken(user.getTempToken())
                .tempTokenExpiresAt(user.getTempTokenExpiresAt())
                .createdAt(user.getCreatedAt())
                .stats(userStatsService.getUserStats(user.getId()))
                .build();

        log.info("C端用户创建账户成功: phone={}, userId={}, tempToken={}", phone, user.getId(), user.getTempToken());
        return response;
    }

    @Override
    @Transactional
    public LoginResponse setMobilePassword(Long userId, String password, String deviceInfo, String ipAddress, String userAgent) {
        log.info("C端用户设置密码开始: userId={}", userId);

        // 获取用户
        User user = getUserById(userId);

        // 验证用户状态
        if (!user.isMobileUser()) {
            throw new BusinessException(6701, "只有C端用户可以使用此功能");
        }

        if (!user.isAccountCreated()) {
            throw new BusinessException(6701, "用户状态错误，无法设置密码");
        }

        // 设置密码
        user.setPassword(passwordUtils.encode(password));
        user.setRegistrationStatus(User.RegistrationStatus.REGISTRATION_COMPLETED.getCode());

        // 清除临时Token
        user.clearTempToken();

        // 更新用户
        userMapper.updateById(user);

        // 执行登录
        StpUtil.login(user.getId());
        String token = StpUtil.getTokenValue();
        long expiresIn = StpUtil.getTokenTimeout();

        // 更新用户登录信息
        updateUserLoginInfo(user.getId(), ipAddress);

        // 记录设置密码登录日志
        userLoginLogService.recordLoginLog(user.getId(), User.LoginType.PHONE_SMS.getCode(),
                UserLoginLog.LoginResult.SUCCESS.getCode(), ipAddress, userAgent, deviceInfo, "设置密码完成注册");

        // 构建响应
        LoginResponse response = LoginResponse.builder()
                .userId(user.getId())
                .username(user.getUsername())
                .nickname(user.getNickname())
                .avatar(user.getAvatar())
                .email(user.getEmail())
                .token(token)
                .tokenType("Bearer")
                .expiresIn(expiresIn)
                .loginTime(LocalDateTime.now())
                .stats(userStatsService.getUserStats(user.getId()))
                .build();

        log.info("C端用户设置密码成功: userId={}", userId);
        return response;
    }

    @Override
    public User findByTempToken(String tempToken) {
        if (tempToken == null || tempToken.isEmpty()) {
            return null;
        }

        LambdaQueryWrapper<User> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(User::getTempToken, tempToken)
                   .eq(User::getIsDeleted, 0);

        return userMapper.selectOne(queryWrapper);
    }

    @Override
    public boolean isValidTempToken(String tempToken) {
        User user = findByTempToken(tempToken);
        return user != null && user.isTempTokenValid();
    }

    @Override
    @Transactional
    public void cleanExpiredTempTokens() {
        log.info("开始清理过期的临时Token");

        LambdaQueryWrapper<User> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.isNotNull(User::getTempToken)
                   .lt(User::getTempTokenExpiresAt, LocalDateTime.now())
                   .eq(User::getIsDeleted, 0);

        List<User> expiredUsers = userMapper.selectList(queryWrapper);

        if (!expiredUsers.isEmpty()) {
            expiredUsers.forEach(user -> {
                user.clearTempToken();
                userMapper.updateById(user);
            });

            log.info("清理过期临时Token完成: count={}", expiredUsers.size());
        } else {
            log.info("没有需要清理的过期临时Token");
        }
    }

    private UserProfileDTO convertToProfileDTO(User user) {
        UserProfileDTO profile = new UserProfileDTO();
        BeanUtils.copyProperties(user, profile);
        return profile;
    }
}
