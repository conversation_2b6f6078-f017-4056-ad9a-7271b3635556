package com.gooseapp.user.service;

import com.gooseapp.user.dto.LoginResponse;

/**
 * 用户统计服务接口
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-06-12
 */
public interface UserStatsService {

    /**
     * 获取用户统计信息
     *
     * @param userId 用户ID
     * @return 用户统计信息 DTO
     */
    LoginResponse.UserStats getUserStats(Long userId);

    /**
     * 更新用户统计信息
     *
     * @param userId      用户ID
     * @param totalGeese  小鹅总数
     * @param totalDays   总天数
     * @param totalFeeds  总喂食数
     */
    void updateUserStats(Long userId, Integer totalGeese, Integer totalDays, Integer totalFeeds);
}