package com.gooseapp.user.service;

/**
 * 用户登录日志服务接口
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-06-12
 */
public interface UserLoginLogService {

    /**
     * 记录登录日志
     *
     * @param userId        用户ID
     * @param loginType     登录方式
     * @param loginResult   登录结果
     * @param ipAddress     IP地址
     * @param userAgent     用户代理
     * @param deviceInfo    设备信息
     * @param failureReason 失败原因
     */
    void recordLoginLog(Long userId, Integer loginType, Integer loginResult, String ipAddress, String userAgent, String deviceInfo, String failureReason);
}