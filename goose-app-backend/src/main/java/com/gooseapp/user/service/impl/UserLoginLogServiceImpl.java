package com.gooseapp.user.service.impl;

import com.gooseapp.user.entity.UserLoginLog;
import com.gooseapp.user.mapper.UserLoginLogMapper;
import com.gooseapp.user.service.UserLoginLogService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;

/**
 * 用户登录日志服务实现类
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-06-12
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class UserLoginLogServiceImpl implements UserLoginLogService {

    private final UserLoginLogMapper userLoginLogMapper;

    @Override
    @Transactional
    public void recordLoginLog(Long userId, Integer loginType, Integer loginResult, String ipAddress, String userAgent, String deviceInfo, String failureReason) {
        UserLoginLog loginLog = new UserLoginLog();
        loginLog.setUserId(userId);
        loginLog.setLoginType(loginType);
        loginLog.setLoginTime(LocalDateTime.now());
        loginLog.setIpAddress(ipAddress);
        loginLog.setUserAgent(userAgent);
        loginLog.setDeviceInfo(deviceInfo);
        loginLog.setLoginResult(loginResult);
        loginLog.setFailureReason(failureReason);

        userLoginLogMapper.insert(loginLog);

        log.debug("登录日志记录成功: userId={}, loginType={}, result={}", userId, loginType, loginResult);
    }
}