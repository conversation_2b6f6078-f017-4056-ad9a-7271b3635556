package com.gooseapp.user.service;

import com.gooseapp.user.dto.SendSmsCodeRequest;
import com.gooseapp.user.dto.VerifySmsCodeRequest;

/**
 * 短信服务接口
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-01-06
 */
public interface SmsService {

    /**
     * 发送短信验证码
     *
     * @param request 发送请求
     * @param ipAddress 请求IP
     * @param userAgent 用户代理
     * @return 是否发送成功
     */
    boolean sendSmsCode(SendSmsCodeRequest request, String ipAddress, String userAgent);

    /**
     * 验证短信验证码
     *
     * @param request 验证请求
     * @return 是否验证通过
     */
    boolean verifySmsCode(VerifySmsCodeRequest request);

    /**
     * 验证并消费短信验证码（验证通过后标记为已使用）
     *
     * @param phone 手机号
     * @param code 验证码
     * @param codeType 验证码类型
     * @return 是否验证通过
     */
    boolean verifyAndConsumeSmsCode(String phone, String code, Integer codeType);

    /**
     * 清理过期的验证码
     */
    void cleanExpiredCodes();

    /**
     * 检查手机号发送频率限制
     *
     * @param phone 手机号
     * @param codeType 验证码类型
     * @return 是否允许发送
     */
    boolean checkPhoneRateLimit(String phone, Integer codeType);

    /**
     * 检查IP发送频率限制
     *
     * @param ipAddress IP地址
     * @return 是否允许发送
     */
    boolean checkIpRateLimit(String ipAddress);
}
