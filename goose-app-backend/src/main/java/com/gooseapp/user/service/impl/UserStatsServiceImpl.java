package com.gooseapp.user.service.impl;

import com.gooseapp.user.dto.LoginResponse;
import com.gooseapp.user.entity.User;
import com.gooseapp.user.mapper.UserMapper;
import com.gooseapp.user.service.UserStatsService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 用户统计服务实现类
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-06-12
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class UserStatsServiceImpl implements UserStatsService {

    private final UserMapper userMapper;

    @Override
    public LoginResponse.UserStats getUserStats(Long userId) {
        User user = userMapper.selectById(userId);
        if (user == null) {
            throw new RuntimeException("用户不存在: " + userId);
        }
        return LoginResponse.UserStats.builder()
                .totalGeese(user.getTotalGeese())
                .totalDays(user.getTotalDays())
                .totalFeeds(user.getTotalFeeds())
                .build();
    }

    @Override
    @Transactional
    public void updateUserStats(Long userId, Integer totalGeese, Integer totalDays, Integer totalFeeds) {
        log.info("更新用户统计: userId={}, geese={}, days={}, feeds={}",
                userId, totalGeese, totalDays, totalFeeds);

        userMapper.updateUserStats(userId, totalGeese, totalDays, totalFeeds);

        log.info("用户统计更新成功: userId={}", userId);
    }
}
