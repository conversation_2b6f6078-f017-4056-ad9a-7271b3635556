package com.gooseapp.user.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.gooseapp.common.entity.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDate;

/**
 * 用户实体类
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-12-19
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("users")
public class User extends BaseEntity {

    /**
     * 用户名（唯一）
     */
    private String username;

    /**
     * 邮箱（唯一）
     */
    private String email;

    /**
     * 密码（加密存储）
     */
    private String password;

    /**
     * 昵称
     */
    private String nickname;

    /**
     * 头像URL
     */
    private String avatar;

    /**
     * 手机号
     */
    private String phone;

    /**
     * 性别：0-未知，1-男，2-女
     */
    private Integer gender = 0;

    /**
     * 生日
     */
    private LocalDate birthday;

    /**
     * 拥有小鹅总数
     */
    private Integer totalGeese = 0;

    /**
     * 养成总天数
     */
    private Integer totalDays = 0;

    /**
     * 喂食总次数
     */
    private Integer totalFeeds = 0;

    /**
     * 用户类型：1-C端用户(移动端)，2-管理后台用户
     */
    private Integer userType = 1;

    /**
     * 登录方式：1-用户名密码，2-手机验证码
     */
    private Integer loginType = 1;

    /**
     * 手机号是否已验证：0-未验证，1-已验证
     */
    private Integer phoneVerified = 0;

    /**
     * 最后登录时间
     */
    private java.time.LocalDateTime lastLoginTime;

    /**
     * 最后登录IP
     */
    private String lastLoginIp;

    /**
     * 登录次数统计
     */
    private Integer loginCount = 0;

    /**
     * 注册状态：1-已创建账户待设密码，2-注册完成
     */
    private Integer registrationStatus = 2;

    /**
     * 临时访问Token
     */
    private String tempToken;

    /**
     * 临时Token过期时间
     */
    private java.time.LocalDateTime tempTokenExpiresAt;

    /**
     * 性别枚举
     */
    public enum Gender {
        UNKNOWN(0, "未知"),
        MALE(1, "男"),
        FEMALE(2, "女");

        private final Integer code;
        private final String description;

        Gender(Integer code, String description) {
            this.code = code;
            this.description = description;
        }

        public Integer getCode() {
            return code;
        }

        public String getDescription() {
            return description;
        }

        public static Gender fromCode(Integer code) {
            for (Gender gender : values()) {
                if (gender.code.equals(code)) {
                    return gender;
                }
            }
            return UNKNOWN;
        }
    }

    /**
     * 用户类型枚举
     */
    public enum UserType {
        MOBILE_USER(1, "C端用户(移动端)"),
        ADMIN_USER(2, "管理后台用户");

        private final Integer code;
        private final String description;

        UserType(Integer code, String description) {
            this.code = code;
            this.description = description;
        }

        public Integer getCode() {
            return code;
        }

        public String getDescription() {
            return description;
        }

        public static UserType fromCode(Integer code) {
            for (UserType type : values()) {
                if (type.code.equals(code)) {
                    return type;
                }
            }
            return MOBILE_USER;
        }
    }

    /**
     * 注册状态枚举
     */
    public enum RegistrationStatus {
        ACCOUNT_CREATED(1, "已创建账户待设密码"),
        REGISTRATION_COMPLETED(2, "注册完成");

        private final Integer code;
        private final String description;

        RegistrationStatus(Integer code, String description) {
            this.code = code;
            this.description = description;
        }

        public Integer getCode() {
            return code;
        }

        public String getDescription() {
            return description;
        }

        public static RegistrationStatus fromCode(Integer code) {
            for (RegistrationStatus status : values()) {
                if (status.code.equals(code)) {
                    return status;
                }
            }
            return REGISTRATION_COMPLETED;
        }
    }

    /**
     * 登录方式枚举
     */
    public enum LoginType {
        USERNAME_PASSWORD(1, "用户名密码"),
        PHONE_SMS(2, "手机验证码");

        private final Integer code;
        private final String description;

        LoginType(Integer code, String description) {
            this.code = code;
            this.description = description;
        }

        public Integer getCode() {
            return code;
        }

        public String getDescription() {
            return description;
        }

        public static LoginType fromCode(Integer code) {
            for (LoginType type : values()) {
                if (type.code.equals(code)) {
                    return type;
                }
            }
            return USERNAME_PASSWORD;
        }
    }

    /**
     * 用户状态枚举
     */
    public enum Status {
        DISABLED(0, "禁用"),
        NORMAL(1, "正常");

        private final Integer code;
        private final String description;

        Status(Integer code, String description) {
            this.code = code;
            this.description = description;
        }

        public Integer getCode() {
            return code;
        }

        public String getDescription() {
            return description;
        }

        public static Status fromCode(Integer code) {
            for (Status status : values()) {
                if (status.code.equals(code)) {
                    return status;
                }
            }
            return NORMAL;
        }
    }

    /**
     * 检查用户是否正常状态
     */
    public boolean isNormal() {
        return Status.NORMAL.getCode().equals(this.status);
    }

    /**
     * 检查用户是否被禁用
     */
    public boolean isDisabled() {
        return Status.DISABLED.getCode().equals(this.status);
    }

    /**
     * 检查是否为C端用户
     */
    public boolean isMobileUser() {
        return UserType.MOBILE_USER.getCode().equals(this.userType);
    }

    /**
     * 检查是否为管理后台用户
     */
    public boolean isAdminUser() {
        return UserType.ADMIN_USER.getCode().equals(this.userType);
    }

    /**
     * 检查是否使用手机验证码登录
     */
    public boolean isPhoneSmsLogin() {
        return LoginType.PHONE_SMS.getCode().equals(this.loginType);
    }

    /**
     * 检查手机号是否已验证
     */
    public boolean hasPhoneVerified() {
        return Integer.valueOf(1).equals(this.phoneVerified);
    }

    /**
     * 获取用户类型描述
     */
    public String getUserTypeDescription() {
        return UserType.fromCode(this.userType).getDescription();
    }

    /**
     * 获取登录方式描述
     */
    public String getLoginTypeDescription() {
        return LoginType.fromCode(this.loginType).getDescription();
    }

    /**
     * 检查是否为待设密码状态
     */
    public boolean isAccountCreated() {
        return RegistrationStatus.ACCOUNT_CREATED.getCode().equals(this.registrationStatus);
    }

    /**
     * 检查是否注册完成
     */
    public boolean isRegistrationCompleted() {
        return RegistrationStatus.REGISTRATION_COMPLETED.getCode().equals(this.registrationStatus);
    }

    /**
     * 检查临时Token是否有效
     */
    public boolean isTempTokenValid() {
        return tempToken != null &&
               tempTokenExpiresAt != null &&
               tempTokenExpiresAt.isAfter(java.time.LocalDateTime.now());
    }

    /**
     * 获取注册状态描述
     */
    public String getRegistrationStatusDescription() {
        return RegistrationStatus.fromCode(this.registrationStatus).getDescription();
    }

    /**
     * 生成临时Token
     */
    public void generateTempToken() {
        this.tempToken = java.util.UUID.randomUUID().toString();
        this.tempTokenExpiresAt = java.time.LocalDateTime.now().plusHours(24);
    }

    /**
     * 清除临时Token
     */
    public void clearTempToken() {
        this.tempToken = null;
        this.tempTokenExpiresAt = null;
    }

    /**
     * 获取性别描述
     */
    public String getGenderDescription() {
        return Gender.fromCode(this.gender).getDescription();
    }

    /**
     * 获取状态描述
     */
    public String getStatusDescription() {
        return Status.fromCode(this.status).getDescription();
    }
}
