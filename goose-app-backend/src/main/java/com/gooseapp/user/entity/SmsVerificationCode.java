package com.gooseapp.user.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.gooseapp.common.entity.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 短信验证码实体类
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-01-06
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("sms_verification_codes")
public class SmsVerificationCode extends BaseEntity {

    /**
     * 手机号
     */
    private String phone;

    /**
     * 验证码
     */
    private String code;

    /**
     * 验证码类型：1-登录，2-注册，3-重置密码，4-绑定手机
     */
    private Integer codeType;

    /**
     * 过期时间
     */
    private LocalDateTime expiresAt;

    /**
     * 是否已使用：0-未使用，1-已使用
     */
    private Integer used = 0;

    /**
     * 请求IP地址
     */
    private String ipAddress;

    /**
     * 用户代理信息
     */
    private String userAgent;

    /**
     * 验证码类型枚举
     */
    public enum CodeType {
        LOGIN(1, "登录"),
        REGISTER(2, "注册"),
        RESET_PASSWORD(3, "重置密码"),
        BIND_PHONE(4, "绑定手机");

        private final Integer code;
        private final String description;

        CodeType(Integer code, String description) {
            this.code = code;
            this.description = description;
        }

        public Integer getCode() {
            return code;
        }

        public String getDescription() {
            return description;
        }

        public static CodeType fromCode(Integer code) {
            for (CodeType type : values()) {
                if (type.code.equals(code)) {
                    return type;
                }
            }
            return LOGIN;
        }
    }

    /**
     * 检查验证码是否已过期
     */
    public boolean isExpired() {
        return LocalDateTime.now().isAfter(this.expiresAt);
    }

    /**
     * 检查验证码是否已使用
     */
    public boolean hasBeenUsed() {
        return Integer.valueOf(1).equals(this.used);
    }

    /**
     * 检查验证码是否有效（未过期且未使用）
     */
    public boolean isValid() {
        return !isExpired() && !hasBeenUsed();
    }

    /**
     * 标记验证码为已使用
     */
    public void markAsUsed() {
        this.used = 1;
    }

    /**
     * 获取验证码类型描述
     */
    public String getCodeTypeDescription() {
        return CodeType.fromCode(this.codeType).getDescription();
    }
}
