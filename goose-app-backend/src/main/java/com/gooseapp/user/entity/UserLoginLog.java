package com.gooseapp.user.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.gooseapp.common.entity.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 用户登录日志实体类
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-01-06
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("user_login_logs")
public class UserLoginLog extends BaseEntity {

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 登录方式：1-用户名密码，2-手机验证码
     */
    private Integer loginType;

    /**
     * 登录时间
     */
    private LocalDateTime loginTime;

    /**
     * 登录IP地址
     */
    private String ipAddress;

    /**
     * 用户代理信息
     */
    private String userAgent;

    /**
     * 设备信息
     */
    private String deviceInfo;

    /**
     * 登录结果：1-成功，0-失败
     */
    private Integer loginResult;

    /**
     * 失败原因
     */
    private String failureReason;

    /**
     * 登录结果枚举
     */
    public enum LoginResult {
        FAILURE(0, "失败"),
        SUCCESS(1, "成功");

        private final Integer code;
        private final String description;

        LoginResult(Integer code, String description) {
            this.code = code;
            this.description = description;
        }

        public Integer getCode() {
            return code;
        }

        public String getDescription() {
            return description;
        }

        public static LoginResult fromCode(Integer code) {
            for (LoginResult result : values()) {
                if (result.code.equals(code)) {
                    return result;
                }
            }
            return FAILURE;
        }
    }

    /**
     * 检查登录是否成功
     */
    public boolean isSuccess() {
        return LoginResult.SUCCESS.getCode().equals(this.loginResult);
    }

    /**
     * 检查登录是否失败
     */
    public boolean isFailure() {
        return LoginResult.FAILURE.getCode().equals(this.loginResult);
    }

    /**
     * 获取登录结果描述
     */
    public String getLoginResultDescription() {
        return LoginResult.fromCode(this.loginResult).getDescription();
    }

    /**
     * 获取登录方式描述
     */
    public String getLoginTypeDescription() {
        return User.LoginType.fromCode(this.loginType).getDescription();
    }

    /**
     * 创建成功登录日志
     */
    public static UserLoginLog createSuccessLog(Long userId, Integer loginType, String ipAddress, String userAgent, String deviceInfo) {
        UserLoginLog log = new UserLoginLog();
        log.setUserId(userId);
        log.setLoginType(loginType);
        log.setLoginTime(LocalDateTime.now());
        log.setIpAddress(ipAddress);
        log.setUserAgent(userAgent);
        log.setDeviceInfo(deviceInfo);
        log.setLoginResult(LoginResult.SUCCESS.getCode());
        return log;
    }

    /**
     * 创建失败登录日志
     */
    public static UserLoginLog createFailureLog(Long userId, Integer loginType, String ipAddress, String userAgent, String deviceInfo, String failureReason) {
        UserLoginLog log = new UserLoginLog();
        log.setUserId(userId);
        log.setLoginType(loginType);
        log.setLoginTime(LocalDateTime.now());
        log.setIpAddress(ipAddress);
        log.setUserAgent(userAgent);
        log.setDeviceInfo(deviceInfo);
        log.setLoginResult(LoginResult.FAILURE.getCode());
        log.setFailureReason(failureReason);
        return log;
    }
}
