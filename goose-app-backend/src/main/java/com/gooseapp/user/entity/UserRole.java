package com.gooseapp.user.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.gooseapp.common.entity.BaseEntity;
import com.gooseapp.user.enums.Role;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 用户角色实体类
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-12-19
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("user_roles")
public class UserRole extends BaseEntity {

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 角色名称
     */
    private Role roleName;

    /**
     * 默认构造函数
     */
    public UserRole() {
    }

    /**
     * 构造函数
     * 
     * @param userId 用户ID
     * @param roleName 角色名称
     */
    public UserRole(Long userId, Role roleName) {
        this.userId = userId;
        this.roleName = roleName;
    }

    /**
     * 检查是否为管理员角色
     * 
     * @return 如果是管理员角色返回true，否则返回false
     */
    public boolean isAdmin() {
        return roleName != null && roleName.isAdmin();
    }

    /**
     * 检查是否为普通用户角色
     * 
     * @return 如果是普通用户角色返回true，否则返回false
     */
    public boolean isUser() {
        return roleName != null && roleName.isUser();
    }

    /**
     * 获取角色代码
     * 
     * @return 角色代码
     */
    public String getRoleCode() {
        return roleName != null ? roleName.getCode() : null;
    }

    /**
     * 获取角色名称
     * 
     * @return 角色名称
     */
    public String getRoleDisplayName() {
        return roleName != null ? roleName.getName() : null;
    }

    /**
     * 获取角色描述
     * 
     * @return 角色描述
     */
    public String getRoleDescription() {
        return roleName != null ? roleName.getDescription() : null;
    }

    @Override
    public String toString() {
        return String.format("UserRole{id=%d, userId=%d, roleName=%s, createdAt=%s}", 
                           id, userId, roleName, createdAt);
    }
}
