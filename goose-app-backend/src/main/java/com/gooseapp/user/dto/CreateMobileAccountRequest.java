package com.gooseapp.user.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;
import lombok.Data;

/**
 * C端用户创建账户请求DTO
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-01-06
 */
@Data
@Schema(description = "C端用户创建账户请求")
public class CreateMobileAccountRequest {

    @NotBlank(message = "手机号不能为空")
    @Pattern(regexp = "^1[3-9]\\d{9}$", message = "手机号格式不正确")
    @Schema(description = "手机号", example = "***********")
    private String phone;

    @NotBlank(message = "验证码不能为空")
    @Pattern(regexp = "^\\d{6}$", message = "验证码必须为6位数字")
    @Schema(description = "验证码", example = "123456")
    private String code;

    @Schema(description = "昵称", example = "小鹅用户")
    private String nickname;

    @Schema(description = "设备信息", example = "iPhone 15 Pro")
    private String deviceInfo;

    /**
     * 获取处理后的昵称
     */
    public String getProcessedNickname() {
        if (nickname == null || nickname.trim().isEmpty()) {
            // 使用手机号后4位生成默认昵称
            return "小鹅用户" + phone.substring(phone.length() - 4);
        }
        return nickname.trim();
    }
}
