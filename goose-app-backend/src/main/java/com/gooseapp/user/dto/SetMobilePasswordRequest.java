package com.gooseapp.user.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;
import lombok.Data;

/**
 * C端用户设置密码请求DTO
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-01-06
 */
@Data
@Schema(description = "C端用户设置密码请求")
public class SetMobilePasswordRequest {

    @NotBlank(message = "密码不能为空")
    @Pattern(regexp = "^(?=.*[A-Za-z])(?=.*\\d)[A-Za-z\\d@$!%*#?&]{8,20}$", 
             message = "密码必须8-20位，包含字母和数字")
    @Schema(description = "新密码", example = "Password123")
    private String password;

    @NotBlank(message = "确认密码不能为空")
    @Schema(description = "确认密码", example = "Password123")
    private String confirmPassword;

    @Schema(description = "设备信息", example = "iPhone 15 Pro")
    private String deviceInfo;

    /**
     * 验证两次密码是否一致
     */
    public boolean isPasswordMatched() {
        return password != null && password.equals(confirmPassword);
    }
}
