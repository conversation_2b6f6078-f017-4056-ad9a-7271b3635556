package com.gooseapp.user.dto;

import com.gooseapp.user.enums.Role;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 用户角色数据传输对象
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-12-19
 */
@Data
@Schema(description = "用户角色信息")
public class UserRoleDTO {

    /**
     * 角色ID
     */
    @Schema(description = "角色ID", example = "1")
    private Long id;

    /**
     * 用户ID
     */
    @Schema(description = "用户ID", example = "1")
    private Long userId;

    /**
     * 角色名称
     */
    @Schema(description = "角色名称", example = "ADMIN")
    private Role roleName;

    /**
     * 角色代码
     */
    @Schema(description = "角色代码", example = "ADMIN")
    private String roleCode;

    /**
     * 角色显示名称
     */
    @Schema(description = "角色显示名称", example = "管理员")
    private String roleDisplayName;

    /**
     * 角色描述
     */
    @Schema(description = "角色描述", example = "拥有系统所有权限，可以创建小鹅、管理商品池等")
    private String roleDescription;

    /**
     * 是否为管理员角色
     */
    @Schema(description = "是否为管理员角色", example = "true")
    private Boolean isAdmin;

    /**
     * 是否为普通用户角色
     */
    @Schema(description = "是否为普通用户角色", example = "false")
    private Boolean isUser;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间", example = "2024-12-19T10:30:00")
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @Schema(description = "更新时间", example = "2024-12-19T10:30:00")
    private LocalDateTime updatedAt;

    /**
     * 默认构造函数
     */
    public UserRoleDTO() {
    }

    /**
     * 构造函数
     * 
     * @param userId 用户ID
     * @param roleName 角色名称
     */
    public UserRoleDTO(Long userId, Role roleName) {
        this.userId = userId;
        this.roleName = roleName;
        this.roleCode = roleName.getCode();
        this.roleDisplayName = roleName.getName();
        this.roleDescription = roleName.getDescription();
        this.isAdmin = roleName.isAdmin();
        this.isUser = roleName.isUser();
    }

    /**
     * 从实体对象创建DTO
     * 
     * @param userRole 用户角色实体
     * @return 用户角色DTO
     */
    public static UserRoleDTO fromEntity(com.gooseapp.user.entity.UserRole userRole) {
        if (userRole == null) {
            return null;
        }

        UserRoleDTO dto = new UserRoleDTO();
        dto.setId(userRole.getId());
        dto.setUserId(userRole.getUserId());
        dto.setRoleName(userRole.getRoleName());
        dto.setRoleCode(userRole.getRoleCode());
        dto.setRoleDisplayName(userRole.getRoleDisplayName());
        dto.setRoleDescription(userRole.getRoleDescription());
        dto.setIsAdmin(userRole.isAdmin());
        dto.setIsUser(userRole.isUser());
        dto.setCreatedAt(userRole.getCreatedAt());
        dto.setUpdatedAt(userRole.getUpdatedAt());

        return dto;
    }

    /**
     * 转换为实体对象
     * 
     * @return 用户角色实体
     */
    public com.gooseapp.user.entity.UserRole toEntity() {
        com.gooseapp.user.entity.UserRole userRole = new com.gooseapp.user.entity.UserRole();
        userRole.setId(this.id);
        userRole.setUserId(this.userId);
        userRole.setRoleName(this.roleName);
        userRole.setCreatedAt(this.createdAt);
        userRole.setUpdatedAt(this.updatedAt);

        return userRole;
    }

    @Override
    public String toString() {
        return String.format("UserRoleDTO{id=%d, userId=%d, roleName=%s, roleDisplayName='%s', isAdmin=%s}", 
                           id, userId, roleName, roleDisplayName, isAdmin);
    }
}
