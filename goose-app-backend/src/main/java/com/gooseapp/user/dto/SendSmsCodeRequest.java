package com.gooseapp.user.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;
import lombok.Data;

/**
 * 发送短信验证码请求DTO
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-01-06
 */
@Data
@Schema(description = "发送短信验证码请求")
public class SendSmsCodeRequest {

    @NotBlank(message = "手机号不能为空")
    @Pattern(regexp = "^1[3-9]\\d{9}$", message = "手机号格式不正确")
    @Schema(description = "手机号", example = "13800138000")
    private String phone;

    @NotNull(message = "验证码类型不能为空")
    @Schema(description = "验证码类型：1-登录，2-注册，3-重置密码，4-绑定手机", example = "1")
    private Integer codeType;

    /**
     * 获取验证码类型描述
     */
    public String getCodeTypeDescription() {
        switch (codeType) {
            case 1: return "登录";
            case 2: return "注册";
            case 3: return "重置密码";
            case 4: return "绑定手机";
            default: return "未知";
        }
    }
}
