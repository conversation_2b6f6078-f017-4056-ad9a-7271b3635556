package com.gooseapp.user.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;
import lombok.Data;

/**
 * 手机号验证码登录请求DTO
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-01-06
 */
@Data
@Schema(description = "手机号验证码登录请求")
public class PhoneLoginRequest {

    @NotBlank(message = "手机号不能为空")
    @Pattern(regexp = "^1[3-9]\\d{9}$", message = "手机号格式不正确")
    @Schema(description = "手机号", example = "13800138000")
    private String phone;

    @NotBlank(message = "验证码不能为空")
    @Pattern(regexp = "^\\d{6}$", message = "验证码必须为6位数字")
    @Schema(description = "验证码", example = "123456")
    private String code;

    @Schema(description = "设备信息", example = "iPhone 15 Pro")
    private String deviceInfo;
}
