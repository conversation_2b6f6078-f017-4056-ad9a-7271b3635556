package com.gooseapp.user.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;
import lombok.Data;

import java.time.LocalDate;

/**
 * 用户信息更新DTO
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-12-19
 */
@Data
@Schema(description = "用户信息更新请求")
public class UserProfileUpdateDTO {

    @Schema(description = "昵称", example = "小鹅爱好者")
    @Size(max = 50, message = "昵称长度不能超过50个字符")
    private String nickname;

    @Schema(description = "手机号", example = "13800138000")
    @Pattern(regexp = "^1[3-9]\\d{9}$", message = "手机号格式不正确")
    private String phone;

    @Schema(description = "性别：0-未知，1-男，2-女", example = "1")
    private Integer gender;

    @Schema(description = "生日", example = "1990-01-01")
    private LocalDate birthday;

    @Schema(description = "头像URL", example = "https://example.com/avatar.jpg")
    private String avatar;
}
