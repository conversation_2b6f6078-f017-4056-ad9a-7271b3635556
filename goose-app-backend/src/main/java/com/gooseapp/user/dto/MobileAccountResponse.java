package com.gooseapp.user.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * C端用户账户响应DTO
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-01-06
 */
@Data
@Builder
@Schema(description = "C端用户账户响应")
public class MobileAccountResponse {

    @Schema(description = "用户ID", example = "1")
    private Long userId;

    @Schema(description = "用户名", example = "user_***********")
    private String username;

    @Schema(description = "昵称", example = "小鹅用户")
    private String nickname;

    @Schema(description = "头像", example = "📱")
    private String avatar;

    @Schema(description = "邮箱", example = "<EMAIL>")
    private String email;

    @Schema(description = "手机号", example = "***********")
    private String phone;

    @Schema(description = "注册状态：1-已创建账户待设密码，2-注册完成", example = "1")
    private Integer registrationStatus;

    @Schema(description = "注册状态描述", example = "已创建账户待设密码")
    private String registrationStatusDesc;

    @Schema(description = "临时访问Token（仅在待设密码状态下返回）")
    private String tempToken;

    @Schema(description = "临时Token过期时间")
    private LocalDateTime tempTokenExpiresAt;

    @Schema(description = "创建时间")
    private LocalDateTime createdAt;

    @Schema(description = "用户统计信息")
    private LoginResponse.UserStats stats;
}
