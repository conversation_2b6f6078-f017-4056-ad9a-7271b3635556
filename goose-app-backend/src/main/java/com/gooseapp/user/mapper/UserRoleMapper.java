package com.gooseapp.user.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.gooseapp.user.entity.UserRole;
import com.gooseapp.user.enums.Role;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;
import java.util.Optional;

/**
 * 用户角色数据访问层 - MyBatis Plus版本
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-12-19
 */
@Mapper
public interface UserRoleMapper extends BaseMapper<UserRole> {

    /**
     * 根据用户ID查找角色
     */
    @Select("SELECT * FROM user_roles WHERE user_id = #{userId} AND is_deleted = 0")
    List<UserRole> findByUserId(@Param("userId") Long userId);

    /**
     * 根据角色名称查找用户角色
     */
    @Select("SELECT * FROM user_roles WHERE role_name = #{roleName} AND is_deleted = 0")
    List<UserRole> findByRoleName(@Param("roleName") Role roleName);

    /**
     * 根据用户ID和角色名称查找用户角色
     */
    @Select("SELECT * FROM user_roles WHERE user_id = #{userId} AND role_name = #{roleName} AND is_deleted = 0")
    Optional<UserRole> findByUserIdAndRoleName(@Param("userId") Long userId, @Param("roleName") Role roleName);

    /**
     * 检查用户是否拥有指定角色
     */
    @Select("SELECT COUNT(*) > 0 FROM user_roles WHERE user_id = #{userId} AND role_name = #{roleName} AND is_deleted = 0")
    boolean existsByUserIdAndRoleName(@Param("userId") Long userId, @Param("roleName") Role roleName);

    /**
     * 删除用户的所有角色
     */
    @Select("UPDATE user_roles SET is_deleted = 1 WHERE user_id = #{userId}")
    void deleteByUserId(@Param("userId") Long userId);

    /**
     * 删除用户的指定角色
     */
    @Select("UPDATE user_roles SET is_deleted = 1 WHERE user_id = #{userId} AND role_name = #{roleName}")
    void deleteByUserIdAndRoleName(@Param("userId") Long userId, @Param("roleName") Role roleName);

    /**
     * 统计拥有指定角色的用户数量
     */
    @Select("SELECT COUNT(DISTINCT user_id) FROM user_roles WHERE role_name = #{roleName} AND is_deleted = 0")
    long countUsersByRoleName(@Param("roleName") Role roleName);

    /**
     * 查找拥有指定角色的所有用户ID
     */
    @Select("SELECT DISTINCT user_id FROM user_roles WHERE role_name = #{roleName} AND is_deleted = 0")
    List<Long> findUserIdsByRoleName(@Param("roleName") Role roleName);

    /**
     * 查找用户的所有角色名称
     */
    @Select("SELECT role_name FROM user_roles WHERE user_id = #{userId} AND is_deleted = 0")
    List<Role> findRoleNamesByUserId(@Param("userId") Long userId);

    /**
     * 检查用户是否为管理员
     */
    @Select("SELECT COUNT(*) > 0 FROM user_roles WHERE user_id = #{userId} AND role_name IN ('ADMIN', 'SUPER_ADMIN') AND is_deleted = 0")
    boolean isAdmin(@Param("userId") Long userId);

    /**
     * 检查用户是否为超级管理员
     */
    @Select("SELECT COUNT(*) > 0 FROM user_roles WHERE user_id = #{userId} AND role_name = 'SUPER_ADMIN' AND is_deleted = 0")
    boolean isSuperAdmin(@Param("userId") Long userId);

    /**
     * 获取所有角色统计信息
     */
    @Select("SELECT role_name, COUNT(DISTINCT user_id) as user_count FROM user_roles WHERE is_deleted = 0 GROUP BY role_name")
    List<Object[]> getRoleStatistics();

    /**
     * 统计指定角色的用户数量
     */
    @Select("SELECT COUNT(DISTINCT user_id) FROM user_roles WHERE role_name = #{roleName} AND is_deleted = 0")
    long countByRoleName(@Param("roleName") Role roleName);

    /**
     * 根据优先级查找用户角色
     */
    @Select("SELECT * FROM user_roles WHERE user_id = #{userId} AND is_deleted = 0 ORDER BY " +
            "CASE role_name " +
            "WHEN 'SUPER_ADMIN' THEN 1 " +
            "WHEN 'ADMIN' THEN 2 " +
            "WHEN 'USER' THEN 3 " +
            "ELSE 4 END")
    List<UserRole> findUserRolesByPriority(@Param("userId") Long userId);

    /**
     * 检查用户是否为管理员
     */
    @Select("SELECT COUNT(*) > 0 FROM user_roles WHERE user_id = #{userId} AND role_name IN ('ADMIN', 'SUPER_ADMIN') AND is_deleted = 0")
    boolean isUserAdmin(@Param("userId") Long userId);

    /**
     * 查找所有管理员用户ID
     */
    @Select("SELECT DISTINCT user_id FROM user_roles WHERE role_name IN ('ADMIN', 'SUPER_ADMIN') AND is_deleted = 0")
    List<Long> findAllAdminUserIds();
}
