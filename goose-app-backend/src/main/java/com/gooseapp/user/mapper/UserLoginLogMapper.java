package com.gooseapp.user.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.gooseapp.user.entity.UserLoginLog;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 用户登录日志Mapper接口
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-01-06
 */
@Mapper
public interface UserLoginLogMapper extends BaseMapper<UserLoginLog> {

    /**
     * 根据用户ID查找最近的登录日志
     */
    @Select("SELECT * FROM user_login_logs " +
            "WHERE user_id = #{userId} AND is_deleted = 0 " +
            "ORDER BY login_time DESC LIMIT #{limit}")
    List<UserLoginLog> findRecentByUserId(@Param("userId") Long userId, @Param("limit") int limit);

    /**
     * 统计用户在指定时间段内的登录次数
     */
    @Select("SELECT COUNT(*) FROM user_login_logs " +
            "WHERE user_id = #{userId} AND login_result = 1 AND is_deleted = 0 " +
            "AND login_time >= #{startTime}")
    int countSuccessLoginsByUserAfterTime(@Param("userId") Long userId, @Param("startTime") LocalDateTime startTime);

    /**
     * 统计IP在指定时间段内的登录尝试次数
     */
    @Select("SELECT COUNT(*) FROM user_login_logs " +
            "WHERE ip_address = #{ipAddress} AND is_deleted = 0 " +
            "AND login_time >= #{startTime}")
    int countLoginAttemptsByIpAfterTime(@Param("ipAddress") String ipAddress, @Param("startTime") LocalDateTime startTime);

    /**
     * 统计IP在指定时间段内的失败登录次数
     */
    @Select("SELECT COUNT(*) FROM user_login_logs " +
            "WHERE ip_address = #{ipAddress} AND login_result = 0 AND is_deleted = 0 " +
            "AND login_time >= #{startTime}")
    int countFailedLoginsByIpAfterTime(@Param("ipAddress") String ipAddress, @Param("startTime") LocalDateTime startTime);

    /**
     * 查找用户最后一次成功登录记录
     */
    @Select("SELECT * FROM user_login_logs " +
            "WHERE user_id = #{userId} AND login_result = 1 AND is_deleted = 0 " +
            "ORDER BY login_time DESC LIMIT 1")
    UserLoginLog findLastSuccessLoginByUserId(@Param("userId") Long userId);
}
