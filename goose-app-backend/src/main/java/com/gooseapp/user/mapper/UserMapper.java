package com.gooseapp.user.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gooseapp.user.entity.User;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * 用户数据访问层 - MyBatis Plus版本
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-12-19
 */
@Mapper
public interface UserMapper extends BaseMapper<User> {

    /**
     * 根据用户名查找用户
     */
    @Select("SELECT * FROM users WHERE username = #{username} AND is_deleted = 0")
    Optional<User> findByUsername(@Param("username") String username);

    /**
     * 检查用户是否存在
     */
    @Select("SELECT COUNT(*) > 0 FROM users WHERE id = #{id} AND is_deleted = 0")
    boolean existsById(@Param("id") Long id);

    /**
     * 根据邮箱查找用户
     */
    @Select("SELECT * FROM users WHERE email = #{email} AND is_deleted = 0")
    Optional<User> findByEmail(@Param("email") String email);

    /**
     * 根据手机号查找用户
     */
    @Select("SELECT * FROM users WHERE phone = #{phone} AND is_deleted = 0")
    Optional<User> findByPhone(@Param("phone") String phone);

    /**
     * 检查用户名是否存在
     */
    @Select("SELECT COUNT(*) > 0 FROM users WHERE username = #{username} AND is_deleted = 0")
    boolean existsByUsername(@Param("username") String username);

    /**
     * 检查邮箱是否存在
     */
    @Select("SELECT COUNT(*) > 0 FROM users WHERE email = #{email} AND is_deleted = 0")
    boolean existsByEmail(@Param("email") String email);

    /**
     * 检查手机号是否存在
     */
    @Select("SELECT COUNT(*) > 0 FROM users WHERE phone = #{phone} AND is_deleted = 0")
    boolean existsByPhone(@Param("phone") String phone);

    /**
     * 根据状态查找用户列表
     */
    @Select("SELECT * FROM users WHERE status = #{status} AND is_deleted = 0 ORDER BY created_at DESC")
    List<User> findByStatus(@Param("status") Integer status);

    /**
     * 查找正常状态的用户
     */
    @Select("SELECT * FROM users WHERE status = 1 AND is_deleted = 0 ORDER BY created_at DESC")
    List<User> findNormalUsers();

    /**
     * 根据创建时间范围查找用户
     */
    @Select("SELECT * FROM users WHERE created_at BETWEEN #{startTime} AND #{endTime} AND is_deleted = 0 ORDER BY created_at DESC")
    List<User> findByCreatedAtBetween(@Param("startTime") LocalDateTime startTime,
                                     @Param("endTime") LocalDateTime endTime);

    /**
     * 统计用户总数
     */
    @Select("SELECT COUNT(*) FROM users WHERE is_deleted = 0")
    long countTotalUsers();

    /**
     * 统计正常状态用户数
     */
    @Select("SELECT COUNT(*) FROM users WHERE status = 1 AND is_deleted = 0")
    long countNormalUsers();

    /**
     * 统计今日注册用户数
     */
    @Select("SELECT COUNT(*) FROM users WHERE DATE(created_at) = CURDATE() AND is_deleted = 0")
    long countTodayRegistrations();

    /**
     * 根据昵称模糊查询用户
     */
    @Select("SELECT * FROM users WHERE nickname LIKE CONCAT('%', #{nickname}, '%') AND status = 1 AND is_deleted = 0 ORDER BY created_at DESC")
    List<User> findByNicknameContaining(@Param("nickname") String nickname);

    /**
     * 查找拥有小鹅数量最多的用户（排行榜）
     */
    @Select("SELECT * FROM users WHERE status = 1 AND is_deleted = 0 ORDER BY total_geese DESC LIMIT #{limit}")
    List<User> findTopUsersByGeeseCount(@Param("limit") int limit);

    /**
     * 查找养成天数最多的用户（排行榜）
     */
    @Select("SELECT * FROM users WHERE status = 1 AND is_deleted = 0 ORDER BY total_days DESC LIMIT #{limit}")
    List<User> findTopUsersByDays(@Param("limit") int limit);

    /**
     * 查找喂食次数最多的用户（排行榜）
     */
    @Select("SELECT * FROM users WHERE status = 1 AND is_deleted = 0 ORDER BY total_feeds DESC LIMIT #{limit}")
    List<User> findTopUsersByFeeds(@Param("limit") int limit);

    /**
     * 分页查询用户
     */
    @Select("SELECT * FROM users WHERE is_deleted = 0 ORDER BY created_at DESC")
    IPage<User> findAllWithPagination(Page<User> page);

    /**
     * 根据状态分页查询用户
     */
    @Select("SELECT * FROM users WHERE status = #{status} AND is_deleted = 0 ORDER BY created_at DESC")
    IPage<User> findByStatusWithPagination(Page<User> page, @Param("status") Integer status);

    /**
     * 根据关键词分页搜索用户（用户名、昵称、邮箱）
     */
    @Select("SELECT * FROM users WHERE (username LIKE CONCAT('%', #{keyword}, '%') " +
            "OR nickname LIKE CONCAT('%', #{keyword}, '%') " +
            "OR email LIKE CONCAT('%', #{keyword}, '%')) " +
            "AND status = 1 AND is_deleted = 0 ORDER BY created_at DESC")
    IPage<User> searchUsersPage(Page<User> page, @Param("keyword") String keyword);

    /**
     * 更新用户最后登录时间
     */
    @Update("UPDATE users SET updated_at = NOW() WHERE id = #{userId} AND is_deleted = 0")
    void updateLastLoginTime(@Param("userId") Long userId);

    /**
     * 根据用户名或邮箱查找用户
     */
    @Select("SELECT * FROM users WHERE (username = #{username} OR email = #{email}) AND is_deleted = 0")
    Optional<User> findByUsernameOrEmail(@Param("username") String username, @Param("email") String email);

    /**
     * 更新用户的小鹅统计信息
     */
    @Update("UPDATE users SET total_geese = #{totalGeese}, total_days = #{totalDays}, total_feeds = #{totalFeeds} WHERE id = #{userId} AND is_deleted = 0")
    void updateUserStats(@Param("userId") Long userId,
                        @Param("totalGeese") Integer totalGeese,
                        @Param("totalDays") Integer totalDays,
                        @Param("totalFeeds") Integer totalFeeds);
}
