package com.gooseapp.user.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.gooseapp.user.entity.SmsVerificationCode;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 短信验证码Mapper接口
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-01-06
 */
@Mapper
public interface SmsVerificationCodeMapper extends BaseMapper<SmsVerificationCode> {

    /**
     * 根据手机号和验证码类型查找最新的有效验证码
     */
    @Select("SELECT * FROM sms_verification_codes " +
            "WHERE phone = #{phone} AND code_type = #{codeType} AND is_deleted = 0 " +
            "ORDER BY created_at DESC LIMIT 1")
    SmsVerificationCode findLatestByPhoneAndType(@Param("phone") String phone, @Param("codeType") Integer codeType);

    /**
     * 根据手机号、验证码和类型查找验证码
     */
    @Select("SELECT * FROM sms_verification_codes " +
            "WHERE phone = #{phone} AND code = #{code} AND code_type = #{codeType} AND is_deleted = 0 " +
            "ORDER BY created_at DESC LIMIT 1")
    SmsVerificationCode findByPhoneAndCodeAndType(@Param("phone") String phone, @Param("code") String code, @Param("codeType") Integer codeType);

    /**
     * 统计指定时间段内某手机号的验证码发送次数
     */
    @Select("SELECT COUNT(*) FROM sms_verification_codes " +
            "WHERE phone = #{phone} AND code_type = #{codeType} AND is_deleted = 0 " +
            "AND created_at >= #{startTime}")
    int countByPhoneAndTypeAfterTime(@Param("phone") String phone, @Param("codeType") Integer codeType, @Param("startTime") LocalDateTime startTime);

    /**
     * 统计指定时间段内某IP的验证码发送次数
     */
    @Select("SELECT COUNT(*) FROM sms_verification_codes " +
            "WHERE ip_address = #{ipAddress} AND is_deleted = 0 " +
            "AND created_at >= #{startTime}")
    int countByIpAfterTime(@Param("ipAddress") String ipAddress, @Param("startTime") LocalDateTime startTime);

    /**
     * 清理过期的验证码
     */
    @Select("SELECT * FROM sms_verification_codes " +
            "WHERE expires_at < #{now} AND is_deleted = 0")
    List<SmsVerificationCode> findExpiredCodes(@Param("now") LocalDateTime now);
}
