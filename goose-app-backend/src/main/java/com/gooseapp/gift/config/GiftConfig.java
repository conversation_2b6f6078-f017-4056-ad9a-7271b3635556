package com.gooseapp.gift.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;
import lombok.Data;

/**
 * 转赠模块配置类
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-12-19
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "app.business.gift")
public class GiftConfig {

    /**
     * 每日最大转赠次数
     */
    private Integer maxPerDay = 5;

    /**
     * 默认过期时间（小时）
     */
    private Integer expireHours = 72;

    /**
     * 最小过期时间（小时）
     */
    private Integer minExpireHours = 1;

    /**
     * 最大过期时间（小时）
     */
    private Integer maxExpireHours = 168;

    /**
     * 转赠码前缀
     */
    private String codePrefix = "GIFT";

    /**
     * 转赠码长度
     */
    private Integer codeLength = 22;

    /**
     * 是否启用转赠功能
     */
    private Boolean enabled = true;

    /**
     * 是否允许转赠给自己
     */
    private Boolean allowSelfGift = false;

    /**
     * 过期转赠清理间隔（小时）
     */
    private Integer cleanupInterval = 24;

    /**
     * 转赠提醒时间（小时）
     */
    private Integer reminderHours = 24;

    /**
     * 是否启用转赠统计
     */
    private Boolean enableStatistics = true;

    /**
     * 是否启用转赠日志
     */
    private Boolean enableLogging = true;

    /**
     * 转赠分享域名
     */
    private String shareDomain = "http://localhost:8080";

    /**
     * 转赠分享路径
     */
    private String sharePath = "/gift";
}
