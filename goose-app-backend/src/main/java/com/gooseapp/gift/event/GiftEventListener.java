package com.gooseapp.gift.event;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

/**
 * 转赠事件监听器
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-12-19
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class GiftEventListener {

    /**
     * 处理转赠创建事件
     */
    @Async
    @EventListener
    public void handleGiftCreated(GiftEvent.GiftCreatedEvent event) {
        log.info("转赠创建事件: giftCode={}, senderId={}, gooseId={}", 
                event.getGiftCode(), event.getUserId(), event.getGooseId());
        
        try {
            // 发送创建通知
            // notificationService.sendGiftCreatedNotification(event);
            
            // 记录操作日志
            // auditService.logGiftCreated(event);
            
            // 更新用户统计
            // userStatisticsService.incrementGiftSent(event.getUserId());
            
        } catch (Exception e) {
            log.error("处理转赠创建事件失败: giftCode={}", event.getGiftCode(), e);
        }
    }

    /**
     * 处理转赠接收事件
     */
    @Async
    @EventListener
    public void handleGiftReceived(GiftEvent.GiftReceivedEvent event) {
        log.info("转赠接收事件: giftCode={}, senderId={}, receiverId={}", 
                event.getGiftCode(), event.getSenderId(), event.getReceiverId());
        
        try {
            // 发送接收通知给发送者
            // notificationService.sendGiftReceivedNotification(event);
            
            // 记录操作日志
            // auditService.logGiftReceived(event);
            
            // 更新用户统计
            // userStatisticsService.incrementGiftReceived(event.getReceiverId());
            
            // 发送感谢消息
            // messageService.sendThankYouMessage(event.getSenderId(), event.getReceiverId());
            
        } catch (Exception e) {
            log.error("处理转赠接收事件失败: giftCode={}", event.getGiftCode(), e);
        }
    }

    /**
     * 处理转赠取消事件
     */
    @Async
    @EventListener
    public void handleGiftCancelled(GiftEvent.GiftCancelledEvent event) {
        log.info("转赠取消事件: giftCode={}, senderId={}", 
                event.getGiftCode(), event.getUserId());
        
        try {
            // 记录操作日志
            // auditService.logGiftCancelled(event);
            
            // 清理相关缓存
            // cacheService.evictGiftCache(event.getGiftCode());
            
        } catch (Exception e) {
            log.error("处理转赠取消事件失败: giftCode={}", event.getGiftCode(), e);
        }
    }

    /**
     * 处理转赠过期事件
     */
    @Async
    @EventListener
    public void handleGiftExpired(GiftEvent.GiftExpiredEvent event) {
        log.info("转赠过期事件: giftCode={}, senderId={}", 
                event.getGiftCode(), event.getUserId());
        
        try {
            // 发送过期通知
            // notificationService.sendGiftExpiredNotification(event);
            
            // 记录操作日志
            // auditService.logGiftExpired(event);
            
            // 清理相关缓存
            // cacheService.evictGiftCache(event.getGiftCode());
            
        } catch (Exception e) {
            log.error("处理转赠过期事件失败: giftCode={}", event.getGiftCode(), e);
        }
    }
}
