package com.gooseapp.gift.event;

import lombok.Getter;
import org.springframework.context.ApplicationEvent;

/**
 * 转赠事件基类
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-12-19
 */
@Getter
public abstract class GiftEvent extends ApplicationEvent {

    private final Long giftId;
    private final String giftCode;
    private final Long userId;

    public GiftEvent(Object source, Long giftId, String giftCode, Long userId) {
        super(source);
        this.giftId = giftId;
        this.giftCode = giftCode;
        this.userId = userId;
    }

    /**
     * 转赠创建事件
     */
    public static class GiftCreatedEvent extends GiftEvent {
        @Getter
        private final Long gooseId;

        public GiftCreatedEvent(Object source, Long giftId, String giftCode, Long senderId, Long gooseId) {
            super(source, giftId, giftCode, senderId);
            this.gooseId = gooseId;
        }
    }

    /**
     * 转赠接收事件
     */
    public static class GiftReceivedEvent extends GiftEvent {
        @Getter
        private final Long senderId;
        @Getter
        private final Long receiverId;

        public GiftReceivedEvent(Object source, Long giftId, String giftCode, Long senderId, Long receiverId) {
            super(source, giftId, giftCode, receiverId);
            this.senderId = senderId;
            this.receiverId = receiverId;
        }
    }

    /**
     * 转赠取消事件
     */
    public static class GiftCancelledEvent extends GiftEvent {
        public GiftCancelledEvent(Object source, Long giftId, String giftCode, Long senderId) {
            super(source, giftId, giftCode, senderId);
        }
    }

    /**
     * 转赠过期事件
     */
    public static class GiftExpiredEvent extends GiftEvent {
        public GiftExpiredEvent(Object source, Long giftId, String giftCode, Long senderId) {
            super(source, giftId, giftCode, senderId);
        }
    }
}
