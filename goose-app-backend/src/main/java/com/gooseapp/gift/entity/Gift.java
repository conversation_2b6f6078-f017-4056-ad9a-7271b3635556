package com.gooseapp.gift.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.gooseapp.common.entity.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 转赠记录实体类
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-12-19
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("gifts")
public class Gift extends BaseEntity {

    /**
     * 转赠码（唯一）
     */
    private String giftCode;

    /**
     * 赠送者用户ID
     */
    private Long senderId;

    /**
     * 接收者用户ID（可为空，待接收时为空）
     */
    private Long receiverId;

    /**
     * 小鹅ID
     */
    private Long gooseId;

    /**
     * 转赠留言
     */
    private String message;

    /**
     * 过期时间
     */
    private LocalDateTime expiresAt;

    /**
     * 接收时间
     */
    private LocalDateTime receivedAt;

    /**
     * 转赠状态枚举
     */
    public enum Status {
        PENDING(0, "待接收"),
        RECEIVED(1, "已接收"),
        COMPLETED(2, "已完成"),
        EXPIRED(3, "已过期"),
        CANCELLED(4, "已取消");

        private final Integer code;
        private final String description;

        Status(Integer code, String description) {
            this.code = code;
            this.description = description;
        }

        public Integer getCode() {
            return code;
        }

        public String getDescription() {
            return description;
        }

        public static Status fromCode(Integer code) {
            for (Status status : values()) {
                if (status.code.equals(code)) {
                    return status;
                }
            }
            return PENDING;
        }
    }

    /**
     * 检查是否为待接收状态
     */
    public boolean isPending() {
        return Status.PENDING.getCode().equals(this.status);
    }

    /**
     * 检查是否已接收
     */
    public boolean isReceived() {
        return Status.RECEIVED.getCode().equals(this.status);
    }

    /**
     * 检查是否已完成
     */
    public boolean isCompleted() {
        return Status.COMPLETED.getCode().equals(this.status);
    }

    /**
     * 检查是否已过期
     */
    public boolean isExpired() {
        return Status.EXPIRED.getCode().equals(this.status);
    }

    /**
     * 检查是否已取消
     */
    public boolean isCancelled() {
        return Status.CANCELLED.getCode().equals(this.status);
    }

    /**
     * 检查是否可以接收
     */
    public boolean canBeReceived() {
        return isPending() && !isExpiredByTime();
    }

    /**
     * 检查是否可以取消
     */
    public boolean canBeCancelled() {
        return isPending() && !isExpiredByTime();
    }

    /**
     * 检查是否按时间过期
     */
    public boolean isExpiredByTime() {
        return expiresAt != null && LocalDateTime.now().isAfter(expiresAt);
    }

    /**
     * 获取状态描述
     */
    public String getStatusDescription() {
        return Status.fromCode(this.status).getDescription();
    }

    /**
     * 设置为已接收状态
     */
    public void markAsReceived(Long receiverId) {
        this.receiverId = receiverId;
        this.status = Status.RECEIVED.getCode();
        this.receivedAt = LocalDateTime.now();
    }

    /**
     * 设置为已完成状态
     */
    public void markAsCompleted() {
        this.status = Status.COMPLETED.getCode();
    }

    /**
     * 设置为已过期状态
     */
    public void markAsExpired() {
        this.status = Status.EXPIRED.getCode();
    }

    /**
     * 设置为已取消状态
     */
    public void markAsCancelled() {
        this.status = Status.CANCELLED.getCode();
    }

    /**
     * 检查是否为指定用户发送的转赠
     */
    public boolean isSentBy(Long userId) {
        return senderId != null && senderId.equals(userId);
    }

    /**
     * 检查是否为指定用户接收的转赠
     */
    public boolean isReceivedBy(Long userId) {
        return receiverId != null && receiverId.equals(userId);
    }

    /**
     * 获取剩余有效时间（小时）
     */
    public long getRemainingHours() {
        if (expiresAt == null) {
            return 0;
        }
        LocalDateTime now = LocalDateTime.now();
        if (now.isAfter(expiresAt)) {
            return 0;
        }
        return java.time.Duration.between(now, expiresAt).toHours();
    }

    /**
     * 检查转赠是否有效（未过期且状态正确）
     */
    public boolean isValid() {
        return isPending() && !isExpiredByTime();
    }
}
