package com.gooseapp.gift.controller;

import cn.dev33.satoken.annotation.SaCheckLogin;
import cn.dev33.satoken.stp.StpUtil;
import com.gooseapp.common.response.ApiResponse;
import com.gooseapp.gift.dto.*;
import com.gooseapp.gift.service.GiftService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.web.bind.annotation.*;

/**
 * 转赠管理控制器
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-12-19
 */
@Slf4j
@RestController
@RequestMapping("/api/v1/gifts")
@RequiredArgsConstructor
@Tag(name = "转赠管理", description = "小鹅转赠相关接口")
public class GiftController {

    private final GiftService giftService;

    /**
     * 创建转赠
     */
    @PostMapping
    @SaCheckLogin
    @Operation(summary = "创建转赠", description = "用户创建小鹅转赠")
    public ApiResponse<GiftDTO> createGift(@Valid @RequestBody GiftCreateRequest request) {
        Long userId = StpUtil.getLoginIdAsLong();
        
        log.info("创建转赠请求: userId={}, gooseId={}", userId, request.getGooseId());
        
        GiftDTO gift = giftService.createGift(userId, request);
        
        return ApiResponse.success("转赠创建成功", gift);
    }

    /**
     * 获取转赠详情
     */
    @GetMapping("/{giftCode}")
    @Operation(summary = "获取转赠详情", description = "根据转赠码获取转赠详情（公开访问）")
    public ApiResponse<GiftDTO> getGiftByCode(
            @Parameter(description = "转赠码") @PathVariable String giftCode) {
        
        log.info("查询转赠详情: giftCode={}", giftCode);
        
        GiftDTO gift = giftService.getGiftByCode(giftCode);
        
        return ApiResponse.success("查询成功", gift);
    }

    /**
     * 接收转赠
     */
    @PostMapping("/{giftCode}/receive")
    @SaCheckLogin
    @Operation(summary = "接收转赠", description = "用户接收转赠的小鹅")
    public ApiResponse<GiftDTO> receiveGift(
            @Parameter(description = "转赠码") @PathVariable String giftCode,
            @Valid @RequestBody GiftReceiveRequest request) {
        
        Long userId = StpUtil.getLoginIdAsLong();
        
        log.info("接收转赠请求: userId={}, giftCode={}", userId, giftCode);
        
        GiftDTO gift = giftService.receiveGift(userId, giftCode, request);
        
        return ApiResponse.success("转赠接收成功", gift);
    }

    /**
     * 取消转赠
     */
    @DeleteMapping("/{giftCode}")
    @SaCheckLogin
    @Operation(summary = "取消转赠", description = "转赠创建者取消转赠")
    public ApiResponse<Boolean> cancelGift(
            @Parameter(description = "转赠码") @PathVariable String giftCode) {
        
        Long userId = StpUtil.getLoginIdAsLong();
        
        log.info("取消转赠请求: userId={}, giftCode={}", userId, giftCode);
        
        Boolean result = giftService.cancelGift(userId, giftCode);
        
        return ApiResponse.success("转赠取消成功", result);
    }

    /**
     * 获取我发送的转赠列表
     */
    @GetMapping("/my/sent")
    @SaCheckLogin
    @Operation(summary = "我发送的转赠", description = "获取当前用户发送的转赠列表")
    public ApiResponse<Page<GiftListDTO>> getSentGifts(
            @Parameter(description = "状态筛选") @RequestParam(required = false) Integer status,
            @Parameter(description = "页码") @RequestParam(defaultValue = "0") int page,
            @Parameter(description = "每页大小") @RequestParam(defaultValue = "10") int size,
            @Parameter(description = "排序字段") @RequestParam(defaultValue = "createdAt") String sort,
            @Parameter(description = "排序方向") @RequestParam(defaultValue = "desc") String direction) {
        
        Long userId = StpUtil.getLoginIdAsLong();
        
        log.info("查询发送的转赠列表: userId={}, status={}, page={}, size={}", 
                userId, status, page, size);
        
        Sort.Direction sortDirection = "asc".equalsIgnoreCase(direction) ? 
                Sort.Direction.ASC : Sort.Direction.DESC;
        Pageable pageable = PageRequest.of(page, size, Sort.by(sortDirection, sort));
        
        Page<GiftListDTO> gifts = giftService.getSentGifts(userId, status, pageable);
        
        return ApiResponse.success("查询成功", gifts);
    }

    /**
     * 获取我接收的转赠列表
     */
    @GetMapping("/my/received")
    @SaCheckLogin
    @Operation(summary = "我接收的转赠", description = "获取当前用户接收的转赠列表")
    public ApiResponse<Page<GiftListDTO>> getReceivedGifts(
            @Parameter(description = "状态筛选") @RequestParam(required = false) Integer status,
            @Parameter(description = "页码") @RequestParam(defaultValue = "0") int page,
            @Parameter(description = "每页大小") @RequestParam(defaultValue = "10") int size,
            @Parameter(description = "排序字段") @RequestParam(defaultValue = "receivedAt") String sort,
            @Parameter(description = "排序方向") @RequestParam(defaultValue = "desc") String direction) {
        
        Long userId = StpUtil.getLoginIdAsLong();
        
        log.info("查询接收的转赠列表: userId={}, status={}, page={}, size={}", 
                userId, status, page, size);
        
        Sort.Direction sortDirection = "asc".equalsIgnoreCase(direction) ? 
                Sort.Direction.ASC : Sort.Direction.DESC;
        Pageable pageable = PageRequest.of(page, size, Sort.by(sortDirection, sort));
        
        Page<GiftListDTO> gifts = giftService.getReceivedGifts(userId, status, pageable);
        
        return ApiResponse.success("查询成功", gifts);
    }

    /**
     * 获取转赠统计信息
     */
    @GetMapping("/statistics")
    @SaCheckLogin
    @Operation(summary = "转赠统计", description = "获取当前用户的转赠统计信息")
    public ApiResponse<GiftStatisticsDTO> getGiftStatistics() {
        Long userId = StpUtil.getLoginIdAsLong();
        
        log.info("查询转赠统计: userId={}", userId);
        
        GiftStatisticsDTO statistics = giftService.getGiftStatistics(userId);
        
        return ApiResponse.success("查询成功", statistics);
    }

    /**
     * 检查小鹅是否可以转赠
     */
    @GetMapping("/check/{gooseId}")
    @SaCheckLogin
    @Operation(summary = "检查转赠权限", description = "检查指定小鹅是否可以转赠")
    public ApiResponse<Boolean> canGiftGoose(
            @Parameter(description = "小鹅ID") @PathVariable Long gooseId) {
        
        Long userId = StpUtil.getLoginIdAsLong();
        
        log.info("检查转赠权限: userId={}, gooseId={}", userId, gooseId);
        
        Boolean canGift = giftService.canGiftGoose(userId, gooseId);
        
        return ApiResponse.success("检查完成", canGift);
    }

    /**
     * 获取今日剩余转赠次数
     */
    @GetMapping("/quota/remaining")
    @SaCheckLogin
    @Operation(summary = "剩余转赠次数", description = "获取用户今日剩余转赠次数")
    public ApiResponse<Integer> getRemainingDailyGifts() {
        Long userId = StpUtil.getLoginIdAsLong();
        
        log.info("查询剩余转赠次数: userId={}", userId);
        
        Integer remaining = giftService.getRemainingDailyGifts(userId);
        
        return ApiResponse.success("查询成功", remaining);
    }

    /**
     * 验证转赠码格式
     */
    @GetMapping("/validate/{giftCode}")
    @Operation(summary = "验证转赠码", description = "验证转赠码格式是否正确")
    public ApiResponse<Boolean> validateGiftCode(
            @Parameter(description = "转赠码") @PathVariable String giftCode) {
        
        log.info("验证转赠码格式: giftCode={}", giftCode);
        
        Boolean isValid = giftService.isValidGiftCodeFormat(giftCode);
        
        return ApiResponse.success("验证完成", isValid);
    }

    /**
     * 获取转赠分享链接
     */
    @GetMapping("/{giftCode}/share-url")
    @Operation(summary = "获取分享链接", description = "获取转赠的分享链接")
    public ApiResponse<String> getShareUrl(
            @Parameter(description = "转赠码") @PathVariable String giftCode) {
        
        log.info("获取分享链接: giftCode={}", giftCode);
        
        String shareUrl = giftService.getShareUrl(giftCode);
        
        return ApiResponse.success("获取成功", shareUrl);
    }
}
