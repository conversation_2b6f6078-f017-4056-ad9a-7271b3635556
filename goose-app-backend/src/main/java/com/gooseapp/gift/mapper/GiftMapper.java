package com.gooseapp.gift.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gooseapp.gift.entity.Gift;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Delete;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * 转赠数据访问层 - MyBatis Plus版本
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-12-19
 */
@Mapper
public interface GiftMapper extends BaseMapper<Gift> {

    /**
     * 根据转赠码查询转赠记录
     */
    @Select("SELECT * FROM gifts WHERE gift_code = #{giftCode} AND is_deleted = 0")
    Gift findByGiftCode(@Param("giftCode") String giftCode);

    /**
     * 检查转赠码是否存在
     */
    @Select("SELECT COUNT(*) > 0 FROM gifts WHERE gift_code = #{giftCode} AND is_deleted = 0")
    boolean existsByGiftCode(@Param("giftCode") String giftCode);

    /**
     * 根据发送者ID和状态查询转赠记录
     */
    @Select("SELECT * FROM gifts WHERE sender_id = #{senderId} AND status = #{status} AND is_deleted = 0 ORDER BY created_at DESC")
    IPage<Gift> findBySenderIdAndStatus(@Param("senderId") Long senderId, 
                                       @Param("status") Integer status, 
                                       Page<Gift> page);

    /**
     * 根据发送者ID查询转赠记录
     */
    @Select("SELECT * FROM gifts WHERE sender_id = #{senderId} AND is_deleted = 0 ORDER BY created_at DESC")
    IPage<Gift> findBySenderId(@Param("senderId") Long senderId, Page<Gift> page);

    /**
     * 根据接收者ID和状态查询转赠记录
     */
    @Select("SELECT * FROM gifts WHERE receiver_id = #{receiverId} AND status = #{status} AND is_deleted = 0 ORDER BY created_at DESC")
    IPage<Gift> findByReceiverIdAndStatus(@Param("receiverId") Long receiverId, 
                                         @Param("status") Integer status, 
                                         Page<Gift> page);

    /**
     * 根据接收者ID查询转赠记录
     */
    @Select("SELECT * FROM gifts WHERE receiver_id = #{receiverId} AND is_deleted = 0 ORDER BY created_at DESC")
    IPage<Gift> findByReceiverId(@Param("receiverId") Long receiverId, Page<Gift> page);

    /**
     * 根据小鹅ID查询转赠记录
     */
    @Select("SELECT * FROM gifts WHERE goose_id = #{gooseId} AND is_deleted = 0 ORDER BY created_at DESC")
    List<Gift> findByGooseId(@Param("gooseId") Long gooseId);

    /**
     * 查询过期的转赠记录
     */
    @Select("SELECT * FROM gifts WHERE expires_at < #{currentTime} AND status = #{status} AND is_deleted = 0")
    List<Gift> findExpiredGifts(@Param("currentTime") LocalDateTime currentTime, @Param("status") Integer status);

    /**
     * 统计用户在指定时间范围内的转赠次数
     */
    @Select("SELECT COUNT(*) FROM gifts WHERE sender_id = #{senderId} AND created_at BETWEEN #{startTime} AND #{endTime} AND is_deleted = 0")
    Long countBySenderIdAndCreatedAtBetween(@Param("senderId") Long senderId, 
                                           @Param("startTime") LocalDateTime startTime, 
                                           @Param("endTime") LocalDateTime endTime);

    /**
     * 统计用户接收的转赠次数
     */
    @Select("SELECT COUNT(*) FROM gifts WHERE receiver_id = #{receiverId} AND received_at BETWEEN #{startTime} AND #{endTime} AND is_deleted = 0")
    Long countByReceiverIdAndReceivedAtBetween(@Param("receiverId") Long receiverId, 
                                              @Param("startTime") LocalDateTime startTime, 
                                              @Param("endTime") LocalDateTime endTime);

    /**
     * 查询用户发送的转赠记录列表
     */
    @Select("SELECT * FROM gifts WHERE sender_id = #{senderId} AND is_deleted = 0 ORDER BY created_at DESC")
    List<Gift> findBySenderIdOrderByCreatedAtDesc(@Param("senderId") Long senderId);

    /**
     * 查询用户接收的转赠记录列表
     */
    @Select("SELECT * FROM gifts WHERE receiver_id = #{receiverId} AND is_deleted = 0 ORDER BY received_at DESC")
    List<Gift> findByReceiverIdOrderByReceivedAtDesc(@Param("receiverId") Long receiverId);

    /**
     * 根据状态查询转赠记录
     */
    @Select("SELECT * FROM gifts WHERE status = #{status} AND is_deleted = 0 ORDER BY created_at DESC")
    List<Gift> findByStatusOrderByCreatedAtDesc(@Param("status") Integer status);

    /**
     * 查询指定时间范围内的转赠记录
     */
    @Select("SELECT * FROM gifts WHERE created_at BETWEEN #{startTime} AND #{endTime} AND is_deleted = 0 ORDER BY created_at DESC")
    List<Gift> findByCreatedAtBetween(@Param("startTime") LocalDateTime startTime, 
                                     @Param("endTime") LocalDateTime endTime);

    /**
     * 查询用户今日发送的转赠记录
     */
    @Select("SELECT * FROM gifts WHERE sender_id = #{senderId} AND created_at BETWEEN #{startOfDay} AND #{endOfDay} AND is_deleted = 0")
    List<Gift> findTodayGiftsBySender(@Param("senderId") Long senderId, 
                                     @Param("startOfDay") LocalDateTime startOfDay, 
                                     @Param("endOfDay") LocalDateTime endOfDay);

    /**
     * 查询最近的转赠记录
     */
    @Select("SELECT * FROM gifts WHERE is_deleted = 0 ORDER BY created_at DESC LIMIT #{limit}")
    List<Gift> findRecentGifts(@Param("limit") int limit);

    /**
     * 根据状态统计转赠数量
     */
    @Select("SELECT COUNT(*) FROM gifts WHERE status = #{status} AND is_deleted = 0")
    Long countByStatus(@Param("status") Integer status);

    /**
     * 查询即将过期的转赠记录（24小时内过期）
     */
    @Select("SELECT * FROM gifts WHERE status = #{status} AND expires_at BETWEEN #{currentTime} AND #{expiryThreshold} AND is_deleted = 0")
    List<Gift> findGiftsExpiringWithin24Hours(@Param("currentTime") LocalDateTime currentTime,
                                              @Param("expiryThreshold") LocalDateTime expiryThreshold,
                                              @Param("status") Integer status);

    /**
     * 删除过期的转赠记录（物理删除，用于数据清理）
     */
    @Delete("DELETE FROM gifts WHERE expires_at < #{expiryTime} AND status = #{status}")
    void deleteExpiredGifts(@Param("expiryTime") LocalDateTime expiryTime, @Param("status") Integer status);

    /**
     * 查询用户参与的所有转赠记录（发送+接收）
     */
    @Select("SELECT * FROM gifts WHERE (sender_id = #{userId} OR receiver_id = #{userId}) AND is_deleted = 0 ORDER BY created_at DESC")
    List<Gift> findByUserIdInvolved(@Param("userId") Long userId);

    /**
     * 分页查询用户参与的所有转赠记录
     */
    @Select("SELECT * FROM gifts WHERE (sender_id = #{userId} OR receiver_id = #{userId}) AND is_deleted = 0 ORDER BY created_at DESC")
    IPage<Gift> findByUserIdInvolved(@Param("userId") Long userId, Page<Gift> page);

    /**
     * 查找有效的转赠记录（按小鹅ID）
     */
    @Select("SELECT * FROM gifts WHERE goose_id = #{gooseId} AND status = #{status} AND expires_at > #{currentTime} AND is_deleted = 0 ORDER BY created_at DESC")
    List<Gift> findValidGiftsByGooseId(@Param("gooseId") Long gooseId, @Param("status") Integer status, @Param("currentTime") LocalDateTime currentTime);

    /**
     * 统计接收者的转赠记录数量
     */
    @Select("SELECT COUNT(*) FROM gifts WHERE receiver_id = #{receiverId} AND status = #{status} AND is_deleted = 0")
    long countByReceiverIdAndStatus(@Param("receiverId") Long receiverId, @Param("status") Integer status);

    /**
     * 统计发送者的转赠记录数量
     */
    @Select("SELECT COUNT(*) FROM gifts WHERE sender_id = #{senderId} AND status = #{status} AND is_deleted = 0")
    long countBySenderIdAndStatus(@Param("senderId") Long senderId, @Param("status") Integer status);

    /**
     * 统计用户发送的转赠总数
     */
    @Select("SELECT COUNT(*) FROM gifts WHERE sender_id = #{senderId} AND is_deleted = 0")
    Long countBySenderId(@Param("senderId") Long senderId);

    /**
     * 统计用户接收的转赠总数
     */
    @Select("SELECT COUNT(*) FROM gifts WHERE receiver_id = #{receiverId} AND is_deleted = 0")
    Long countByReceiverId(@Param("receiverId") Long receiverId);

    /**
     * 查询用户今日可发送的转赠次数（基于限制）
     */
    @Select("SELECT COUNT(*) FROM gifts WHERE sender_id = #{senderId} AND DATE(created_at) = CURDATE() AND is_deleted = 0")
    Long countTodayGiftsBySender(@Param("senderId") Long senderId);

    /**
     * 查询小鹅的转赠历史
     */
    @Select("SELECT * FROM gifts WHERE goose_id = #{gooseId} AND status IN (1, 2) AND is_deleted = 0 ORDER BY created_at DESC")
    List<Gift> findGooseGiftHistory(@Param("gooseId") Long gooseId);

    /**
     * 检查小鹅是否正在转赠中
     */
    @Select("SELECT COUNT(*) > 0 FROM gifts WHERE goose_id = #{gooseId} AND status = 0 AND is_deleted = 0")
    boolean isGooseInGiftProcess(@Param("gooseId") Long gooseId);

    /**
     * 查询待处理的转赠记录（用于定时任务）
     */
    @Select("SELECT * FROM gifts WHERE status = 0 AND expires_at > NOW() AND is_deleted = 0 ORDER BY created_at ASC")
    List<Gift> findPendingGifts();

    /**
     * 根据转赠码和状态查询
     */
    @Select("SELECT * FROM gifts WHERE gift_code = #{giftCode} AND status = #{status} AND is_deleted = 0")
    Optional<Gift> findByGiftCodeAndStatus(@Param("giftCode") String giftCode, @Param("status") Integer status);

    /**
     * 查询用户的转赠统计信息
     */
    @Select("SELECT status, COUNT(*) as count FROM gifts WHERE sender_id = #{userId} AND is_deleted = 0 GROUP BY status")
    List<Object[]> getGiftStatisticsByUser(@Param("userId") Long userId);

    /**
     * 查询系统转赠统计信息
     */
    @Select("SELECT DATE(created_at) as date, COUNT(*) as count FROM gifts WHERE is_deleted = 0 " +
            "AND created_at BETWEEN #{startTime} AND #{endTime} GROUP BY DATE(created_at) ORDER BY date")
    List<Object[]> getSystemGiftStatistics(@Param("startTime") LocalDateTime startTime, 
                                          @Param("endTime") LocalDateTime endTime);
}
