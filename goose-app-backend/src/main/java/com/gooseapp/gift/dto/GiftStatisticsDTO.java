package com.gooseapp.gift.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 转赠统计DTO
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-12-19
 */
@Data
@Schema(description = "转赠统计信息")
public class GiftStatisticsDTO {

    @Schema(description = "发送的转赠总数")
    private Long totalSent;

    @Schema(description = "接收的转赠总数")
    private Long totalReceived;

    @Schema(description = "待接收的转赠数量")
    private Long pendingReceived;

    @Schema(description = "已完成的转赠数量")
    private Long completedSent;

    @Schema(description = "已过期的转赠数量")
    private Long expiredSent;

    @Schema(description = "今日发送的转赠数量")
    private Long todaySent;

    @Schema(description = "今日剩余可发送数量")
    private Long todayRemaining;

    @Schema(description = "每日发送限制")
    private Long dailyLimit;

    @Schema(description = "本周发送的转赠数量")
    private Long weekSent;

    @Schema(description = "本月发送的转赠数量")
    private Long monthSent;

    @Schema(description = "本周接收的转赠数量")
    private Long weekReceived;

    @Schema(description = "本月接收的转赠数量")
    private Long monthReceived;

    @Schema(description = "转赠成功率（已接收/已发送）")
    private Double successRate;

    @Schema(description = "平均转赠完成时间（小时）")
    private Double averageCompletionTime;

    @Schema(description = "最受欢迎的转赠品种")
    private String mostPopularBreed;

    @Schema(description = "转赠活跃度评分（0-100）")
    private Integer activityScore;
}
