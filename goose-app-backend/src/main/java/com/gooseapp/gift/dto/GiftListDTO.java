package com.gooseapp.gift.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 转赠列表DTO
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-12-19
 */
@Data
@Schema(description = "转赠列表信息")
public class GiftListDTO {

    @Schema(description = "转赠记录ID")
    private Long id;

    @Schema(description = "转赠码")
    private String giftCode;

    @Schema(description = "小鹅名称")
    private String gooseName;

    @Schema(description = "小鹅品种")
    private String gooseBreed;

    @Schema(description = "小鹅图片URL")
    private String gooseImageUrl;

    @Schema(description = "小鹅等级")
    private Integer gooseLevel;

    @Schema(description = "对方用户昵称（发送列表显示接收者，接收列表显示发送者）")
    private String otherUserNickname;

    @Schema(description = "对方用户头像")
    private String otherUserAvatar;

    @Schema(description = "转赠留言")
    private String message;

    @Schema(description = "状态：0-待接收，1-已接收，2-已完成，3-已过期，4-已取消")
    private Integer status;

    @Schema(description = "状态描述")
    private String statusDescription;

    @Schema(description = "过期时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime expiresAt;

    @Schema(description = "接收时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime receivedAt;

    @Schema(description = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdAt;

    @Schema(description = "剩余有效时间（小时）")
    private Long remainingHours;

    @Schema(description = "是否可以操作（接收或取消）")
    private Boolean canOperate;

    @Schema(description = "操作类型：receive-可接收，cancel-可取消，none-无操作")
    private String operationType;
}
