package com.gooseapp.gift.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.*;
import lombok.Data;

/**
 * 创建转赠请求DTO
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-12-19
 */
@Data
@Schema(description = "创建转赠请求")
public class GiftCreateRequest {

    @Schema(description = "小鹅ID", required = true)
    @NotNull(message = "小鹅ID不能为空")
    @Positive(message = "小鹅ID必须为正数")
    private Long gooseId;

    @Schema(description = "转赠留言", example = "希望这只小鹅能给你带来快乐！")
    @Size(max = 500, message = "转赠留言不能超过500个字符")
    private String message;

    @Schema(description = "过期时间（小时）", example = "72", required = true)
    @NotNull(message = "过期时间不能为空")
    @Min(value = 1, message = "过期时间至少为1小时")
    @Max(value = 168, message = "过期时间最多为168小时（7天）")
    private Integer expiresHours;
}
