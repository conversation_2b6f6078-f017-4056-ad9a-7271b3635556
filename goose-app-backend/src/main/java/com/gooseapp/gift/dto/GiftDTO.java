package com.gooseapp.gift.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 转赠信息DTO
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-12-19
 */
@Data
@Schema(description = "转赠信息")
public class GiftDTO {

    @Schema(description = "转赠记录ID")
    private Long id;

    @Schema(description = "转赠码")
    private String giftCode;

    @Schema(description = "赠送者用户ID")
    private Long senderId;

    @Schema(description = "赠送者信息")
    private UserBasicDTO sender;

    @Schema(description = "接收者用户ID")
    private Long receiverId;

    @Schema(description = "接收者信息")
    private UserBasicDTO receiver;

    @Schema(description = "小鹅ID")
    private Long gooseId;

    @Schema(description = "小鹅信息")
    private GooseBasicDTO goose;

    @Schema(description = "转赠留言")
    private String message;

    @Schema(description = "状态：0-待接收，1-已接收，2-已完成，3-已过期，4-已取消")
    private Integer status;

    @Schema(description = "状态描述")
    private String statusDescription;

    @Schema(description = "过期时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime expiresAt;

    @Schema(description = "接收时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime receivedAt;

    @Schema(description = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdAt;

    @Schema(description = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updatedAt;

    @Schema(description = "分享链接")
    private String shareUrl;

    @Schema(description = "剩余有效时间（小时）")
    private Long remainingHours;

    @Schema(description = "是否可以接收")
    private Boolean canBeReceived;

    @Schema(description = "是否可以取消")
    private Boolean canBeCancelled;

    @Schema(description = "是否已过期")
    private Boolean isExpired;

    /**
     * 用户基础信息DTO
     */
    @Data
    @Schema(description = "用户基础信息")
    public static class UserBasicDTO {
        @Schema(description = "用户ID")
        private Long id;

        @Schema(description = "用户名")
        private String username;

        @Schema(description = "昵称")
        private String nickname;

        @Schema(description = "头像URL")
        private String avatar;
    }

    /**
     * 小鹅基础信息DTO
     */
    @Data
    @Schema(description = "小鹅基础信息")
    public static class GooseBasicDTO {
        @Schema(description = "小鹅ID")
        private Long id;

        @Schema(description = "小鹅名称")
        private String name;

        @Schema(description = "品种")
        private String breed;

        @Schema(description = "性别：1-雄性，2-雌性")
        private Integer gender;

        @Schema(description = "性别描述")
        private String genderDescription;

        @Schema(description = "年龄（周）")
        private Integer age;

        @Schema(description = "等级")
        private Integer level;

        @Schema(description = "小鹅图片URL")
        private String imageUrl;

        @Schema(description = "健康度（0-100）")
        private Integer health;

        @Schema(description = "整体状态评分（0-100）")
        private Double overallScore;
    }
}
