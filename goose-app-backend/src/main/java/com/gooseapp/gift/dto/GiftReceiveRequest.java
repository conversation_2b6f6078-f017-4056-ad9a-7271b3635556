package com.gooseapp.gift.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 接收转赠请求DTO
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-12-19
 */
@Data
@Schema(description = "接收转赠请求")
public class GiftReceiveRequest {

    @Schema(description = "接收确认", example = "true")
    private Boolean confirmed = true;

    @Schema(description = "接收备注", example = "谢谢你的小鹅！")
    private String note;
}
