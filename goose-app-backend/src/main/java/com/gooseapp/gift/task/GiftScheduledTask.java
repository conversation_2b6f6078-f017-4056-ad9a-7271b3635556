package com.gooseapp.gift.task;

import com.gooseapp.gift.service.GiftService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

/**
 * 转赠定时任务
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-12-19
 */
@Slf4j
@Component
@RequiredArgsConstructor
@ConditionalOnProperty(name = "app.business.gift.enabled", havingValue = "true", matchIfMissing = true)
public class GiftScheduledTask {

    private final GiftService giftService;

    /**
     * 处理过期转赠
     * 每小时执行一次
     */
    @Scheduled(cron = "0 0 * * * ?")
    public void processExpiredGifts() {
        log.info("开始执行过期转赠处理定时任务");
        
        try {
            Integer processedCount = giftService.processExpiredGifts();
            log.info("过期转赠处理完成，处理数量: {}", processedCount);
        } catch (Exception e) {
            log.error("过期转赠处理失败", e);
        }
    }

    /**
     * 转赠提醒任务
     * 每天上午9点执行
     */
    @Scheduled(cron = "0 0 9 * * ?")
    public void sendGiftReminders() {
        log.info("开始执行转赠提醒任务");
        
        try {
            // 获取即将过期的转赠
            var expiringSoon = giftService.getGiftsExpiringWithin24Hours(
                org.springframework.data.domain.PageRequest.of(0, 100)
            );
            
            log.info("发现即将过期的转赠数量: {}", expiringSoon.getTotalElements());
            
            // 这里可以集成消息推送服务
            // messageService.sendGiftExpiryReminders(expiringSoon.getContent());
            
        } catch (Exception e) {
            log.error("转赠提醒任务执行失败", e);
        }
    }

    /**
     * 转赠统计更新任务
     * 每天凌晨2点执行
     */
    @Scheduled(cron = "0 0 2 * * ?")
    public void updateGiftStatistics() {
        log.info("开始执行转赠统计更新任务");
        
        try {
            // 这里可以实现统计数据的预计算和缓存
            // statisticsService.updateDailyGiftStatistics();
            
            log.info("转赠统计更新完成");
        } catch (Exception e) {
            log.error("转赠统计更新失败", e);
        }
    }

    /**
     * 清理过期数据任务
     * 每周日凌晨3点执行
     */
    @Scheduled(cron = "0 0 3 ? * SUN")
    public void cleanupExpiredData() {
        log.info("开始执行过期数据清理任务");
        
        try {
            // 清理30天前的过期转赠记录
            // giftService.cleanupExpiredGifts(30);
            
            log.info("过期数据清理完成");
        } catch (Exception e) {
            log.error("过期数据清理失败", e);
        }
    }
}
