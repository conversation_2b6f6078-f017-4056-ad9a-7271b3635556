package com.gooseapp.gift.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gooseapp.common.constant.ErrorCode;
import com.gooseapp.common.exception.BusinessException;
import com.gooseapp.gift.dto.*;
import com.gooseapp.gift.entity.Gift;
import com.gooseapp.gift.mapper.GiftMapper;
import com.gooseapp.gift.service.GiftService;
import com.gooseapp.goose.entity.Goose;
import com.gooseapp.goose.mapper.GooseMapper;
import com.gooseapp.user.entity.User;
import com.gooseapp.user.mapper.UserMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.security.SecureRandom;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 转赠服务实现类
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-12-19
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class GiftServiceImpl extends ServiceImpl<GiftMapper, Gift> implements GiftService {

    private final GooseMapper gooseMapper;
    private final UserMapper userMapper;

    @Value("${app.business.gift.max-per-day:5}")
    private Integer maxGiftsPerDay;

    @Value("${app.business.gift.expire-hours:72}")
    private Integer defaultExpireHours;

    @Value("${app.domain:http://localhost:8080}")
    private String appDomain;

    private static final String GIFT_CODE_PREFIX = "GIFT";
    private static final String CHARACTERS = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
    private static final SecureRandom RANDOM = new SecureRandom();

    @Override
    @Transactional
    public GiftDTO createGift(Long userId, GiftCreateRequest request) {
        log.info("创建转赠: userId={}, gooseId={}", userId, request.getGooseId());

        // 1. 验证用户是否存在
        User sender = userMapper.selectById(userId);
        if (sender == null) {
            throw new BusinessException(ErrorCode.USER_NOT_FOUND);
        }

        // 2. 验证小鹅是否存在且属于用户
        Goose goose = gooseMapper.selectById(request.getGooseId());
        if (goose == null) {
            throw new BusinessException(ErrorCode.GOOSE_NOT_FOUND);
        }

        if (!goose.getUserId().equals(userId)) {
            throw new BusinessException(ErrorCode.PERMISSION_DENIED, "您没有权限转赠此小鹅");
        }

        // 3. 验证小鹅状态
        if (!goose.isOwned()) {
            throw new BusinessException(ErrorCode.GOOSE_NOT_AVAILABLE, "只能转赠已归属的小鹅");
        }

        if (goose.isGifted()) {
            throw new BusinessException(ErrorCode.GOOSE_NOT_AVAILABLE, "此小鹅已被转赠，无法重复转赠");
        }

        // 4. 检查今日转赠次数限制
        if (hasReachedDailyLimit(userId)) {
            throw new BusinessException(ErrorCode.GIFT_LIMIT_EXCEEDED, "今日转赠次数已达上限（" + maxGiftsPerDay + "次）");
        }

        // 5. 检查小鹅是否有未完成的转赠
        List<Gift> existingGifts = getBaseMapper().findValidGiftsByGooseId(
                request.getGooseId(),
                Gift.Status.PENDING.getCode(),
                LocalDateTime.now()
        );
        if (!existingGifts.isEmpty()) {
            throw new BusinessException(ErrorCode.BUSINESS_RULE_VIOLATION, "此小鹅已有待接收的转赠，请先取消或等待过期");
        }

        // 6. 创建转赠记录
        Gift gift = new Gift();
        gift.setGiftCode(generateGiftCode());
        gift.setSenderId(userId);
        gift.setGooseId(request.getGooseId());
        gift.setMessage(request.getMessage());
        gift.setStatus(Gift.Status.PENDING.getCode());
        gift.setExpiresAt(LocalDateTime.now().plusHours(request.getExpiresHours()));

        save(gift);

        // 7. 更新小鹅状态为已转赠
        goose.setStatus(Goose.Status.GIFTED.getCode());
        gooseMapper.updateById(goose);

        log.info("转赠创建成功: giftCode={}, userId={}, gooseId={}", 
                gift.getGiftCode(), userId, request.getGooseId());

        return convertToGiftDTO(gift);
    }

    @Override
    @Transactional(readOnly = true)
    public GiftDTO getGiftByCode(String giftCode) {
        log.info("查询转赠详情: giftCode={}", giftCode);

        Gift gift = getBaseMapper().findByGiftCode(giftCode);
        if (gift == null) {
            throw new BusinessException(ErrorCode.GIFT_NOT_FOUND);
        }

        return convertToGiftDTO(gift);
    }

    @Override
    @Transactional
    public GiftDTO receiveGift(Long userId, String giftCode, GiftReceiveRequest request) {
        log.info("接收转赠: userId={}, giftCode={}", userId, giftCode);

        // 1. 验证用户是否存在
        User receiver = userMapper.selectById(userId);
        if (receiver == null) {
            throw new BusinessException(ErrorCode.USER_NOT_FOUND);
        }

        // 2. 查询转赠记录
        Gift gift = getBaseMapper().findByGiftCode(giftCode);
        if (gift == null) {
            throw new BusinessException(ErrorCode.GIFT_NOT_FOUND);
        }

        // 3. 验证转赠状态
        if (!gift.canBeReceived()) {
            if (gift.isExpiredByTime()) {
                throw new BusinessException(ErrorCode.GIFT_EXPIRED);
            }
            if (gift.isReceived()) {
                throw new BusinessException(ErrorCode.GIFT_ALREADY_RECEIVED);
            }
            if (gift.isCancelled()) {
                throw new BusinessException(ErrorCode.OPERATION_NOT_ALLOWED, "转赠已被取消");
            }
            throw new BusinessException(ErrorCode.OPERATION_NOT_ALLOWED, "转赠状态异常，无法接收");
        }

        // 4. 验证不能接收自己的转赠
        if (gift.getSenderId().equals(userId)) {
            throw new BusinessException(ErrorCode.OPERATION_NOT_ALLOWED, "不能接收自己发送的转赠");
        }

        // 5. 获取小鹅信息
        Goose goose = gooseMapper.selectById(gift.getGooseId());
        if (goose == null) {
            throw new BusinessException(ErrorCode.GOOSE_NOT_FOUND);
        }

        // 6. 更新转赠记录
        gift.markAsReceived(userId);
        updateById(gift);

        // 7. 更新小鹅归属
        goose.setUserId(userId);
        goose.setStatus(Goose.Status.NORMAL.getCode());
        goose.setOwnershipStatus(Goose.OwnershipStatus.OWNED.getCode());
        goose.setPurchaseTime(LocalDateTime.now());
        gooseMapper.updateById(goose);

        log.info("转赠接收成功: giftCode={}, senderId={}, receiverId={}, gooseId={}", 
                giftCode, gift.getSenderId(), userId, gift.getGooseId());

        return convertToGiftDTO(gift);
    }

    @Override
    @Transactional
    public Boolean cancelGift(Long userId, String giftCode) {
        log.info("取消转赠: userId={}, giftCode={}", userId, giftCode);

        // 1. 查询转赠记录
        Gift gift = getBaseMapper().findByGiftCode(giftCode);
        if (gift == null) {
            throw new BusinessException(ErrorCode.GIFT_NOT_FOUND);
        }

        // 2. 验证权限
        if (!gift.isSentBy(userId)) {
            throw new BusinessException(ErrorCode.PERMISSION_DENIED, "您没有权限取消此转赠");
        }

        // 3. 验证状态
        if (!gift.canBeCancelled()) {
            if (gift.isReceived()) {
                throw new BusinessException(ErrorCode.OPERATION_NOT_ALLOWED, "转赠已被接收，无法取消");
            }
            if (gift.isExpiredByTime()) {
                throw new BusinessException(ErrorCode.GIFT_EXPIRED, "转赠已过期，无法取消");
            }
            if (gift.isCancelled()) {
                throw new BusinessException(ErrorCode.OPERATION_NOT_ALLOWED, "转赠已被取消");
            }
            throw new BusinessException(ErrorCode.OPERATION_NOT_ALLOWED, "转赠状态异常，无法取消");
        }

        // 4. 更新转赠状态
        gift.markAsCancelled();
        updateById(gift);

        // 5. 恢复小鹅状态
        Goose goose = gooseMapper.selectById(gift.getGooseId());
        if (goose == null) {
            throw new BusinessException(ErrorCode.GOOSE_NOT_FOUND);
        }
        
        goose.setStatus(Goose.Status.NORMAL.getCode());
        gooseMapper.updateById(goose);

        log.info("转赠取消成功: giftCode={}, userId={}, gooseId={}", 
                giftCode, userId, gift.getGooseId());

        return true;
    }

    @Override
    public String generateGiftCode() {
        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
        String randomSuffix = generateRandomString(4);
        String giftCode = GIFT_CODE_PREFIX + timestamp + randomSuffix;
        
        // 确保转赠码唯一性
        while (getBaseMapper().existsByGiftCode(giftCode)) {
            randomSuffix = generateRandomString(4);
            giftCode = GIFT_CODE_PREFIX + timestamp + randomSuffix;
        }
        
        return giftCode;
    }

    private String generateRandomString(int length) {
        StringBuilder sb = new StringBuilder(length);
        for (int i = 0; i < length; i++) {
            sb.append(CHARACTERS.charAt(RANDOM.nextInt(CHARACTERS.length())));
        }
        return sb.toString();
    }

    private GiftDTO convertToGiftDTO(Gift gift) {
        GiftDTO dto = new GiftDTO();
        dto.setId(gift.getId());
        dto.setGiftCode(gift.getGiftCode());
        dto.setSenderId(gift.getSenderId());
        dto.setReceiverId(gift.getReceiverId());
        dto.setGooseId(gift.getGooseId());
        dto.setMessage(gift.getMessage());
        dto.setStatus(gift.getStatus());
        dto.setStatusDescription(gift.getStatusDescription());
        dto.setExpiresAt(gift.getExpiresAt());
        dto.setReceivedAt(gift.getReceivedAt());
        dto.setCreatedAt(gift.getCreatedAt());
        dto.setUpdatedAt(gift.getUpdatedAt());
        dto.setShareUrl(getShareUrl(gift.getGiftCode()));
        dto.setRemainingHours(gift.getRemainingHours());
        dto.setCanBeReceived(gift.canBeReceived());
        dto.setCanBeCancelled(gift.canBeCancelled());
        dto.setIsExpired(gift.isExpiredByTime());

        // 设置发送者信息
        if (gift.getSenderId() != null) {
            User sender = userMapper.selectById(gift.getSenderId());
            if (sender != null) {
                GiftDTO.UserBasicDTO senderDTO = new GiftDTO.UserBasicDTO();
                senderDTO.setId(sender.getId());
                senderDTO.setUsername(sender.getUsername());
                senderDTO.setNickname(sender.getNickname());
                senderDTO.setAvatar(sender.getAvatar());
                dto.setSender(senderDTO);
            }
        }

        // 设置接收者信息
        if (gift.getReceiverId() != null) {
            User receiver = userMapper.selectById(gift.getReceiverId());
            if (receiver != null) {
                GiftDTO.UserBasicDTO receiverDTO = new GiftDTO.UserBasicDTO();
                receiverDTO.setId(receiver.getId());
                receiverDTO.setUsername(receiver.getUsername());
                receiverDTO.setNickname(receiver.getNickname());
                receiverDTO.setAvatar(receiver.getAvatar());
                dto.setReceiver(receiverDTO);
            }
        }

        // 设置小鹅信息
        Goose goose = gooseMapper.selectById(gift.getGooseId());
        if (goose != null) {
            GiftDTO.GooseBasicDTO gooseDTO = new GiftDTO.GooseBasicDTO();
            gooseDTO.setId(goose.getId());
            gooseDTO.setName(goose.getName());
            gooseDTO.setBreed(goose.getBreed());
            gooseDTO.setGender(goose.getGender());
            gooseDTO.setGenderDescription(Goose.Gender.fromCode(goose.getGender()).getDescription());
            gooseDTO.setAge(goose.getAge());
            gooseDTO.setLevel(goose.getLevel());
            gooseDTO.setImageUrl(goose.getImageUrl());
            gooseDTO.setHealth(goose.getHealth());
            gooseDTO.setOverallScore(goose.getOverallScore());
            dto.setGoose(gooseDTO);
        }

        return dto;
    }

    @Override
    @Transactional(readOnly = true)
    public Page<GiftListDTO> getSentGifts(Long userId, Integer status, Pageable pageable) {
        log.info("查询用户发送的转赠列表: userId={}, status={}", userId, status);

        // 转换分页参数
        com.baomidou.mybatisplus.extension.plugins.pagination.Page<Gift> page =
            new com.baomidou.mybatisplus.extension.plugins.pagination.Page<>(
                pageable.getPageNumber() + 1, pageable.getPageSize());

        IPage<Gift> gifts;
        if (status != null) {
            gifts = getBaseMapper().findBySenderIdAndStatus(userId, status, page);
        } else {
            gifts = getBaseMapper().findBySenderId(userId, page);
        }

        List<GiftListDTO> giftListDTOs = gifts.getRecords().stream()
                .map(this::convertToGiftListDTO)
                .collect(Collectors.toList());

        return new PageImpl<>(giftListDTOs, pageable, gifts.getTotal());
    }

    @Override
    @Transactional(readOnly = true)
    public Page<GiftListDTO> getReceivedGifts(Long userId, Integer status, Pageable pageable) {
        log.info("查询用户接收的转赠列表: userId={}, status={}", userId, status);

        // 转换分页参数
        com.baomidou.mybatisplus.extension.plugins.pagination.Page<Gift> page =
            new com.baomidou.mybatisplus.extension.plugins.pagination.Page<>(
                pageable.getPageNumber() + 1, pageable.getPageSize());

        IPage<Gift> gifts;
        if (status != null) {
            gifts = getBaseMapper().findByReceiverIdAndStatus(userId, status, page);
        } else {
            gifts = getBaseMapper().findByReceiverId(userId, page);
        }

        List<GiftListDTO> giftListDTOs = gifts.getRecords().stream()
                .map(this::convertToGiftListDTO)
                .collect(Collectors.toList());

        return new PageImpl<>(giftListDTOs, pageable, gifts.getTotal());
    }

    @Override
    @Transactional(readOnly = true)
    public GiftStatisticsDTO getGiftStatistics(Long userId) {
        log.info("查询用户转赠统计: userId={}", userId);

        GiftStatisticsDTO statistics = new GiftStatisticsDTO();

        // 基础统计
        statistics.setTotalSent(getBaseMapper().countBySenderId(userId));
        statistics.setTotalReceived(getBaseMapper().countByReceiverId(userId));
        statistics.setPendingReceived(getBaseMapper().countByReceiverIdAndStatus(userId, Gift.Status.PENDING.getCode()));
        statistics.setCompletedSent(getBaseMapper().countBySenderIdAndStatus(userId, Gift.Status.COMPLETED.getCode()));
        statistics.setExpiredSent(getBaseMapper().countBySenderIdAndStatus(userId, Gift.Status.EXPIRED.getCode()));

        // 今日统计
        LocalDateTime startOfDay = LocalDateTime.now().withHour(0).withMinute(0).withSecond(0).withNano(0);
        LocalDateTime endOfDay = startOfDay.plusDays(1).minusNanos(1);
        statistics.setTodaySent(getBaseMapper().countBySenderIdAndCreatedAtBetween(userId, startOfDay, endOfDay));
        statistics.setTodayRemaining((long) Math.max(0, maxGiftsPerDay - statistics.getTodaySent().intValue()));
        statistics.setDailyLimit((long) maxGiftsPerDay);

        // 本周统计
        LocalDateTime startOfWeek = LocalDateTime.now().minusDays(LocalDateTime.now().getDayOfWeek().getValue() - 1)
                .withHour(0).withMinute(0).withSecond(0).withNano(0);
        LocalDateTime endOfWeek = startOfWeek.plusDays(7).minusNanos(1);
        statistics.setWeekSent(getBaseMapper().countBySenderIdAndCreatedAtBetween(userId, startOfWeek, endOfWeek));

        // 本月统计
        LocalDateTime startOfMonth = LocalDateTime.now().withDayOfMonth(1)
                .withHour(0).withMinute(0).withSecond(0).withNano(0);
        LocalDateTime endOfMonth = startOfMonth.plusMonths(1).minusNanos(1);
        statistics.setMonthSent(getBaseMapper().countBySenderIdAndCreatedAtBetween(userId, startOfMonth, endOfMonth));

        // 计算成功率
        if (statistics.getTotalSent() > 0) {
            Long receivedCount = getBaseMapper().countBySenderIdAndStatus(userId, Gift.Status.RECEIVED.getCode()) +
                               getBaseMapper().countBySenderIdAndStatus(userId, Gift.Status.COMPLETED.getCode());
            statistics.setSuccessRate((double) receivedCount / statistics.getTotalSent() * 100);
        } else {
            statistics.setSuccessRate(0.0);
        }

        // 活跃度评分（简化计算）
        int activityScore = Math.min(100,
            (int) (statistics.getTotalSent() * 10 + statistics.getTotalReceived() * 5));
        statistics.setActivityScore(activityScore);

        return statistics;
    }

    @Override
    @Transactional(readOnly = true)
    public Boolean canGiftGoose(Long userId, Long gooseId) {
        // 1. 验证小鹅是否存在
        Goose goose = gooseMapper.selectById(gooseId);
        if (goose == null) {
            return false;
        }

        // 2. 验证归属权
        if (!goose.getUserId().equals(userId)) {
            return false;
        }

        // 3. 验证小鹅状态
        if (!goose.isOwned() || goose.isGifted()) {
            return false;
        }

        // 4. 检查是否有未完成的转赠
        List<Gift> existingGifts = getBaseMapper().findValidGiftsByGooseId(
                gooseId, Gift.Status.PENDING.getCode(), LocalDateTime.now());

        return existingGifts.isEmpty();
    }

    @Override
    @Transactional(readOnly = true)
    public Boolean hasReachedDailyLimit(Long userId) {
        LocalDateTime startOfDay = LocalDateTime.now().withHour(0).withMinute(0).withSecond(0).withNano(0);
        LocalDateTime endOfDay = startOfDay.plusDays(1).minusNanos(1);

        Long todayCount = getBaseMapper().countBySenderIdAndCreatedAtBetween(userId, startOfDay, endOfDay);
        return todayCount >= maxGiftsPerDay;
    }

    @Override
    @Transactional(readOnly = true)
    public Integer getRemainingDailyGifts(Long userId) {
        LocalDateTime startOfDay = LocalDateTime.now().withHour(0).withMinute(0).withSecond(0).withNano(0);
        LocalDateTime endOfDay = startOfDay.plusDays(1).minusNanos(1);

        Long todayCount = getBaseMapper().countBySenderIdAndCreatedAtBetween(userId, startOfDay, endOfDay);
        return Math.max(0, maxGiftsPerDay - todayCount.intValue());
    }

    @Override
    @Transactional
    public Integer processExpiredGifts() {
        log.info("开始处理过期转赠");

        List<Gift> expiredGifts = getBaseMapper().findExpiredGifts(
                LocalDateTime.now(), Gift.Status.PENDING.getCode());

        int processedCount = 0;
        for (Gift gift : expiredGifts) {
            try {
                // 更新转赠状态为过期
                gift.markAsExpired();
                updateById(gift);

                // 恢复小鹅状态
                Goose goose = gooseMapper.selectById(gift.getGooseId());
                if (goose != null) {
                    goose.setStatus(Goose.Status.NORMAL.getCode());
                    gooseMapper.updateById(goose);
                }

                processedCount++;
                log.debug("处理过期转赠: giftCode={}", gift.getGiftCode());
            } catch (Exception e) {
                log.error("处理过期转赠失败: giftCode={}, error={}", gift.getGiftCode(), e.getMessage());
            }
        }

        log.info("过期转赠处理完成: 处理数量={}", processedCount);
        return processedCount;
    }

    @Override
    @Transactional(readOnly = true)
    public Page<GiftDTO> getGiftsExpiringWithin24Hours(Pageable pageable) {
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime in24Hours = now.plusHours(24);

        List<Gift> gifts = getBaseMapper().findGiftsExpiringWithin24Hours(
                now, in24Hours, Gift.Status.PENDING.getCode());

        List<GiftDTO> giftDTOs = gifts.stream()
                .map(this::convertToGiftDTO)
                .collect(Collectors.toList());

        return new PageImpl<>(giftDTOs, pageable, gifts.size());
    }

    @Override
    public Boolean isValidGiftCodeFormat(String giftCode) {
        if (giftCode == null || giftCode.trim().isEmpty()) {
            return false;
        }

        // 检查格式: GIFT + 14位时间戳 + 4位随机字符
        return giftCode.matches("^GIFT\\d{14}[A-Z0-9]{4}$");
    }

    @Override
    public String getShareUrl(String giftCode) {
        return appDomain + "/gift/" + giftCode;
    }

    private GiftListDTO convertToGiftListDTO(Gift gift) {
        GiftListDTO dto = new GiftListDTO();
        dto.setId(gift.getId());
        dto.setGiftCode(gift.getGiftCode());
        dto.setMessage(gift.getMessage());
        dto.setStatus(gift.getStatus());
        dto.setStatusDescription(gift.getStatusDescription());
        dto.setExpiresAt(gift.getExpiresAt());
        dto.setReceivedAt(gift.getReceivedAt());
        dto.setCreatedAt(gift.getCreatedAt());
        dto.setRemainingHours(gift.getRemainingHours());

        // 设置小鹅信息
        Goose goose = gooseMapper.selectById(gift.getGooseId());
        if (goose != null) {
            dto.setGooseName(goose.getName());
            dto.setGooseBreed(goose.getBreed());
            dto.setGooseImageUrl(goose.getImageUrl());
            dto.setGooseLevel(goose.getLevel());
        }

        // 设置操作状态
        if (gift.canBeReceived()) {
            dto.setCanOperate(true);
            dto.setOperationType("receive");
        } else if (gift.canBeCancelled()) {
            dto.setCanOperate(true);
            dto.setOperationType("cancel");
        } else {
            dto.setCanOperate(false);
            dto.setOperationType("none");
        }

        return dto;
    }
}
