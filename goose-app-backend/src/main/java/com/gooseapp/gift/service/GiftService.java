package com.gooseapp.gift.service;

import com.gooseapp.gift.dto.*;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

/**
 * 转赠服务接口
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-12-19
 */
public interface GiftService {

    /**
     * 创建转赠
     *
     * @param userId 用户ID
     * @param request 创建转赠请求
     * @return 转赠信息
     */
    GiftDTO createGift(Long userId, GiftCreateRequest request);

    /**
     * 根据转赠码获取转赠详情
     *
     * @param giftCode 转赠码
     * @return 转赠详情
     */
    GiftDTO getGiftByCode(String giftCode);

    /**
     * 接收转赠
     *
     * @param userId 用户ID
     * @param giftCode 转赠码
     * @param request 接收转赠请求
     * @return 转赠信息
     */
    GiftDTO receiveGift(Long userId, String giftCode, GiftReceiveRequest request);

    /**
     * 取消转赠
     *
     * @param userId 用户ID
     * @param giftCode 转赠码
     * @return 是否成功
     */
    Boolean cancelGift(Long userId, String giftCode);

    /**
     * 获取用户发送的转赠列表
     *
     * @param userId 用户ID
     * @param status 状态筛选（可选）
     * @param pageable 分页参数
     * @return 转赠列表
     */
    Page<GiftListDTO> getSentGifts(Long userId, Integer status, Pageable pageable);

    /**
     * 获取用户接收的转赠列表
     *
     * @param userId 用户ID
     * @param status 状态筛选（可选）
     * @param pageable 分页参数
     * @return 转赠列表
     */
    Page<GiftListDTO> getReceivedGifts(Long userId, Integer status, Pageable pageable);

    /**
     * 获取转赠统计信息
     *
     * @param userId 用户ID
     * @return 转赠统计
     */
    GiftStatisticsDTO getGiftStatistics(Long userId);

    /**
     * 检查用户是否可以转赠指定小鹅
     *
     * @param userId 用户ID
     * @param gooseId 小鹅ID
     * @return 是否可以转赠
     */
    Boolean canGiftGoose(Long userId, Long gooseId);

    /**
     * 检查用户今日转赠次数是否已达上限
     *
     * @param userId 用户ID
     * @return 是否已达上限
     */
    Boolean hasReachedDailyLimit(Long userId);

    /**
     * 获取用户今日剩余转赠次数
     *
     * @param userId 用户ID
     * @return 剩余次数
     */
    Integer getRemainingDailyGifts(Long userId);

    /**
     * 生成转赠码
     *
     * @return 转赠码
     */
    String generateGiftCode();

    /**
     * 处理过期转赠（定时任务调用）
     *
     * @return 处理的过期转赠数量
     */
    Integer processExpiredGifts();

    /**
     * 获取即将过期的转赠列表（用于提醒）
     *
     * @return 即将过期的转赠列表
     */
    Page<GiftDTO> getGiftsExpiringWithin24Hours(Pageable pageable);

    /**
     * 验证转赠码格式
     *
     * @param giftCode 转赠码
     * @return 是否有效
     */
    Boolean isValidGiftCodeFormat(String giftCode);

    /**
     * 获取转赠分享链接
     *
     * @param giftCode 转赠码
     * @return 分享链接
     */
    String getShareUrl(String giftCode);
}
