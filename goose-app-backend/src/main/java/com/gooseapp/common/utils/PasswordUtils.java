package com.gooseapp.common.utils;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.stereotype.Component;

/**
 * 密码工具类
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-12-19
 */
@Slf4j
@Component
public class PasswordUtils {

    private final BCryptPasswordEncoder passwordEncoder;

    @Value("${app.password.strength:10}")
    private int strength;

    public PasswordUtils() {
        this.passwordEncoder = new BCryptPasswordEncoder();
    }

    /**
     * 加密密码
     * 
     * @param rawPassword 原始密码
     * @return 加密后的密码
     */
    public String encode(String rawPassword) {
        if (rawPassword == null || rawPassword.isEmpty()) {
            throw new IllegalArgumentException("密码不能为空");
        }
        
        try {
            String encodedPassword = passwordEncoder.encode(rawPassword);
            log.debug("密码加密成功");
            return encodedPassword;
        } catch (Exception e) {
            log.error("密码加密失败: {}", e.getMessage());
            throw new RuntimeException("密码加密失败", e);
        }
    }

    /**
     * 验证密码
     * 
     * @param rawPassword 原始密码
     * @param encodedPassword 加密后的密码
     * @return 是否匹配
     */
    public boolean matches(String rawPassword, String encodedPassword) {
        if (rawPassword == null || encodedPassword == null) {
            return false;
        }
        
        try {
            boolean matches = passwordEncoder.matches(rawPassword, encodedPassword);
            log.debug("密码验证结果: {}", matches);
            return matches;
        } catch (Exception e) {
            log.error("密码验证失败: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 检查密码强度
     * 
     * @param password 密码
     * @return 密码强度等级 (1-5)
     */
    public int checkPasswordStrength(String password) {
        if (password == null || password.isEmpty()) {
            return 0;
        }

        int score = 0;
        
        // 长度检查
        if (password.length() >= 8) {
            score++;
        }
        if (password.length() >= 12) {
            score++;
        }
        
        // 包含小写字母
        if (password.matches(".*[a-z].*")) {
            score++;
        }
        
        // 包含大写字母
        if (password.matches(".*[A-Z].*")) {
            score++;
        }
        
        // 包含数字
        if (password.matches(".*\\d.*")) {
            score++;
        }
        
        // 包含特殊字符
        if (password.matches(".*[!@#$%^&*()_+\\-=\\[\\]{};':\"\\\\|,.<>\\/?].*")) {
            score++;
        }
        
        return Math.min(score, 5);
    }

    /**
     * 验证密码格式
     * 
     * @param password 密码
     * @return 是否符合格式要求
     */
    public boolean isValidPassword(String password) {
        if (password == null || password.isEmpty()) {
            return false;
        }
        
        // 长度检查
        if (password.length() < 6 || password.length() > 20) {
            return false;
        }
        
        // 必须包含字母和数字
        boolean hasLetter = password.matches(".*[a-zA-Z].*");
        boolean hasDigit = password.matches(".*\\d.*");
        
        return hasLetter && hasDigit;
    }

    /**
     * 生成随机密码
     * 
     * @param length 密码长度
     * @return 随机密码
     */
    public String generateRandomPassword(int length) {
        if (length < 6 || length > 50) {
            throw new IllegalArgumentException("密码长度必须在6-50之间");
        }
        
        String chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*";
        StringBuilder password = new StringBuilder();
        
        // 确保包含至少一个大写字母、小写字母、数字和特殊字符
        password.append("ABCDEFGHIJKLMNOPQRSTUVWXYZ".charAt((int) (Math.random() * 26)));
        password.append("abcdefghijklmnopqrstuvwxyz".charAt((int) (Math.random() * 26)));
        password.append("0123456789".charAt((int) (Math.random() * 10)));
        password.append("!@#$%^&*".charAt((int) (Math.random() * 8)));
        
        // 填充剩余长度
        for (int i = 4; i < length; i++) {
            password.append(chars.charAt((int) (Math.random() * chars.length())));
        }
        
        // 打乱字符顺序
        char[] passwordArray = password.toString().toCharArray();
        for (int i = 0; i < passwordArray.length; i++) {
            int randomIndex = (int) (Math.random() * passwordArray.length);
            char temp = passwordArray[i];
            passwordArray[i] = passwordArray[randomIndex];
            passwordArray[randomIndex] = temp;
        }
        
        return new String(passwordArray);
    }

    /**
     * 获取密码强度描述
     * 
     * @param strength 强度等级
     * @return 强度描述
     */
    public String getPasswordStrengthDescription(int strength) {
        return switch (strength) {
            case 0, 1 -> "很弱";
            case 2 -> "弱";
            case 3 -> "中等";
            case 4 -> "强";
            case 5 -> "很强";
            default -> "未知";
        };
    }
}
