package com.gooseapp.common.config;

import cn.dev33.satoken.stp.StpUtil;
import com.baomidou.mybatisplus.annotation.DbType;
import com.baomidou.mybatisplus.core.handlers.MetaObjectHandler;
import com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor;
import com.baomidou.mybatisplus.extension.plugins.inner.PaginationInnerInterceptor;
import org.apache.ibatis.reflection.MetaObject;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;

/**
 * MyBatis Plus 配置类
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-12-19
 */
@Configuration
public class MyBatisPlusConfig {

    /**
     * 分页插件配置
     */
    @Bean
    public MybatisPlusInterceptor mybatisPlusInterceptor() {
        MybatisPlusInterceptor interceptor = new MybatisPlusInterceptor();
        interceptor.addInnerInterceptor(new PaginationInnerInterceptor(DbType.MYSQL)); // 如果配置多个插件, 切记分页最后添加
        // 如果有多数据源可以不配具体类型, 否则都建议配上具体的 DbType
        return interceptor;
    }

    /**
     * 自动填充处理器
     */
    @Component
    public static class MyMetaObjectHandler implements MetaObjectHandler {

        /**
         * 插入时自动填充
         */
        @Override
        public void insertFill(MetaObject metaObject) {
            LocalDateTime now = LocalDateTime.now();

            // 填充创建时间
            this.strictInsertFill(metaObject, "createdAt", LocalDateTime.class, now);
            // 填充更新时间
            this.strictInsertFill(metaObject, "updatedAt", LocalDateTime.class, now);

            Long currentUserId = getCurrentUserId();
            if (currentUserId != null) {
                // 填充创建用户
                this.strictInsertFill(metaObject, "createUser", Long.class, currentUserId);
                // 填充更新用户
                this.strictInsertFill(metaObject, "updateUser", Long.class, currentUserId);
            }
        }

        /**
         * 更新时自动填充
         */
        @Override
        public void updateFill(MetaObject metaObject) {
            LocalDateTime now = LocalDateTime.now();

            // 填充更新时间
            this.strictUpdateFill(metaObject, "updatedAt", LocalDateTime.class, now);

            Long currentUserId = getCurrentUserId();
            if (currentUserId != null) {
                // 填充更新用户
                this.strictUpdateFill(metaObject, "updateUser", Long.class, currentUserId);
            }
        }

        /**
         * 获取当前登录用户ID
         */
        private Long getCurrentUserId() {
            try {
                if (StpUtil.isLogin()) {
                    return StpUtil.getLoginIdAsLong();
                }
                return null;
            } catch (Exception e) {
                // 在极少数情况下，即使isLogin()为true，getLoginIdAsLong()也可能因其他原因失败。
                // 在这种情况下，返回null也是一种安全的降级策略，可以防止数据污染。
                // 生产环境中，此处应添加日志记录。
                return null;
            }
        }
    }
}
