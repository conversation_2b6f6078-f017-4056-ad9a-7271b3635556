package com.gooseapp.common.annotation;

import java.lang.annotation.*;

/**
 * 管理员权限验证注解
 * 
 * 用于标记需要管理员权限才能访问的方法或类
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-12-19
 */
@Target({ElementType.METHOD, ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface AdminRequired {

    /**
     * 权限验证失败时的提示信息
     * 
     * @return 提示信息
     */
    String message() default "需要管理员权限才能访问此资源";

    /**
     * 是否允许跳过权限验证（用于测试环境）
     * 
     * @return 是否允许跳过
     */
    boolean skipInTest() default false;

    /**
     * 权限验证的优先级（数值越小优先级越高）
     * 
     * @return 优先级
     */
    int priority() default 0;
}
