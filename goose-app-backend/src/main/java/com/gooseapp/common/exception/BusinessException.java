package com.gooseapp.common.exception;

import com.gooseapp.common.constant.ErrorCode;
import lombok.Getter;

/**
 * 业务异常类
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-12-19
 */
@Getter
public class BusinessException extends RuntimeException {

    /**
     * 错误码
     */
    private final Integer code;

    /**
     * 错误消息
     */
    private final String message;

    /**
     * 错误详情
     */
    private final String detail;

    public BusinessException(ErrorCode errorCode) {
        super(errorCode.getMessage());
        this.code = errorCode.getCode();
        this.message = errorCode.getMessage();
        this.detail = null;
    }

    public BusinessException(ErrorCode errorCode, String detail) {
        super(errorCode.getMessage());
        this.code = errorCode.getCode();
        this.message = errorCode.getMessage();
        this.detail = detail;
    }

    public BusinessException(Integer code, String message) {
        super(message);
        this.code = code;
        this.message = message;
        this.detail = null;
    }

    public BusinessException(Integer code, String message, String detail) {
        super(message);
        this.code = code;
        this.message = message;
        this.detail = detail;
    }

    public BusinessException(ErrorCode errorCode, Throwable cause) {
        super(errorCode.getMessage(), cause);
        this.code = errorCode.getCode();
        this.message = errorCode.getMessage();
        this.detail = cause.getMessage();
    }

    public BusinessException(Integer code, String message, Throwable cause) {
        super(message, cause);
        this.code = code;
        this.message = message;
        this.detail = cause.getMessage();
    }

    @Override
    public String getMessage() {
        return message;
    }

    /**
     * 获取完整的错误信息
     */
    public String getFullMessage() {
        if (detail != null && !detail.isEmpty()) {
            return message + ": " + detail;
        }
        return message;
    }
}
