package com.gooseapp.common.constant;

/**
 * 错误码定义
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-12-19
 */
public enum ErrorCode {

    // 成功
    SUCCESS(200, "操作成功"),

    // 客户端错误 4xx
    BAD_REQUEST(400, "请求参数错误"),
    UNAUTHORIZED(401, "未授权访问"),
    FORBIDDEN(403, "禁止访问"),
    NOT_FOUND(404, "资源不存在"),
    METHOD_NOT_ALLOWED(405, "请求方法不允许"),
    CONFLICT(409, "资源冲突"),

    // 服务器错误 5xx
    INTERNAL_ERROR(500, "服务器内部错误"),
    SERVICE_UNAVAILABLE(503, "服务不可用"),

    // 用户相关错误 6001-6099
    USER_NOT_FOUND(6001, "用户不存在"),
    USER_ALREADY_EXISTS(6002, "用户已存在"),
    USERNAME_ALREADY_EXISTS(6003, "用户名已存在"),
    EMAIL_ALREADY_EXISTS(6004, "邮箱已被注册"),
    PHONE_ALREADY_EXISTS(6005, "手机号已被注册"),
    PASSWORD_ERROR(6006, "密码错误"),
    PASSWORD_NOT_MATCH(6007, "两次输入的密码不一致"),
    USER_DISABLED(6008, "用户已被禁用"),
    USER_STATUS_ERROR(6009, "用户状态异常"),

    // Token相关错误 6100-6199
    TOKEN_EXPIRED(6101, "Token已过期"),
    TOKEN_INVALID(6102, "Token无效"),
    TOKEN_MISSING(6103, "Token缺失"),
    TOKEN_REFRESH_FAILED(6104, "Token刷新失败"),
    LOGIN_REQUIRED(6105, "请先登录"),

    // 小鹅相关错误 6200-6299
    GOOSE_NOT_FOUND(6201, "小鹅不存在"),
    GOOSE_NOT_OWNED(6202, "小鹅不属于当前用户"),
    GOOSE_STATUS_ERROR(6203, "小鹅状态异常"),
    INTERACTION_COOLDOWN(6204, "互动冷却中"),
    INTERACTION_TYPE_INVALID(6205, "互动类型无效"),

    // 商品相关错误 6300-6399
    PRODUCT_NOT_FOUND(6301, "商品不存在"),
    PRODUCT_OUT_OF_STOCK(6302, "商品库存不足"),
    PRODUCT_STATUS_ERROR(6303, "商品状态异常"),

    // 订单相关错误 6400-6499
    ORDER_NOT_FOUND(6401, "订单不存在"),
    ORDER_STATUS_ERROR(6402, "订单状态错误"),
    ORDER_PAYMENT_FAILED(6403, "订单支付失败"),
    ORDER_ALREADY_PAID(6404, "订单已支付"),
    ORDER_ALREADY_CANCELLED(6405, "订单已取消"),

    // 转赠相关错误 6500-6599
    GIFT_NOT_FOUND(6501, "转赠记录不存在"),
    GIFT_EXPIRED(6502, "转赠已过期"),
    GIFT_ALREADY_RECEIVED(6503, "转赠已被领取"),
    GIFT_CODE_INVALID(6504, "转赠码无效"),
    GIFT_LIMIT_EXCEEDED(6505, "转赠次数超限"),

    // 文件上传相关错误 6600-6699
    FILE_UPLOAD_FAILED(6601, "文件上传失败"),
    FILE_TYPE_NOT_SUPPORTED(6602, "文件类型不支持"),
    FILE_SIZE_EXCEEDED(6603, "文件大小超限"),
    FILE_NOT_FOUND(6604, "文件不存在"),

    // 验证相关错误 6700-6719
    VALIDATION_ERROR(6701, "参数验证失败"),
    CAPTCHA_ERROR(6702, "验证码错误"),
    CAPTCHA_EXPIRED(6703, "验证码已过期"),

    // 小鹅扩展错误 6720-6739
    GOOSE_NOT_AVAILABLE(6726, "小鹅不可购买"),
    GOOSE_ALREADY_PURCHASED(6727, "小鹅已被购买"),

    // 购买相关错误 6740-6749
    PURCHASE_FAILED(6741, "购买失败"),
    PURCHASE_NOT_FOUND(6742, "购买记录不存在"),
    INSUFFICIENT_BALANCE(6743, "余额不足"),
    PURCHASE_PRICE_INVALID(6744, "购买价格无效"),

    // 权限相关错误 6750-6799
    ROLE_NOT_FOUND(6751, "角色不存在"),
    ROLE_ALREADY_EXISTS(6752, "角色已存在"),
    PERMISSION_DENIED(6753, "权限不足"),
    ADMIN_REQUIRED(6754, "需要管理员权限"),
    ROLE_OPERATION_DENIED(6755, "角色操作被拒绝"),
    LAST_ADMIN_CANNOT_BE_REMOVED(6756, "不能移除最后一个管理员"),

    // 业务规则错误 6800-6899
    BUSINESS_RULE_VIOLATION(6801, "违反业务规则"),
    OPERATION_NOT_ALLOWED(6802, "操作不被允许"),
    RESOURCE_LOCKED(6803, "资源被锁定"),
    CONCURRENT_OPERATION(6804, "并发操作冲突"),

    // 系统错误
    INTERNAL_SERVER_ERROR(500, "服务器内部错误"),
    SYSTEM_ERROR(500, "系统错误");

    private final Integer code;
    private final String message;

    ErrorCode(Integer code, String message) {
        this.code = code;
        this.message = message;
    }

    public Integer getCode() {
        return code;
    }

    public String getMessage() {
        return message;
    }

    /**
     * 根据错误码获取错误信息
     */
    public static ErrorCode fromCode(Integer code) {
        for (ErrorCode errorCode : values()) {
            if (errorCode.code.equals(code)) {
                return errorCode;
            }
        }
        return INTERNAL_ERROR;
    }
}
