package com.gooseapp.common.interceptor;

import cn.dev33.satoken.stp.StpUtil;
import com.gooseapp.common.annotation.AdminRequired;
import com.gooseapp.common.constant.ErrorCode;
import com.gooseapp.common.exception.BusinessException;
import com.gooseapp.user.service.RoleService;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.HandlerInterceptor;

import java.lang.reflect.Method;

/**
 * 权限验证拦截器
 * 
 * 用于拦截带有@AdminRequired注解的请求，验证用户是否具有管理员权限
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-12-19
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class PermissionInterceptor implements HandlerInterceptor {

    private final RoleService roleService;

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        // 如果不是方法处理器，直接放行
        if (!(handler instanceof HandlerMethod)) {
            return true;
        }

        HandlerMethod handlerMethod = (HandlerMethod) handler;
        Method method = handlerMethod.getMethod();
        Class<?> clazz = handlerMethod.getBeanType();

        // 检查方法或类上是否有@AdminRequired注解
        AdminRequired methodAnnotation = method.getAnnotation(AdminRequired.class);
        AdminRequired classAnnotation = clazz.getAnnotation(AdminRequired.class);

        // 如果没有@AdminRequired注解，直接放行
        if (methodAnnotation == null && classAnnotation == null) {
            return true;
        }

        // 获取生效的注解（方法级别优先于类级别）
        AdminRequired effectiveAnnotation = methodAnnotation != null ? methodAnnotation : classAnnotation;

        try {
            // 检查用户是否已登录
            if (!StpUtil.isLogin()) {
                log.warn("未登录用户尝试访问管理员接口: {}", request.getRequestURI());
                throw new BusinessException(ErrorCode.UNAUTHORIZED, "请先登录");
            }

            // 获取当前登录用户ID
            Long userId = StpUtil.getLoginIdAsLong();
            
            // 验证用户是否具有管理员权限
            if (!roleService.isUserAdmin(userId)) {
                log.warn("用户[{}]尝试访问管理员接口但权限不足: {}", userId, request.getRequestURI());
                throw new BusinessException(ErrorCode.FORBIDDEN, effectiveAnnotation.message());
            }

            log.debug("管理员用户[{}]成功通过权限验证，访问接口: {}", userId, request.getRequestURI());
            return true;

        } catch (BusinessException e) {
            // 重新抛出业务异常
            throw e;
        } catch (Exception e) {
            log.error("权限验证过程中发生异常: {}", e.getMessage(), e);
            throw new BusinessException(ErrorCode.INTERNAL_SERVER_ERROR, "权限验证失败");
        }
    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) throws Exception {
        // 清理线程本地变量（如果有的话）
        // 这里可以添加一些清理逻辑
    }
}
