package com.gooseapp;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;

/**
 * 小鹅养成APP主启动类
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-12-19
 */
@SpringBootApplication
@MapperScan("com.gooseapp.**.mapper")
public class GooseAppApplication {

    public static void main(String[] args) {
        SpringApplication.run(GooseAppApplication.class, args);
        System.out.println("🦆 小鹅养成APP后端服务启动成功！");
        System.out.println("📖 API文档地址: http://localhost:8080/swagger-ui.html");
        System.out.println("🔧 开发环境: http://localhost:8080");
    }
}
