package com.gooseapp.goose.controller;

import cn.dev33.satoken.annotation.SaCheckLogin;
import cn.dev33.satoken.stp.StpUtil;
import com.gooseapp.common.response.ApiResponse;
import com.gooseapp.goose.dto.InteractionDTO;
import com.gooseapp.goose.service.GooseService;
import com.gooseapp.goose.service.InteractionService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 互动管理控制器
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-12-19
 */
@Slf4j
@RestController
@RequestMapping("/api/v1/interactions")
@RequiredArgsConstructor
@SaCheckLogin
@Tag(name = "互动管理", description = "小鹅互动相关接口")
public class InteractionController {

    private final GooseService gooseService;
    private final InteractionService interactionService;

    /**
     * 获取用户的所有互动记录
     */
    @GetMapping("/my")
    @Operation(summary = "获取我的互动记录", description = "获取当前用户的所有互动记录")
    public ApiResponse<List<InteractionDTO>> getMyInteractions(
            @Parameter(description = "页码") @RequestParam(defaultValue = "0") int page,
            @Parameter(description = "每页大小") @RequestParam(defaultValue = "20") int size) {
        
        Long userId = StpUtil.getLoginIdAsLong();
        
        log.info("获取用户互动记录: userId={}, page={}, size={}", userId, page, size);
        
        List<InteractionDTO> interactions = gooseService.getUserInteractions(userId, page, size);
        
        return ApiResponse.success("查询成功", interactions);
    }

    /**
     * 检查互动冷却状态
     */
    @GetMapping("/cooldown/{gooseId}")
    @Operation(summary = "检查互动冷却状态", description = "检查指定小鹅的所有互动类型冷却状态")
    public ApiResponse<Map<String, InteractionService.CooldownStatus>> getCooldownStatus(
            @Parameter(description = "小鹅ID") @PathVariable Long gooseId) {
        
        Long userId = StpUtil.getLoginIdAsLong();
        
        log.info("检查互动冷却状态: userId={}, gooseId={}", userId, gooseId);
        
        Map<String, InteractionService.CooldownStatus> cooldownStatus = 
                interactionService.getAllCooldownStatus(userId, gooseId);
        
        return ApiResponse.success("查询成功", cooldownStatus);
    }

    /**
     * 检查特定互动类型的冷却状态
     */
    @GetMapping("/cooldown/{gooseId}/{interactionType}")
    @Operation(summary = "检查特定互动冷却状态", description = "检查指定小鹅特定互动类型的冷却状态")
    public ApiResponse<CooldownStatusResponse> getSpecificCooldownStatus(
            @Parameter(description = "小鹅ID") @PathVariable Long gooseId,
            @Parameter(description = "互动类型") @PathVariable String interactionType) {
        
        Long userId = StpUtil.getLoginIdAsLong();
        
        log.info("检查特定互动冷却状态: userId={}, gooseId={}, type={}", 
                userId, gooseId, interactionType);
        
        boolean inCooldown = gooseService.isInteractionInCooldown(userId, gooseId, interactionType);
        long remainingMinutes = gooseService.getInteractionCooldownMinutes(userId, gooseId, interactionType);
        
        CooldownStatusResponse response = new CooldownStatusResponse();
        response.setInCooldown(inCooldown);
        response.setRemainingMinutes(remainingMinutes);
        response.setCanInteract(!inCooldown);
        
        return ApiResponse.success("查询成功", response);
    }

    /**
     * 获取今日互动统计
     */
    @GetMapping("/stats/today")
    @Operation(summary = "获取今日互动统计", description = "获取当前用户今日的互动统计信息")
    public ApiResponse<InteractionService.TodayInteractionStats> getTodayStats(
            @Parameter(description = "小鹅ID（可选）") @RequestParam(required = false) Long gooseId) {
        
        Long userId = StpUtil.getLoginIdAsLong();
        
        log.info("获取今日互动统计: userId={}, gooseId={}", userId, gooseId);
        
        InteractionService.TodayInteractionStats stats = 
                interactionService.getTodayStats(userId, gooseId);
        
        return ApiResponse.success("查询成功", stats);
    }

    /**
     * 获取互动类型统计
     */
    @GetMapping("/stats/types")
    @Operation(summary = "获取互动类型统计", description = "获取各种互动类型的统计数据")
    public ApiResponse<Map<String, Long>> getInteractionTypeStats() {
        Long userId = StpUtil.getLoginIdAsLong();
        
        log.info("获取互动类型统计: userId={}", userId);
        
        Map<String, Long> stats = interactionService.getInteractionTypeStats(userId);
        
        return ApiResponse.success("查询成功", stats);
    }

    /**
     * 获取最近7天互动统计
     */
    @GetMapping("/stats/weekly")
    @Operation(summary = "获取最近7天互动统计", description = "获取最近7天的每日互动统计")
    public ApiResponse<List<InteractionService.DailyInteractionStats>> getWeeklyStats() {
        Long userId = StpUtil.getLoginIdAsLong();
        
        log.info("获取最近7天互动统计: userId={}", userId);
        
        List<InteractionService.DailyInteractionStats> stats = 
                interactionService.getWeeklyStats(userId);
        
        return ApiResponse.success("查询成功", stats);
    }

    /**
     * 获取用户总互动统计
     */
    @GetMapping("/stats/total")
    @Operation(summary = "获取用户总互动统计", description = "获取用户的总体互动统计信息")
    public ApiResponse<InteractionService.UserInteractionStats> getUserTotalStats() {
        Long userId = StpUtil.getLoginIdAsLong();
        
        log.info("获取用户总互动统计: userId={}", userId);
        
        InteractionService.UserInteractionStats stats = 
                interactionService.getUserTotalStats(userId);
        
        return ApiResponse.success("查询成功", stats);
    }

    /**
     * 获取最活跃的小鹅
     */
    @GetMapping("/stats/active-geese")
    @Operation(summary = "获取最活跃的小鹅", description = "获取互动次数最多的小鹅排行")
    public ApiResponse<List<InteractionService.GooseActivityStats>> getMostActiveGeese(
            @Parameter(description = "返回数量") @RequestParam(defaultValue = "10") int limit) {
        
        Long userId = StpUtil.getLoginIdAsLong();
        
        log.info("获取最活跃的小鹅: userId={}, limit={}", userId, limit);
        
        List<InteractionService.GooseActivityStats> stats = 
                interactionService.getMostActiveGeese(userId, limit);
        
        return ApiResponse.success("查询成功", stats);
    }

    /**
     * 获取连续互动天数
     */
    @GetMapping("/stats/continuous-days")
    @Operation(summary = "获取连续互动天数", description = "获取用户连续互动的天数")
    public ApiResponse<ContinuousDaysResponse> getContinuousDays() {
        Long userId = StpUtil.getLoginIdAsLong();
        
        log.info("获取连续互动天数: userId={}", userId);
        
        long continuousDays = interactionService.calculateContinuousDays(userId);
        
        ContinuousDaysResponse response = new ContinuousDaysResponse();
        response.setContinuousDays(continuousDays);
        
        return ApiResponse.success("查询成功", response);
    }

    // ==================== 响应DTO类 ====================

    /**
     * 冷却状态响应
     */
    public static class CooldownStatusResponse {
        private Boolean inCooldown;
        private Long remainingMinutes;
        private Boolean canInteract;

        // getters and setters
        public Boolean getInCooldown() { return inCooldown; }
        public void setInCooldown(Boolean inCooldown) { this.inCooldown = inCooldown; }
        public Long getRemainingMinutes() { return remainingMinutes; }
        public void setRemainingMinutes(Long remainingMinutes) { this.remainingMinutes = remainingMinutes; }
        public Boolean getCanInteract() { return canInteract; }
        public void setCanInteract(Boolean canInteract) { this.canInteract = canInteract; }
    }

    /**
     * 连续天数响应
     */
    public static class ContinuousDaysResponse {
        private Long continuousDays;

        // getters and setters
        public Long getContinuousDays() { return continuousDays; }
        public void setContinuousDays(Long continuousDays) { this.continuousDays = continuousDays; }
    }
}
