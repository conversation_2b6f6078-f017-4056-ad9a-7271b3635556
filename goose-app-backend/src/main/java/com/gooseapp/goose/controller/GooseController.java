package com.gooseapp.goose.controller;

import cn.dev33.satoken.annotation.SaCheckLogin;
import cn.dev33.satoken.stp.StpUtil;
import com.gooseapp.common.response.ApiResponse;
import com.gooseapp.goose.dto.GooseDTO;
import com.gooseapp.goose.dto.GooseDetailDTO;
import com.gooseapp.goose.dto.InteractionDTO;
import com.gooseapp.goose.service.GooseService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 小鹅管理控制器
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-12-19
 */
@Slf4j
@RestController
@RequestMapping("/api/v1/geese")
@RequiredArgsConstructor
@SaCheckLogin
@Tag(name = "小鹅管理", description = "小鹅养成相关接口")
public class GooseController {

    private final GooseService gooseService;

    /**
     * 获取用户的小鹅列表
     */
    @GetMapping("/my")
    @Operation(summary = "获取我的小鹅列表", description = "获取当前用户的所有小鹅")
    public ApiResponse<List<GooseDTO>> getMyGeese() {
        Long userId = StpUtil.getLoginIdAsLong();

        log.info("获取用户小鹅列表: userId={}", userId);

        List<GooseDTO> geese = gooseService.getUserGeese(userId);

        return ApiResponse.success("查询成功", geese);
    }

    /**
     * 获取小鹅详细信息
     */
    @GetMapping("/{gooseId}")
    @Operation(summary = "获取小鹅详情", description = "获取指定小鹅的详细信息")
    public ApiResponse<GooseDetailDTO> getGooseDetail(
            @Parameter(description = "小鹅ID") @PathVariable Long gooseId) {

        Long userId = StpUtil.getLoginIdAsLong();

        log.info("获取小鹅详情: userId={}, gooseId={}", userId, gooseId);

        GooseDetailDTO gooseDetail = gooseService.getGooseDetail(userId, gooseId);

        return ApiResponse.success("查询成功", gooseDetail);
    }

    // 注意：用户不能直接创建小鹅，只能通过购买获得
    // 小鹅由管理员创建到商品池，用户通过商城购买

    /**
     * 更新小鹅名称
     */
    @PutMapping("/{gooseId}/name")
    @Operation(summary = "更新小鹅名称", description = "修改小鹅的名称")
    public ApiResponse<GooseDTO> updateGooseName(
            @Parameter(description = "小鹅ID") @PathVariable Long gooseId,
            @Valid @RequestBody UpdateGooseNameRequest request) {

        Long userId = StpUtil.getLoginIdAsLong();

        log.info("更新小鹅名称: userId={}, gooseId={}, newName={}",
                userId, gooseId, request.getName());

        GooseDTO goose = gooseService.updateGooseName(userId, gooseId, request.getName());

        return ApiResponse.success("更新成功", goose);
    }

    /**
     * 删除小鹅
     */
    @DeleteMapping("/{gooseId}")
    @Operation(summary = "删除小鹅", description = "删除指定的小鹅（软删除）")
    public ApiResponse<Void> deleteGoose(
            @Parameter(description = "小鹅ID") @PathVariable Long gooseId) {

        Long userId = StpUtil.getLoginIdAsLong();

        log.info("删除小鹅: userId={}, gooseId={}", userId, gooseId);

        gooseService.deleteGoose(userId, gooseId);

        return ApiResponse.success("删除成功");
    }

    /**
     * 喂食小鹅
     */
    @PostMapping("/{gooseId}/feed")
    @Operation(summary = "喂食小鹅", description = "给小鹅喂食")
    public ApiResponse<InteractionDTO> feedGoose(
            @Parameter(description = "小鹅ID") @PathVariable Long gooseId) {

        Long userId = StpUtil.getLoginIdAsLong();

        log.info("喂食小鹅: userId={}, gooseId={}", userId, gooseId);

        InteractionDTO interaction = gooseService.feedGoose(userId, gooseId);

        return ApiResponse.success("喂食成功", interaction);
    }

    /**
     * 给小鹅喂水
     */
    @PostMapping("/{gooseId}/water")
    @Operation(summary = "给小鹅喂水", description = "给小鹅喂水")
    public ApiResponse<InteractionDTO> waterGoose(
            @Parameter(description = "小鹅ID") @PathVariable Long gooseId) {

        Long userId = StpUtil.getLoginIdAsLong();

        log.info("给小鹅喂水: userId={}, gooseId={}", userId, gooseId);

        InteractionDTO interaction = gooseService.waterGoose(userId, gooseId);

        return ApiResponse.success("喂水成功", interaction);
    }

    /**
     * 清洁小鹅
     */
    @PostMapping("/{gooseId}/clean")
    @Operation(summary = "清洁小鹅", description = "给小鹅清洁")
    public ApiResponse<InteractionDTO> cleanGoose(
            @Parameter(description = "小鹅ID") @PathVariable Long gooseId) {

        Long userId = StpUtil.getLoginIdAsLong();

        log.info("清洁小鹅: userId={}, gooseId={}", userId, gooseId);

        InteractionDTO interaction = gooseService.cleanGoose(userId, gooseId);

        return ApiResponse.success("清洁成功", interaction);
    }

    /**
     * 与小鹅玩耍
     */
    @PostMapping("/{gooseId}/play")
    @Operation(summary = "与小鹅玩耍", description = "与小鹅一起玩耍")
    public ApiResponse<InteractionDTO> playWithGoose(
            @Parameter(description = "小鹅ID") @PathVariable Long gooseId) {

        Long userId = StpUtil.getLoginIdAsLong();

        log.info("与小鹅玩耍: userId={}, gooseId={}", userId, gooseId);

        InteractionDTO interaction = gooseService.playWithGoose(userId, gooseId);

        return ApiResponse.success("玩耍成功", interaction);
    }

    /**
     * 获取小鹅互动记录
     */
    @GetMapping("/{gooseId}/interactions")
    @Operation(summary = "获取小鹅互动记录", description = "获取指定小鹅的互动历史记录")
    public ApiResponse<List<InteractionDTO>> getGooseInteractions(
            @Parameter(description = "小鹅ID") @PathVariable Long gooseId,
            @Parameter(description = "页码") @RequestParam(defaultValue = "0") int page,
            @Parameter(description = "每页大小") @RequestParam(defaultValue = "20") int size) {

        Long userId = StpUtil.getLoginIdAsLong();

        log.info("获取小鹅互动记录: userId={}, gooseId={}, page={}, size={}",
                userId, gooseId, page, size);

        List<InteractionDTO> interactions = gooseService.getGooseInteractions(userId, gooseId, page, size);

        return ApiResponse.success("查询成功", interactions);
    }

    /**
     * 获取需要照顾的小鹅
     */
    @GetMapping("/need-care")
    @Operation(summary = "获取需要照顾的小鹅", description = "获取当前需要照顾的小鹅列表")
    public ApiResponse<List<GooseDTO>> getGeeseNeedingCare() {
        Long userId = StpUtil.getLoginIdAsLong();

        log.info("获取需要照顾的小鹅: userId={}", userId);

        List<GooseDTO> geese = gooseService.getGeeseNeedingCare(userId);

        return ApiResponse.success("查询成功", geese);
    }

    /**
     * 搜索小鹅
     */
    @GetMapping("/search")
    @Operation(summary = "搜索小鹅", description = "根据关键词搜索小鹅")
    public ApiResponse<List<GooseDTO>> searchGeese(
            @Parameter(description = "搜索关键词") @RequestParam String keyword) {

        Long userId = StpUtil.getLoginIdAsLong();

        log.info("搜索小鹅: userId={}, keyword={}", userId, keyword);

        List<GooseDTO> geese = gooseService.searchUserGeese(userId, keyword);

        return ApiResponse.success("搜索成功", geese);
    }

    /**
     * 获取小鹅统计信息
     */
    @GetMapping("/stats")
    @Operation(summary = "获取小鹅统计", description = "获取用户的小鹅统计信息")
    public ApiResponse<GooseService.GooseStatsDTO> getGooseStats() {
        Long userId = StpUtil.getLoginIdAsLong();

        log.info("获取小鹅统计: userId={}", userId);

        GooseService.GooseStatsDTO stats = gooseService.getUserGooseStats(userId);

        return ApiResponse.success("查询成功", stats);
    }

    // ==================== 请求DTO类 ====================

    /**
     * 更新小鹅名称请求
     */
    public static class UpdateGooseNameRequest {
        private String name;

        // getters and setters
        public String getName() { return name; }
        public void setName(String name) { this.name = name; }
    }
}
