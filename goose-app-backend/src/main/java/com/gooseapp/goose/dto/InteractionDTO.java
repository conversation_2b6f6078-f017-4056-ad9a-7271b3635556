package com.gooseapp.goose.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 互动记录DTO
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-12-19
 */
@Data
@Schema(description = "互动记录")
public class InteractionDTO {

    @Schema(description = "互动记录ID")
    private Long id;

    @Schema(description = "用户ID")
    private Long userId;

    @Schema(description = "小鹅ID")
    private Long gooseId;

    @Schema(description = "小鹅名称")
    private String gooseName;

    @Schema(description = "互动类型：feed-喂食，water-喂水，clean-清洁，play-玩耍")
    private String interactionType;

    @Schema(description = "互动类型描述")
    private String interactionTypeDescription;

    @Schema(description = "数值变化")
    private Integer valueChange;

    @Schema(description = "获得经验")
    private Integer experienceGained;

    @Schema(description = "冷却时间（分钟）")
    private Integer cooldownMinutes;

    @Schema(description = "是否在冷却期")
    private Boolean inCooldown;

    @Schema(description = "剩余冷却时间（分钟）")
    private Long remainingCooldownMinutes;

    @Schema(description = "冷却结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime cooldownEndTime;

    @Schema(description = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdAt;
}
