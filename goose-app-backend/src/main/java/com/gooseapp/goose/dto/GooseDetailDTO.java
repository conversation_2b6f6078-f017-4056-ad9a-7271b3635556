package com.gooseapp.goose.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 小鹅详细信息DTO
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-12-19
 */
@Data
@Schema(description = "小鹅详细信息")
public class GooseDetailDTO {

    @Schema(description = "小鹅ID")
    private Long id;

    @Schema(description = "所属用户ID")
    private Long userId;

    @Schema(description = "小鹅名称")
    private String name;

    @Schema(description = "品种")
    private String breed;

    @Schema(description = "品种描述")
    private String breedDescription;

    @Schema(description = "性别：1-雄性，2-雌性")
    private Integer gender;

    @Schema(description = "性别描述")
    private String genderDescription;

    @Schema(description = "年龄（周）")
    private Integer age;

    @Schema(description = "健康度（0-100）")
    private Integer health;

    @Schema(description = "饥饿度（0-100）")
    private Integer hunger;

    @Schema(description = "口渴度（0-100）")
    private Integer thirst;

    @Schema(description = "清洁度（0-100）")
    private Integer cleanliness;

    @Schema(description = "快乐度（0-100）")
    private Integer happiness;

    @Schema(description = "等级")
    private Integer level;

    @Schema(description = "经验值")
    private Integer experience;

    @Schema(description = "下一级所需经验")
    private Integer expToNextLevel;

    @Schema(description = "小鹅图片URL")
    private String imageUrl;

    @Schema(description = "直播视频URL")
    private String videoUrl;

    @Schema(description = "最后喂食时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime lastFeedTime;

    @Schema(description = "最后喂水时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime lastWaterTime;

    @Schema(description = "最后清洁时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime lastCleanTime;

    @Schema(description = "状态：0-已转赠，1-正常，2-生病")
    private Integer status;

    @Schema(description = "状态描述")
    private String statusDescription;

    @Schema(description = "整体状态评分（0-100）")
    private Double overallScore;

    @Schema(description = "是否需要照顾")
    private Boolean needsCare;

    @Schema(description = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdAt;

    @Schema(description = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updatedAt;

    @Schema(description = "互动冷却状态")
    private InteractionCooldownDTO cooldownStatus;

    @Schema(description = "最近互动记录")
    private List<InteractionDTO> recentInteractions;

    @Schema(description = "今日互动统计")
    private InteractionStatsDTO todayStats;

    /**
     * 互动冷却状态DTO
     */
    @Data
    @Schema(description = "互动冷却状态")
    public static class InteractionCooldownDTO {
        
        @Schema(description = "喂食冷却剩余时间（分钟）")
        private Long feedCooldownMinutes;
        
        @Schema(description = "喂水冷却剩余时间（分钟）")
        private Long waterCooldownMinutes;
        
        @Schema(description = "清洁冷却剩余时间（分钟）")
        private Long cleanCooldownMinutes;
        
        @Schema(description = "玩耍冷却剩余时间（分钟）")
        private Long playCooldownMinutes;
        
        @Schema(description = "是否可以喂食")
        private Boolean canFeed;
        
        @Schema(description = "是否可以喂水")
        private Boolean canWater;
        
        @Schema(description = "是否可以清洁")
        private Boolean canClean;
        
        @Schema(description = "是否可以玩耍")
        private Boolean canPlay;
    }

    /**
     * 互动统计DTO
     */
    @Data
    @Schema(description = "互动统计")
    public static class InteractionStatsDTO {
        
        @Schema(description = "今日总互动次数")
        private Long totalInteractions;
        
        @Schema(description = "今日喂食次数")
        private Long feedCount;
        
        @Schema(description = "今日喂水次数")
        private Long waterCount;
        
        @Schema(description = "今日清洁次数")
        private Long cleanCount;
        
        @Schema(description = "今日玩耍次数")
        private Long playCount;
        
        @Schema(description = "今日获得经验")
        private Long experienceGained;
    }
}
