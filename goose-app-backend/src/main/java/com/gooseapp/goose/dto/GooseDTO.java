package com.gooseapp.goose.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 小鹅基础信息DTO
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-12-19
 */
@Data
@Schema(description = "小鹅基础信息")
public class GooseDTO {

    @Schema(description = "小鹅ID")
    private Long id;

    @Schema(description = "所属用户ID")
    private Long userId;

    @Schema(description = "小鹅名称")
    private String name;

    @Schema(description = "品种")
    private String breed;

    @Schema(description = "性别：1-雄性，2-雌性")
    private Integer gender;

    @Schema(description = "年龄（周）")
    private Integer age;

    @Schema(description = "健康度（0-100）")
    private Integer health;

    @Schema(description = "饥饿度（0-100）")
    private Integer hunger;

    @Schema(description = "口渴度（0-100）")
    private Integer thirst;

    @Schema(description = "清洁度（0-100）")
    private Integer cleanliness;

    @Schema(description = "快乐度（0-100）")
    private Integer happiness;

    @Schema(description = "等级")
    private Integer level;

    @Schema(description = "经验值")
    private Integer experience;

    @Schema(description = "下一级所需经验")
    private Integer expToNextLevel;

    @Schema(description = "小鹅图片URL")
    private String imageUrl;

    @Schema(description = "直播视频URL")
    private String videoUrl;

    @Schema(description = "归属状态：0-待售，1-已归属")
    private Integer ownershipStatus;

    @Schema(description = "归属状态描述")
    private String ownershipStatusDescription;

    @Schema(description = "购买时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime purchaseTime;

    @Schema(description = "状态：0-已转赠，1-正常，2-生病")
    private Integer status;

    @Schema(description = "状态描述")
    private String statusDescription;

    @Schema(description = "整体状态评分（0-100）")
    private Double overallScore;

    @Schema(description = "是否需要照顾")
    private Boolean needsCare;

    @Schema(description = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdAt;

    @Schema(description = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updatedAt;
}
