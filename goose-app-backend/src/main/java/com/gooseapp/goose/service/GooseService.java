package com.gooseapp.goose.service;

import com.gooseapp.goose.dto.GooseDTO;
import com.gooseapp.goose.dto.GooseDetailDTO;
import com.gooseapp.goose.dto.InteractionDTO;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.List;

/**
 * 小鹅服务接口
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-12-19
 */
public interface GooseService {

    /**
     * 获取用户的小鹅列表
     *
     * @param userId 用户ID
     * @return 小鹅列表
     */
    List<GooseDTO> getUserGeese(Long userId);

    /**
     * 获取小鹅详细信息
     *
     * @param userId 用户ID
     * @param gooseId 小鹅ID
     * @return 小鹅详细信息
     */
    GooseDetailDTO getGooseDetail(Long userId, Long gooseId);

    /**
     * 创建新小鹅
     *
     * @param userId 用户ID
     * @param name 小鹅名称
     * @param breed 品种
     * @param gender 性别
     * @return 创建的小鹅信息
     */
    GooseDTO createGoose(Long userId, String name, String breed, Integer gender);

    /**
     * 更新小鹅名称
     *
     * @param userId 用户ID
     * @param gooseId 小鹅ID
     * @param newName 新名称
     * @return 更新后的小鹅信息
     */
    GooseDTO updateGooseName(Long userId, Long gooseId, String newName);

    /**
     * 删除小鹅（软删除，标记为已转赠）
     *
     * @param userId 用户ID
     * @param gooseId 小鹅ID
     */
    void deleteGoose(Long userId, Long gooseId);

    /**
     * 喂食小鹅
     *
     * @param userId 用户ID
     * @param gooseId 小鹅ID
     * @return 互动记录
     */
    InteractionDTO feedGoose(Long userId, Long gooseId);

    /**
     * 给小鹅喂水
     *
     * @param userId 用户ID
     * @param gooseId 小鹅ID
     * @return 互动记录
     */
    InteractionDTO waterGoose(Long userId, Long gooseId);

    /**
     * 清洁小鹅
     *
     * @param userId 用户ID
     * @param gooseId 小鹅ID
     * @return 互动记录
     */
    InteractionDTO cleanGoose(Long userId, Long gooseId);

    /**
     * 与小鹅玩耍
     *
     * @param userId 用户ID
     * @param gooseId 小鹅ID
     * @return 互动记录
     */
    InteractionDTO playWithGoose(Long userId, Long gooseId);

    /**
     * 获取小鹅的互动记录
     *
     * @param userId 用户ID
     * @param gooseId 小鹅ID
     * @param page 页码
     * @param size 每页大小
     * @return 互动记录列表
     */
    List<InteractionDTO> getGooseInteractions(Long userId, Long gooseId, int page, int size);

    /**
     * 获取用户的所有互动记录
     *
     * @param userId 用户ID
     * @param page 页码
     * @param size 每页大小
     * @return 互动记录列表
     */
    List<InteractionDTO> getUserInteractions(Long userId, int page, int size);

    /**
     * 获取需要照顾的小鹅列表
     *
     * @param userId 用户ID
     * @return 需要照顾的小鹅列表
     */
    List<GooseDTO> getGeeseNeedingCare(Long userId);

    /**
     * 获取用户小鹅统计信息
     *
     * @param userId 用户ID
     * @return 统计信息
     */
    GooseStatsDTO getUserGooseStats(Long userId);

    /**
     * 检查互动冷却状态
     *
     * @param userId 用户ID
     * @param gooseId 小鹅ID
     * @param interactionType 互动类型
     * @return 是否在冷却期
     */
    boolean isInteractionInCooldown(Long userId, Long gooseId, String interactionType);

    /**
     * 获取互动冷却剩余时间
     *
     * @param userId 用户ID
     * @param gooseId 小鹅ID
     * @param interactionType 互动类型
     * @return 剩余冷却时间（分钟）
     */
    long getInteractionCooldownMinutes(Long userId, Long gooseId, String interactionType);

    /**
     * 自动更新小鹅状态（定时任务调用）
     *
     * @param gooseId 小鹅ID
     */
    void autoUpdateGooseStatus(Long gooseId);

    /**
     * 批量自动更新所有小鹅状态
     */
    void autoUpdateAllGeeseStatus();

    /**
     * 检查并升级小鹅等级
     *
     * @param gooseId 小鹅ID
     * @return 是否升级成功
     */
    boolean checkAndLevelUpGoose(Long gooseId);

    /**
     * 搜索用户的小鹅
     *
     * @param userId 用户ID
     * @param keyword 搜索关键词
     * @return 搜索结果
     */
    List<GooseDTO> searchUserGeese(Long userId, String keyword);

    /**
     * 获取小鹅排行榜
     *
     * @param type 排行类型：level-等级，health-健康度，happiness-快乐度
     * @param limit 返回数量限制
     * @return 排行榜列表
     */
    List<GooseDTO> getGooseRanking(String type, int limit);

    // ========== 商城相关方法 ==========

    /**
     * 获取待售小鹅列表（分页）
     *
     * @param pageable 分页参数
     * @return 待售小鹅分页列表
     */
    Page<GooseDTO> getAvailableGeese(Pageable pageable);

    /**
     * 根据品种获取待售小鹅列表
     *
     * @param breed 品种
     * @return 待售小鹅列表
     */
    List<GooseDTO> getAvailableGeeseByBreed(String breed);

    /**
     * 根据ID获取小鹅信息
     *
     * @param gooseId 小鹅ID
     * @return 小鹅信息
     */
    GooseDTO getGooseById(Long gooseId);

    /**
     * 获取所有小鹅品种列表
     *
     * @return 品种列表
     */
    List<String> getAllBreeds();

    /**
     * 搜索可购买的小鹅
     *
     * @param keyword 搜索关键词
     * @param pageable 分页参数
     * @return 搜索结果分页列表
     */
    Page<GooseDTO> searchAvailableGeese(String keyword, Pageable pageable);

    /**
     * 小鹅统计信息DTO
     */
    interface GooseStatsDTO {
        Long getTotalGeese();
        Long getHealthyGeese();
        Long getSickGeese();
        Long getGeeseNeedingCare();
        Long getTotalInteractions();
        Long getTodayInteractions();
        Double getAverageLevel();
        Double getAverageHealth();
        Long getTotalExperience();
        Long getContinuousDays();
    }
}
