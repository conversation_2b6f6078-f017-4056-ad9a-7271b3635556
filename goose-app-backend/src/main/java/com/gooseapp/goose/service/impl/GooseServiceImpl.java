package com.gooseapp.goose.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gooseapp.common.exception.BusinessException;
import com.gooseapp.common.constant.ErrorCode;
import com.gooseapp.goose.dto.GooseDTO;
import com.gooseapp.goose.dto.GooseDetailDTO;
import com.gooseapp.goose.dto.InteractionDTO;
import com.gooseapp.goose.entity.Goose;
import com.gooseapp.goose.entity.Interaction;
import com.gooseapp.goose.mapper.GooseMapper;
import com.gooseapp.goose.mapper.InteractionMapper;
import com.gooseapp.goose.service.GooseService;
import com.gooseapp.goose.service.InteractionService;
import com.gooseapp.user.mapper.UserMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.PageImpl;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.Optional;

/**
 * 小鹅服务实现类
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-12-19
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class GooseServiceImpl extends ServiceImpl<GooseMapper, Goose> implements GooseService {

    private final GooseMapper gooseMapper;
    private final InteractionMapper interactionMapper;
    private final UserMapper userMapper;
    private final InteractionService interactionService;

    @Override
    public List<GooseDTO> getUserGeese(Long userId) {
        log.info("获取用户小鹅列表: userId={}", userId);

        // 验证用户存在
        validateUserExists(userId);

        // 只返回用户已归属的小鹅（ownershipStatus = 1）
        List<Goose> geese = gooseMapper.findOwnedGeeseByUserId(userId);

        return geese.stream()
                .map(this::convertToGooseDTO)
                .collect(Collectors.toList());
    }

    @Override
    public GooseDetailDTO getGooseDetail(Long userId, Long gooseId) {
        log.info("获取小鹅详细信息: userId={}, gooseId={}", userId, gooseId);

        Goose goose = validateGooseOwnership(userId, gooseId);

        // 构建详细信息
        GooseDetailDTO detailDTO = convertToGooseDetailDTO(goose);

        // 获取冷却状态
        Map<String, InteractionService.CooldownStatus> cooldownStatus =
                interactionService.getAllCooldownStatus(userId, gooseId);
        detailDTO.setCooldownStatus(convertToCooldownDTO(cooldownStatus));

        // 获取最近互动记录
        List<InteractionDTO> recentInteractions =
                interactionService.getInteractions(userId, gooseId, 0, 10);
        detailDTO.setRecentInteractions(recentInteractions);

        // 获取今日统计
        InteractionService.TodayInteractionStats todayStats =
                interactionService.getTodayStats(userId, gooseId);
        detailDTO.setTodayStats(convertToStatsDTO(todayStats));

        return detailDTO;
    }

    @Override
    @Transactional
    public GooseDTO createGoose(Long userId, String name, String breed, Integer gender) {
        log.info("创建新小鹅: userId={}, name={}, breed={}, gender={}",
                userId, name, breed, gender);

        // 验证用户存在
        validateUserExists(userId);

        // 验证参数
        validateGooseCreationParams(name, breed, gender);

        // 创建小鹅实体
        Goose goose = new Goose();
        goose.setUserId(userId);
        goose.setName(name);
        goose.setBreed(breed);
        goose.setGender(gender);
        goose.setStatus(Goose.Status.NORMAL.getCode());

        // 设置初始状态
        initializeGooseStatus(goose);

        // 保存小鹅
        gooseMapper.updateById(goose);

        // 更新用户统计
        updateUserGooseCount(userId);

        log.info("小鹅创建成功: gooseId={}, name={}", goose.getId(), goose.getName());
        return convertToGooseDTO(goose);
    }

    @Override
    @Transactional
    public GooseDTO updateGooseName(Long userId, Long gooseId, String newName) {
        log.info("更新小鹅名称: userId={}, gooseId={}, newName={}", userId, gooseId, newName);

        Goose goose = validateGooseOwnership(userId, gooseId);

        // 验证名称
        if (newName == null || newName.trim().isEmpty()) {
            throw new BusinessException(ErrorCode.VALIDATION_ERROR, "小鹅名称不能为空");
        }
        if (newName.length() > 50) {
            throw new BusinessException(ErrorCode.VALIDATION_ERROR, "小鹅名称不能超过50个字符");
        }

        goose.setName(newName.trim());
        updateById(goose);

        log.info("小鹅名称更新成功: gooseId={}, newName={}", gooseId, newName);
        return convertToGooseDTO(goose);
    }

    @Override
    @Transactional
    public void deleteGoose(Long userId, Long gooseId) {
        log.info("删除小鹅: userId={}, gooseId={}", userId, gooseId);

        Goose goose = validateGooseOwnership(userId, gooseId);

        // 软删除：标记为已转赠状态
        goose.setStatus(Goose.Status.GIFTED.getCode());
        gooseMapper.updateById(goose);

        // 更新用户统计
        updateUserGooseCount(userId);

        log.info("小鹅删除成功: gooseId={}", gooseId);
    }

    @Override
    @Transactional
    public InteractionDTO feedGoose(Long userId, Long gooseId) {
        log.info("喂食小鹅: userId={}, gooseId={}", userId, gooseId);
        return performInteraction(userId, gooseId, Interaction.Type.FEED);
    }

    @Override
    @Transactional
    public InteractionDTO waterGoose(Long userId, Long gooseId) {
        log.info("给小鹅喂水: userId={}, gooseId={}", userId, gooseId);
        return performInteraction(userId, gooseId, Interaction.Type.WATER);
    }

    @Override
    @Transactional
    public InteractionDTO cleanGoose(Long userId, Long gooseId) {
        log.info("清洁小鹅: userId={}, gooseId={}", userId, gooseId);
        return performInteraction(userId, gooseId, Interaction.Type.CLEAN);
    }

    @Override
    @Transactional
    public InteractionDTO playWithGoose(Long userId, Long gooseId) {
        log.info("与小鹅玩耍: userId={}, gooseId={}", userId, gooseId);
        return performInteraction(userId, gooseId, Interaction.Type.PLAY);
    }

    @Override
    public List<InteractionDTO> getGooseInteractions(Long userId, Long gooseId, int page, int size) {
        log.info("获取小鹅互动记录: userId={}, gooseId={}, page={}, size={}",
                userId, gooseId, page, size);

        validateGooseOwnership(userId, gooseId);
        return interactionService.getInteractions(userId, gooseId, page, size);
    }

    @Override
    public List<InteractionDTO> getUserInteractions(Long userId, int page, int size) {
        log.info("获取用户互动记录: userId={}, page={}, size={}", userId, page, size);

        validateUserExists(userId);
        return interactionService.getInteractions(userId, null, page, size);
    }

    @Override
    public List<GooseDTO> getGeeseNeedingCare(Long userId) {
        log.info("获取需要照顾的小鹅: userId={}", userId);

        validateUserExists(userId);

        List<Goose> geese = gooseMapper.findGeeseNeedingCare(userId);
        return geese.stream()
                .map(this::convertToGooseDTO)
                .collect(Collectors.toList());
    }

    @Override
    public GooseStatsDTO getUserGooseStats(Long userId) {
        log.info("获取用户小鹅统计: userId={}", userId);

        validateUserExists(userId);

        return new GooseStatsDTO() {
            @Override
            public Long getTotalGeese() {
                return gooseMapper.countByUserIdAndOwnershipStatus(userId, 1);
            }

            @Override
            public Long getHealthyGeese() {
                return gooseMapper.findOwnedGeeseByUserId(userId)
                        .stream()
                        .filter(g -> g.getHealth() >= 80)
                        .count();
            }

            @Override
            public Long getSickGeese() {
                return gooseMapper.findOwnedGeeseByUserId(userId)
                        .stream()
                        .filter(g -> g.getStatus().equals(Goose.Status.SICK.getCode()))
                        .count();
            }

            @Override
            public Long getGeeseNeedingCare() {
                return (long) gooseMapper.findGeeseNeedingCare(userId).size();
            }

            @Override
            public Long getTotalInteractions() {
                return interactionMapper.countByUserId(userId);
            }

            @Override
            public Long getTodayInteractions() {
                return interactionMapper.countTodayInteractions(userId);
            }

            @Override
            public Double getAverageLevel() {
                List<Goose> geese = gooseMapper.findOwnedGeeseByUserId(userId);
                return geese.stream()
                        .mapToInt(Goose::getLevel)
                        .average()
                        .orElse(0.0);
            }

            @Override
            public Double getAverageHealth() {
                List<Goose> geese = gooseMapper.findOwnedGeeseByUserId(userId);
                return geese.stream()
                        .mapToInt(Goose::getHealth)
                        .average()
                        .orElse(0.0);
            }

            @Override
            public Long getTotalExperience() {
                return interactionMapper.getTotalExperienceGained(userId);
            }

            @Override
            public Long getContinuousDays() {
                return interactionService.calculateContinuousDays(userId);
            }
        };
    }

    @Override
    public boolean isInteractionInCooldown(Long userId, Long gooseId, String interactionType) {
        return interactionService.isInCooldown(userId, gooseId, interactionType);
    }

    @Override
    public long getInteractionCooldownMinutes(Long userId, Long gooseId, String interactionType) {
        return interactionService.getCooldownRemainingMinutes(userId, gooseId, interactionType);
    }

    @Override
    @Transactional
    public void autoUpdateGooseStatus(Long gooseId) {
        log.debug("自动更新小鹅状态: gooseId={}", gooseId);

        Goose goose = gooseMapper.selectById(gooseId);
        if (goose == null) {
            throw new BusinessException(ErrorCode.GOOSE_NOT_FOUND);
        }

        if (!goose.isNormal()) {
            return; // 只更新正常状态的小鹅
        }

        LocalDateTime now = LocalDateTime.now();
        boolean statusChanged = false;

        // 自然状态衰减
        if (goose.getLastFeedTime() != null) {
            long hoursSinceLastFeed = java.time.Duration.between(goose.getLastFeedTime(), now).toHours();
            if (hoursSinceLastFeed > 0) {
                int hungerIncrease = (int) Math.min(hoursSinceLastFeed * 2, 20);
                goose.setHunger(Math.min(100, goose.getHunger() + hungerIncrease));
                statusChanged = true;
            }
        }

        if (goose.getLastWaterTime() != null) {
            long hoursSinceLastWater = java.time.Duration.between(goose.getLastWaterTime(), now).toHours();
            if (hoursSinceLastWater > 0) {
                int thirstIncrease = (int) Math.min(hoursSinceLastWater * 3, 25);
                goose.setThirst(Math.min(100, goose.getThirst() + thirstIncrease));
                statusChanged = true;
            }
        }

        if (goose.getLastCleanTime() != null) {
            long hoursSinceLastClean = java.time.Duration.between(goose.getLastCleanTime(), now).toHours();
            if (hoursSinceLastClean > 0) {
                int cleanlinessDecrease = (int) Math.min(hoursSinceLastClean, 15);
                goose.setCleanliness(Math.max(0, goose.getCleanliness() - cleanlinessDecrease));
                statusChanged = true;
            }
        }

        // 健康度和快乐度计算
        updateHealthAndHappiness(goose);

        // 检查是否需要升级
        checkAndLevelUpGoose(gooseId);

        if (statusChanged) {
            gooseMapper.updateById(goose);
        }
    }

    @Override
    @Transactional
    public void autoUpdateAllGeeseStatus() {
        log.info("批量自动更新所有小鹅状态");

        List<Goose> allGeese = gooseMapper.findByStatus(Goose.Status.NORMAL.getCode());

        for (Goose goose : allGeese) {
            try {
                autoUpdateGooseStatus(goose.getId());
            } catch (Exception e) {
                log.error("更新小鹅状态失败: gooseId={}, error={}", goose.getId(), e.getMessage());
            }
        }

        log.info("批量更新完成，共处理{}只小鹅", allGeese.size());
    }

    @Override
    @Transactional
    public boolean checkAndLevelUpGoose(Long gooseId) {
        Goose goose = gooseMapper.selectById(gooseId);
        if (goose == null) {
            throw new BusinessException(ErrorCode.GOOSE_NOT_FOUND);
        }

        int requiredExp = goose.getLevel() * 100;
        if (goose.getExperience() >= requiredExp) {
            goose.setLevel(goose.getLevel() + 1);
            goose.setExperience(goose.getExperience() - requiredExp);

            // 升级奖励：恢复部分状态
            goose.setHealth(Math.min(100, goose.getHealth() + 10));
            goose.setHappiness(Math.min(100, goose.getHappiness() + 15));

            gooseMapper.updateById(goose);

            log.info("小鹅升级成功: gooseId={}, newLevel={}", gooseId, goose.getLevel());
            return true;
        }

        return false;
    }

    @Override
    public List<GooseDTO> searchUserGeese(Long userId, String keyword) {
        log.info("搜索用户小鹅: userId={}, keyword={}", userId, keyword);

        validateUserExists(userId);

        if (keyword == null || keyword.trim().isEmpty()) {
            return getUserGeese(userId);
        }

        // 只搜索用户已归属的小鹅
        List<Goose> geese = gooseMapper.findOwnedGeeseByUserIdAndNameContaining(userId, keyword.trim());

        return geese.stream()
                .map(this::convertToGooseDTO)
                .collect(Collectors.toList());
    }

    @Override
    public List<GooseDTO> getGooseRanking(String type, int limit) {
        log.info("获取小鹅排行榜: type={}, limit={}", type, limit);

        List<Goose> geese;

        switch (type.toLowerCase()) {
            case "level":
                geese = gooseMapper.findTop10ByStatusOrderByLevelDescExperienceDesc(
                        Goose.Status.NORMAL.getCode());
                break;
            case "health":
                geese = gooseMapper.findByUserIdAndStatusOrderByCreatedAtDesc(null,
                        Goose.Status.NORMAL.getCode())
                        .stream()
                        .sorted((g1, g2) -> Integer.compare(g2.getHealth(), g1.getHealth()))
                        .limit(limit)
                        .collect(Collectors.toList());
                break;
            case "happiness":
                geese = gooseMapper.findByUserIdAndStatusOrderByCreatedAtDesc(null,
                        Goose.Status.NORMAL.getCode())
                        .stream()
                        .sorted((g1, g2) -> Integer.compare(g2.getHappiness(), g1.getHappiness()))
                        .limit(limit)
                        .collect(Collectors.toList());
                break;
            default:
                geese = gooseMapper.findTop10ByStatusOrderByCreatedAtDesc(
                        Goose.Status.NORMAL.getCode());
        }

        return geese.stream()
                .map(this::convertToGooseDTO)
                .collect(Collectors.toList());
    }

    // ==================== 私有辅助方法 ====================

    /**
     * 执行互动操作的通用方法
     */
    private InteractionDTO performInteraction(Long userId, Long gooseId, Interaction.Type interactionType) {
        Goose goose = validateGooseOwnership(userId, gooseId);

        // 检查小鹅状态
        if (!goose.isNormal()) {
            throw new BusinessException(ErrorCode.VALIDATION_ERROR, "小鹅当前状态不允许互动");
        }

        // 执行互动
        Interaction interaction = interactionService.performInteraction(userId, goose, interactionType);

        // 更新用户统计
        updateUserInteractionStats(userId);

        return convertToInteractionDTO(interaction);
    }

    /**
     * 验证用户存在
     */
    private void validateUserExists(Long userId) {
        if (userMapper.selectById(userId) == null) {
            throw new BusinessException(ErrorCode.USER_NOT_FOUND);
        }
    }

    /**
     * 验证小鹅所有权
     */
    private Goose validateGooseOwnership(Long userId, Long gooseId) {
        Goose goose = gooseMapper.findByIdAndUserId(gooseId, userId)
                .orElseThrow(() -> new BusinessException(ErrorCode.VALIDATION_ERROR, "小鹅不存在或不属于当前用户"));
        return goose;
    }

    /**
     * 验证小鹅创建参数
     */
    private void validateGooseCreationParams(String name, String breed, Integer gender) {
        if (name == null || name.trim().isEmpty()) {
            throw new BusinessException(ErrorCode.VALIDATION_ERROR, "小鹅名称不能为空");
        }
        if (name.length() > 50) {
            throw new BusinessException(ErrorCode.VALIDATION_ERROR, "小鹅名称不能超过50个字符");
        }
        if (breed == null || breed.trim().isEmpty()) {
            throw new BusinessException(ErrorCode.VALIDATION_ERROR, "小鹅品种不能为空");
        }
        if (gender == null || (gender != 1 && gender != 2)) {
            throw new BusinessException(ErrorCode.VALIDATION_ERROR, "性别参数错误");
        }
    }

    /**
     * 初始化小鹅状态
     */
    private void initializeGooseStatus(Goose goose) {
        // 根据品种设置不同的初始属性
        switch (goose.getBreed()) {
            case "白鹅":
                goose.setHealth(95);
                goose.setHappiness(85);
                break;
            case "灰鹅":
                goose.setHealth(90);
                goose.setHappiness(90);
                break;
            case "花鹅":
                goose.setHealth(100);
                goose.setHappiness(95);
                break;
            case "黑鹅":
                goose.setHealth(85);
                goose.setHappiness(80);
                break;
            default:
                goose.setHealth(90);
                goose.setHappiness(85);
        }

        // 设置默认图片
        goose.setImageUrl(getDefaultImageUrl(goose.getBreed(), goose.getGender()));
    }

    /**
     * 获取默认图片URL
     */
    private String getDefaultImageUrl(String breed, Integer gender) {
        String genderSuffix = gender == 1 ? "_male" : "_female";
        return String.format("/images/geese/%s%s.jpg", breed.toLowerCase(), genderSuffix);
    }

    /**
     * 更新用户小鹅数量统计
     */
    private void updateUserGooseCount(Long userId) {
        long count = gooseMapper.countByUserIdAndStatus(userId, Goose.Status.NORMAL.getCode());
        // TODO: 这里可以更新用户表的统计字段，暂时省略具体实现
        log.debug("用户{}当前拥有{}只小鹅", userId, count);
    }

    /**
     * 更新用户互动统计
     */
    private void updateUserInteractionStats(Long userId) {
        // 这里可以更新用户表的互动统计字段，暂时省略具体实现
    }

    /**
     * 更新小鹅健康度和快乐度
     */
    private void updateHealthAndHappiness(Goose goose) {
        // 根据各项指标计算健康度
        int healthScore = 100;
        if (goose.getHunger() > 80) healthScore -= 20;
        if (goose.getThirst() > 80) healthScore -= 15;
        if (goose.getCleanliness() < 30) healthScore -= 25;

        goose.setHealth(Math.max(0, Math.min(100, healthScore)));

        // 根据健康度和清洁度计算快乐度
        int happinessScore = (goose.getHealth() + goose.getCleanliness()) / 2;
        if (goose.getHunger() < 30 && goose.getThirst() < 30) {
            happinessScore += 10; // 吃饱喝足加成
        }

        goose.setHappiness(Math.max(0, Math.min(100, happinessScore)));

        // 检查是否生病
        if (goose.getHealth() < 30) {
            goose.setStatus(Goose.Status.SICK.getCode());
        } else if (goose.getHealth() > 70 && goose.getStatus().equals(Goose.Status.SICK.getCode())) {
            goose.setStatus(Goose.Status.NORMAL.getCode());
        }
    }

    /**
     * 转换为GooseDTO
     */
    private GooseDTO convertToGooseDTO(Goose goose) {
        GooseDTO dto = new GooseDTO();
        dto.setId(goose.getId());
        dto.setName(goose.getName());
        dto.setBreed(goose.getBreed());
        dto.setGender(goose.getGender());
        dto.setAge(goose.getAge());
        dto.setLevel(goose.getLevel());
        dto.setExperience(goose.getExperience());
        dto.setExpToNextLevel(goose.getExpToNextLevel());
        dto.setImageUrl(goose.getImageUrl());
        dto.setStatus(goose.getStatus());
        dto.setStatusDescription(Goose.Status.fromCode(goose.getStatus()).getDescription());
        dto.setOverallScore(goose.getOverallScore());
        dto.setNeedsCare(goose.needsCare());
        dto.setCreatedAt(goose.getCreatedAt());
        dto.setUpdatedAt(goose.getUpdatedAt());
        return dto;
    }

    /**
     * 转换为GooseDetailDTO
     */
    private GooseDetailDTO convertToGooseDetailDTO(Goose goose) {
        GooseDetailDTO dto = new GooseDetailDTO();
        dto.setId(goose.getId());
        dto.setUserId(goose.getUserId());
        dto.setName(goose.getName());
        dto.setBreed(goose.getBreed());
        dto.setBreedDescription(Goose.Breed.fromName(goose.getBreed()).getDescription());
        dto.setGender(goose.getGender());
        dto.setGenderDescription(Goose.Gender.fromCode(goose.getGender()).getDescription());
        dto.setAge(goose.getAge());
        dto.setHealth(goose.getHealth());
        dto.setHunger(goose.getHunger());
        dto.setThirst(goose.getThirst());
        dto.setCleanliness(goose.getCleanliness());
        dto.setHappiness(goose.getHappiness());
        dto.setLevel(goose.getLevel());
        dto.setExperience(goose.getExperience());
        dto.setExpToNextLevel(goose.getExpToNextLevel());
        dto.setImageUrl(goose.getImageUrl());
        dto.setVideoUrl(goose.getVideoUrl());
        dto.setLastFeedTime(goose.getLastFeedTime());
        dto.setLastWaterTime(goose.getLastWaterTime());
        dto.setLastCleanTime(goose.getLastCleanTime());
        dto.setStatus(goose.getStatus());
        dto.setStatusDescription(Goose.Status.fromCode(goose.getStatus()).getDescription());
        dto.setOverallScore(goose.getOverallScore());
        dto.setNeedsCare(goose.needsCare());
        dto.setCreatedAt(goose.getCreatedAt());
        dto.setUpdatedAt(goose.getUpdatedAt());
        return dto;
    }

    /**
     * 转换为InteractionDTO
     */
    private InteractionDTO convertToInteractionDTO(Interaction interaction) {
        InteractionDTO dto = new InteractionDTO();
        dto.setId(interaction.getId());
        dto.setUserId(interaction.getUserId());
        dto.setGooseId(interaction.getGooseId());
        dto.setInteractionType(interaction.getInteractionType());
        dto.setInteractionTypeDescription(interaction.getType().getDescription());
        dto.setValueChange(interaction.getValueChange());
        dto.setExperienceGained(interaction.getExperienceGained());
        dto.setCooldownMinutes(interaction.getCooldownMinutes());
        dto.setInCooldown(interaction.isInCooldown());
        dto.setRemainingCooldownMinutes(interaction.getRemainingCooldownMinutes());
        dto.setCooldownEndTime(interaction.getCooldownEndTime());
        dto.setCreatedAt(interaction.getCreatedAt());
        return dto;
    }

    /**
     * 转换冷却状态DTO
     */
    private GooseDetailDTO.InteractionCooldownDTO convertToCooldownDTO(
            Map<String, InteractionService.CooldownStatus> cooldownStatus) {
        GooseDetailDTO.InteractionCooldownDTO dto = new GooseDetailDTO.InteractionCooldownDTO();

        InteractionService.CooldownStatus feedStatus = cooldownStatus.get("feed");
        if (feedStatus != null) {
            dto.setFeedCooldownMinutes(feedStatus.getRemainingMinutes());
            dto.setCanFeed(feedStatus.canInteract());
        }

        InteractionService.CooldownStatus waterStatus = cooldownStatus.get("water");
        if (waterStatus != null) {
            dto.setWaterCooldownMinutes(waterStatus.getRemainingMinutes());
            dto.setCanWater(waterStatus.canInteract());
        }

        InteractionService.CooldownStatus cleanStatus = cooldownStatus.get("clean");
        if (cleanStatus != null) {
            dto.setCleanCooldownMinutes(cleanStatus.getRemainingMinutes());
            dto.setCanClean(cleanStatus.canInteract());
        }

        InteractionService.CooldownStatus playStatus = cooldownStatus.get("play");
        if (playStatus != null) {
            dto.setPlayCooldownMinutes(playStatus.getRemainingMinutes());
            dto.setCanPlay(playStatus.canInteract());
        }

        return dto;
    }

    /**
     * 转换统计DTO
     */
    private GooseDetailDTO.InteractionStatsDTO convertToStatsDTO(
            InteractionService.TodayInteractionStats stats) {
        GooseDetailDTO.InteractionStatsDTO dto = new GooseDetailDTO.InteractionStatsDTO();
        dto.setTotalInteractions(stats.getTotalInteractions());
        dto.setFeedCount(stats.getFeedCount());
        dto.setWaterCount(stats.getWaterCount());
        dto.setCleanCount(stats.getCleanCount());
        dto.setPlayCount(stats.getPlayCount());
        dto.setExperienceGained(stats.getExperienceGained());
        return dto;
    }

    // ==================== 商城相关方法实现 ====================

    @Override
    public org.springframework.data.domain.Page<GooseDTO> getAvailableGeese(Pageable pageable) {
        log.debug("获取待售小鹅列表: page={}, size={}", pageable.getPageNumber(), pageable.getPageSize());

        // 将Spring Data Pageable转换为MyBatis Plus Page
        Page<Goose> mybatisPage = new Page<>(pageable.getPageNumber() + 1, pageable.getPageSize());
        QueryWrapper<Goose> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("ownership_status", 0);
        IPage<Goose> geesePage = gooseMapper.selectPage(mybatisPage, queryWrapper);
        
        // 转换为DTO列表
        List<GooseDTO> dtoList = geesePage.getRecords().stream()
                .map(this::convertToGooseDTO)
                .collect(Collectors.toList());
        
        // 返回Spring Data Page
        return new PageImpl<>(dtoList, pageable, geesePage.getTotal());
    }

    @Override
    public List<GooseDTO> getAvailableGeeseByBreed(String breed) {
        log.debug("根据品种获取待售小鹅: breed={}", breed);

        QueryWrapper<Goose> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("breed", breed)
                   .eq("ownership_status", 0);
        List<Goose> geese = gooseMapper.selectList(queryWrapper);
        return geese.stream()
                .map(this::convertToGooseDTO)
                .collect(Collectors.toList());
    }

    @Override
    public GooseDTO getGooseById(Long gooseId) {
        log.debug("根据ID获取小鹅信息: gooseId={}", gooseId);

        Goose goose = Optional.ofNullable(gooseMapper.selectById(gooseId))
                .orElseThrow(() -> new BusinessException(ErrorCode.GOOSE_NOT_FOUND, "小鹅不存在"));

        return convertToGooseDTO(goose);
    }

    @Override
    public List<String> getAllBreeds() {
        log.debug("获取所有小鹅品种列表");

        QueryWrapper<Goose> queryWrapper = new QueryWrapper<>();
        queryWrapper.select("DISTINCT breed");
        List<Goose> geese = gooseMapper.selectList(queryWrapper);
        return geese.stream()
                .map(Goose::getBreed)
                .distinct()
                .collect(Collectors.toList());
    }

    @Override
    public org.springframework.data.domain.Page<GooseDTO> searchAvailableGeese(String keyword, Pageable pageable) {
        log.debug("搜索可购买的小鹅: keyword={}, page={}, size={}",
                keyword, pageable.getPageNumber(), pageable.getPageSize());

        if (keyword == null || keyword.trim().isEmpty()) {
            return getAvailableGeese(pageable);
        }

        // 将Spring Data Pageable转换为MyBatis Plus Page
        Page<Goose> mybatisPage = new Page<>(pageable.getPageNumber() + 1, pageable.getPageSize());
        QueryWrapper<Goose> queryWrapper = new QueryWrapper<>();
        queryWrapper.like("name", keyword.trim())
                   .eq("ownership_status", 0);
        IPage<Goose> geesePage = gooseMapper.selectPage(mybatisPage, queryWrapper);
        
        // 转换为DTO列表
        List<GooseDTO> dtoList = geesePage.getRecords().stream()
                .map(this::convertToGooseDTO)
                .collect(Collectors.toList());
        
        // 返回Spring Data Page
        return new PageImpl<>(dtoList, pageable, geesePage.getTotal());
    }
}
