package com.gooseapp.goose.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gooseapp.common.constant.ErrorCode;
import com.gooseapp.common.exception.BusinessException;
import com.gooseapp.goose.dto.InteractionDTO;
import com.gooseapp.goose.entity.Goose;
import com.gooseapp.goose.entity.Interaction;
import com.gooseapp.goose.mapper.GooseMapper;
import com.gooseapp.goose.mapper.InteractionMapper;
import com.gooseapp.goose.service.InteractionService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 互动服务实现类
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-12-19
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class InteractionServiceImpl extends ServiceImpl<InteractionMapper, Interaction> implements InteractionService {

    private final GooseMapper gooseMapper;

    @Override
    @Transactional
    public Interaction performInteraction(Long userId, Goose goose, Interaction.Type interactionType) {
        log.info("执行互动操作: userId={}, gooseId={}, type={}",
                userId, goose.getId(), interactionType.getCode());

        // 检查冷却时间
        if (isInCooldown(userId, goose.getId(), interactionType.getCode())) {
            long remainingMinutes = getCooldownRemainingMinutes(userId, goose.getId(), interactionType.getCode());
            throw new BusinessException(ErrorCode.INTERACTION_COOLDOWN,
                    String.format("互动冷却中，还需等待%d分钟", remainingMinutes));
        }

        // 创建互动记录
        Interaction interaction = new Interaction(userId, goose.getId(), interactionType);
        save(interaction);
        Interaction savedInteraction = interaction;

        // 更新小鹅状态
        updateGooseStatus(goose, interactionType);

        // 更新小鹅最后互动时间
        updateLastInteractionTime(goose, interactionType);

        // 保存小鹅状态
        gooseMapper.updateById(goose);

        log.info("互动操作完成: interactionId={}, experience={}",
                savedInteraction.getId(), savedInteraction.getExperienceGained());

        return savedInteraction;
    }

    @Override
    public boolean isInCooldown(Long userId, Long gooseId, String interactionType) {
        LocalDateTime cooldownThreshold = LocalDateTime.now().minus(
                java.time.Duration.ofMinutes(Interaction.Type.fromCode(interactionType).getCooldownMinutes()));

        return getBaseMapper().isInCooldown(userId, gooseId, interactionType, cooldownThreshold);
    }

    @Override
    public long getCooldownRemainingMinutes(Long userId, Long gooseId, String interactionType) {
        Interaction.Type type = Interaction.Type.fromCode(interactionType);

        Interaction interaction = getBaseMapper().findTopByUserIdAndGooseIdAndInteractionTypeOrderByCreatedAtDesc(
                userId, gooseId, interactionType);

        if (interaction != null) {
            LocalDateTime cooldownEnd = interaction.getCreatedAt().plusMinutes(type.getCooldownMinutes());
            LocalDateTime now = LocalDateTime.now();
            if (now.isBefore(cooldownEnd)) {
                return java.time.Duration.between(now, cooldownEnd).toMinutes();
            }
        }
        return 0L;
    }

    @Override
    public Map<String, CooldownStatus> getAllCooldownStatus(Long userId, Long gooseId) {
        Map<String, CooldownStatus> statusMap = new HashMap<>();

        for (Interaction.Type type : Interaction.Type.values()) {
            String typeCode = type.getCode();
            boolean inCooldown = isInCooldown(userId, gooseId, typeCode);
            long remainingMinutes = inCooldown ? getCooldownRemainingMinutes(userId, gooseId, typeCode) : 0;

            statusMap.put(typeCode, new CooldownStatus() {
                @Override
                public boolean isInCooldown() {
                    return inCooldown;
                }

                @Override
                public long getRemainingMinutes() {
                    return remainingMinutes;
                }

                @Override
                public LocalDateTime getEndTime() {
                    if (!inCooldown) return null;
                    return LocalDateTime.now().plusMinutes(remainingMinutes);
                }

                @Override
                public boolean canInteract() {
                    return !inCooldown;
                }
            });
        }

        return statusMap;
    }

    @Override
    public List<InteractionDTO> getInteractions(Long userId, Long gooseId, int page, int size) {
        List<Interaction> interactions;
        if (gooseId != null) {
            interactions = getBaseMapper().findByUserIdAndGooseIdOrderByCreatedAtDesc(userId, gooseId)
                    .stream()
                    .skip((long) page * size)
                    .limit(size)
                    .collect(Collectors.toList());
        } else {
            interactions = getBaseMapper().findByUserIdOrderByCreatedAtDesc(userId)
                    .stream()
                    .skip((long) page * size)
                    .limit(size)
                    .collect(Collectors.toList());
        }

        return interactions.stream()
                .map(this::convertToInteractionDTO)
                .collect(Collectors.toList());
    }

    @Override
    public TodayInteractionStats getTodayStats(Long userId, Long gooseId) {
        List<Interaction> todayInteractions;
        if (gooseId != null) {
            todayInteractions = getBaseMapper().findGooseTodayInteractions(gooseId);
        } else {
            todayInteractions = getBaseMapper().findTodayInteractions(userId);
        }

        return new TodayInteractionStats() {
            @Override
            public long getTotalInteractions() {
                return todayInteractions.size();
            }

            @Override
            public long getFeedCount() {
                return todayInteractions.stream()
                        .filter(i -> "feed".equals(i.getInteractionType()))
                        .count();
            }

            @Override
            public long getWaterCount() {
                return todayInteractions.stream()
                        .filter(i -> "water".equals(i.getInteractionType()))
                        .count();
            }

            @Override
            public long getCleanCount() {
                return todayInteractions.stream()
                        .filter(i -> "clean".equals(i.getInteractionType()))
                        .count();
            }

            @Override
            public long getPlayCount() {
                return todayInteractions.stream()
                        .filter(i -> "play".equals(i.getInteractionType()))
                        .count();
            }

            @Override
            public long getExperienceGained() {
                return todayInteractions.stream()
                        .mapToLong(Interaction::getExperienceGained)
                        .sum();
            }
        };
    }

    @Override
    public Map<String, Long> getInteractionTypeStats(Long userId) {
        List<Object[]> stats = getBaseMapper().countInteractionsByType(userId);
        Map<String, Long> result = new HashMap<>();

        for (Object[] stat : stats) {
            String type = (String) stat[0];
            Long count = (Long) stat[1];
            result.put(type, count);
        }

        return result;
    }

    @Override
    public List<DailyInteractionStats> getWeeklyStats(Long userId) {
        LocalDateTime startDate = LocalDateTime.now().minusDays(7);
        List<Object[]> stats = getBaseMapper().getWeeklyInteractionStats(userId, startDate);

        return stats.stream()
                .map(stat -> new DailyInteractionStats() {
                    @Override
                    public LocalDateTime getDate() {
                        return (LocalDateTime) stat[0];
                    }

                    @Override
                    public long getInteractionCount() {
                        return (Long) stat[1];
                    }

                    @Override
                    public long getExperienceGained() {
                        // 这里可以添加经验统计逻辑
                        return getInteractionCount() * 5; // 简化计算
                    }
                })
                .collect(Collectors.toList());
    }

    @Override
    public UserInteractionStats getUserTotalStats(Long userId) {
        return new UserInteractionStats() {
            @Override
            public long getTotalInteractions() {
                return getBaseMapper().countByUserId(userId);
            }

            @Override
            public long getTotalExperienceGained() {
                return getBaseMapper().getTotalExperienceGained(userId);
            }

            @Override
            public long getContinuousDays() {
                return calculateContinuousDays(userId);
            }

            @Override
            public LocalDateTime getFirstInteractionDate() {
                return getBaseMapper().findByUserIdOrderByCreatedAtDesc(userId)
                        .stream()
                        .map(Interaction::getCreatedAt)
                        .min(LocalDateTime::compareTo)
                        .orElse(null);
            }

            @Override
            public LocalDateTime getLastInteractionDate() {
                return getBaseMapper().findByUserIdOrderByCreatedAtDesc(userId)
                        .stream()
                        .findFirst()
                        .map(Interaction::getCreatedAt)
                        .orElse(null);
            }

            @Override
            public Map<String, Long> getTypeStats() {
                return getInteractionTypeStats(userId);
            }
        };
    }

    @Override
    public long calculateContinuousDays(Long userId) {
        LocalDateTime startDate = LocalDateTime.now().minusDays(365); // 最多计算一年
        return getBaseMapper().countContinuousDays(userId, startDate);
    }

    @Override
    public List<GooseActivityStats> getMostActiveGeese(Long userId, int limit) {
        List<Object[]> stats = getBaseMapper().findMostActiveGeese(userId);

        return stats.stream()
                .limit(limit)
                .map(stat -> new GooseActivityStats() {
                    @Override
                    public Long getGooseId() {
                        return (Long) stat[0];
                    }

                    @Override
                    public String getGooseName() {
                        // 这里可以查询小鹅名称
                        Goose goose = gooseMapper.selectById(getGooseId());
                        return goose != null ? goose.getName() : "未知";
                    }

                    @Override
                    public long getInteractionCount() {
                        return (Long) stat[1];
                    }

                    @Override
                    public LocalDateTime getLastInteractionTime() {
                        return getBaseMapper().findByGooseIdOrderByCreatedAtDesc(getGooseId())
                                .stream()
                                .findFirst()
                                .map(Interaction::getCreatedAt)
                                .orElse(null);
                    }
                })
                .collect(Collectors.toList());
    }

    // ==================== 私有辅助方法 ====================

    /**
     * 更新小鹅状态
     */
    private void updateGooseStatus(Goose goose, Interaction.Type interactionType) {
        switch (interactionType) {
            case FEED:
                // 降低饥饿度，增加健康度和快乐度
                goose.setHunger(Math.max(0, goose.getHunger() - interactionType.getValueChange()));
                goose.setHealth(Math.min(100, goose.getHealth() + 5));
                goose.setHappiness(Math.min(100, goose.getHappiness() + 3));
                break;

            case WATER:
                // 降低口渴度，增加健康度
                goose.setThirst(Math.max(0, goose.getThirst() - interactionType.getValueChange()));
                goose.setHealth(Math.min(100, goose.getHealth() + 3));
                goose.setHappiness(Math.min(100, goose.getHappiness() + 2));
                break;

            case CLEAN:
                // 增加清洁度，增加健康度和快乐度
                goose.setCleanliness(Math.min(100, goose.getCleanliness() + interactionType.getValueChange()));
                goose.setHealth(Math.min(100, goose.getHealth() + 8));
                goose.setHappiness(Math.min(100, goose.getHappiness() + 5));
                break;

            case PLAY:
                // 主要增加快乐度，轻微增加健康度
                goose.setHappiness(Math.min(100, goose.getHappiness() + interactionType.getValueChange()));
                goose.setHealth(Math.min(100, goose.getHealth() + 2));
                // 玩耍会轻微增加饥饿度和口渴度
                goose.setHunger(Math.min(100, goose.getHunger() + 3));
                goose.setThirst(Math.min(100, goose.getThirst() + 2));
                break;
        }

        // 增加经验值
        goose.setExperience(goose.getExperience() + interactionType.getExperienceGained());

        // 检查是否需要升级
        checkAndLevelUp(goose);
    }

    /**
     * 更新最后互动时间
     */
    private void updateLastInteractionTime(Goose goose, Interaction.Type interactionType) {
        LocalDateTime now = LocalDateTime.now();

        switch (interactionType) {
            case FEED:
                goose.setLastFeedTime(now);
                break;
            case WATER:
                goose.setLastWaterTime(now);
                break;
            case CLEAN:
                goose.setLastCleanTime(now);
                break;
            case PLAY:
                // 玩耍不更新特定时间，但可以记录在其他字段中
                break;
        }
    }

    /**
     * 检查并升级小鹅
     */
    private void checkAndLevelUp(Goose goose) {
        int requiredExp = goose.getLevel() * 100;
        if (goose.getExperience() >= requiredExp) {
            goose.setLevel(goose.getLevel() + 1);
            goose.setExperience(goose.getExperience() - requiredExp);

            // 升级奖励
            goose.setHealth(Math.min(100, goose.getHealth() + 10));
            goose.setHappiness(Math.min(100, goose.getHappiness() + 15));

            log.info("小鹅升级: gooseId={}, newLevel={}", goose.getId(), goose.getLevel());
        }
    }

    /**
     * 转换为InteractionDTO
     */
    private InteractionDTO convertToInteractionDTO(Interaction interaction) {
        InteractionDTO dto = new InteractionDTO();
        dto.setId(interaction.getId());
        dto.setUserId(interaction.getUserId());
        dto.setGooseId(interaction.getGooseId());
        dto.setInteractionType(interaction.getInteractionType());
        dto.setInteractionTypeDescription(interaction.getType().getDescription());
        dto.setValueChange(interaction.getValueChange());
        dto.setExperienceGained(interaction.getExperienceGained());
        dto.setCooldownMinutes(interaction.getCooldownMinutes());
        dto.setInCooldown(interaction.isInCooldown());
        dto.setRemainingCooldownMinutes(interaction.getRemainingCooldownMinutes());
        dto.setCooldownEndTime(interaction.getCooldownEndTime());
        dto.setCreatedAt(interaction.getCreatedAt());

        // 设置小鹅名称
        Goose goose = gooseMapper.selectById(interaction.getGooseId());
        if (goose != null) {
            dto.setGooseName(goose.getName());
        }

        return dto;
    }
}
