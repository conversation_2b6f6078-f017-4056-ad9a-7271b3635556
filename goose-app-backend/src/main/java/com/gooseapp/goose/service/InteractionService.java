package com.gooseapp.goose.service;

import com.gooseapp.goose.dto.InteractionDTO;
import com.gooseapp.goose.entity.Goose;
import com.gooseapp.goose.entity.Interaction;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 互动服务接口
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-12-19
 */
public interface InteractionService {

    /**
     * 执行互动操作
     * 
     * @param userId 用户ID
     * @param goose 小鹅实体
     * @param interactionType 互动类型
     * @return 互动记录
     */
    Interaction performInteraction(Long userId, Goose goose, Interaction.Type interactionType);

    /**
     * 检查互动是否在冷却期
     * 
     * @param userId 用户ID
     * @param gooseId 小鹅ID
     * @param interactionType 互动类型
     * @return 是否在冷却期
     */
    boolean isInCooldown(Long userId, Long gooseId, String interactionType);

    /**
     * 获取互动冷却剩余时间
     * 
     * @param userId 用户ID
     * @param gooseId 小鹅ID
     * @param interactionType 互动类型
     * @return 剩余时间（分钟）
     */
    long getCooldownRemainingMinutes(Long userId, Long gooseId, String interactionType);

    /**
     * 获取所有互动类型的冷却状态
     * 
     * @param userId 用户ID
     * @param gooseId 小鹅ID
     * @return 冷却状态映射
     */
    Map<String, CooldownStatus> getAllCooldownStatus(Long userId, Long gooseId);

    /**
     * 获取互动记录列表
     * 
     * @param userId 用户ID
     * @param gooseId 小鹅ID（可选）
     * @param page 页码
     * @param size 每页大小
     * @return 互动记录列表
     */
    List<InteractionDTO> getInteractions(Long userId, Long gooseId, int page, int size);

    /**
     * 获取今日互动统计
     * 
     * @param userId 用户ID
     * @param gooseId 小鹅ID（可选）
     * @return 今日统计
     */
    TodayInteractionStats getTodayStats(Long userId, Long gooseId);

    /**
     * 获取互动类型统计
     * 
     * @param userId 用户ID
     * @return 各类型互动次数
     */
    Map<String, Long> getInteractionTypeStats(Long userId);

    /**
     * 获取最近7天互动统计
     * 
     * @param userId 用户ID
     * @return 每日互动统计
     */
    List<DailyInteractionStats> getWeeklyStats(Long userId);

    /**
     * 获取用户总互动统计
     * 
     * @param userId 用户ID
     * @return 总统计信息
     */
    UserInteractionStats getUserTotalStats(Long userId);

    /**
     * 计算连续互动天数
     * 
     * @param userId 用户ID
     * @return 连续天数
     */
    long calculateContinuousDays(Long userId);

    /**
     * 获取最活跃的小鹅
     * 
     * @param userId 用户ID
     * @param limit 返回数量
     * @return 小鹅ID和互动次数
     */
    List<GooseActivityStats> getMostActiveGeese(Long userId, int limit);

    /**
     * 冷却状态
     */
    interface CooldownStatus {
        boolean isInCooldown();
        long getRemainingMinutes();
        LocalDateTime getEndTime();
        boolean canInteract();
    }

    /**
     * 今日互动统计
     */
    interface TodayInteractionStats {
        long getTotalInteractions();
        long getFeedCount();
        long getWaterCount();
        long getCleanCount();
        long getPlayCount();
        long getExperienceGained();
    }

    /**
     * 每日互动统计
     */
    interface DailyInteractionStats {
        LocalDateTime getDate();
        long getInteractionCount();
        long getExperienceGained();
    }

    /**
     * 用户互动总统计
     */
    interface UserInteractionStats {
        long getTotalInteractions();
        long getTotalExperienceGained();
        long getContinuousDays();
        LocalDateTime getFirstInteractionDate();
        LocalDateTime getLastInteractionDate();
        Map<String, Long> getTypeStats();
    }

    /**
     * 小鹅活跃度统计
     */
    interface GooseActivityStats {
        Long getGooseId();
        String getGooseName();
        long getInteractionCount();
        LocalDateTime getLastInteractionTime();
    }
}
