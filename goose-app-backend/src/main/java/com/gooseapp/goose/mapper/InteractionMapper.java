package com.gooseapp.goose.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gooseapp.goose.entity.Interaction;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 互动记录数据访问层 - MyBatis Plus版本
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-12-19
 */
@Mapper
public interface InteractionMapper extends BaseMapper<Interaction> {

    /**
     * 根据用户ID查找互动记录
     */
    @Select("SELECT * FROM interactions WHERE user_id = #{userId} AND is_deleted = 0 ORDER BY created_at DESC")
    List<Interaction> findByUserId(@Param("userId") Long userId);

    /**
     * 根据小鹅ID查找互动记录
     */
    @Select("SELECT * FROM interactions WHERE goose_id = #{gooseId} AND is_deleted = 0 ORDER BY created_at DESC")
    List<Interaction> findByGooseId(@Param("gooseId") Long gooseId);

    /**
     * 根据用户ID和小鹅ID查找互动记录
     */
    @Select("SELECT * FROM interactions WHERE user_id = #{userId} AND goose_id = #{gooseId} AND is_deleted = 0 ORDER BY created_at DESC")
    List<Interaction> findByUserIdAndGooseId(@Param("userId") Long userId, @Param("gooseId") Long gooseId);

    /**
     * 根据互动类型查找记录
     */
    @Select("SELECT * FROM interactions WHERE interaction_type = #{interactionType} AND is_deleted = 0 ORDER BY created_at DESC")
    List<Interaction> findByInteractionType(@Param("interactionType") String interactionType);

    /**
     * 根据用户ID和互动类型查找记录
     */
    @Select("SELECT * FROM interactions WHERE user_id = #{userId} AND interaction_type = #{interactionType} AND is_deleted = 0 ORDER BY created_at DESC")
    List<Interaction> findByUserIdAndInteractionType(@Param("userId") Long userId, @Param("interactionType") String interactionType);

    /**
     * 根据小鹅ID和互动类型查找记录
     */
    @Select("SELECT * FROM interactions WHERE goose_id = #{gooseId} AND interaction_type = #{interactionType} AND is_deleted = 0 ORDER BY created_at DESC")
    List<Interaction> findByGooseIdAndInteractionType(@Param("gooseId") Long gooseId, @Param("interactionType") String interactionType);

    /**
     * 查找指定时间范围内的互动记录
     */
    @Select("SELECT * FROM interactions WHERE created_at BETWEEN #{startTime} AND #{endTime} AND is_deleted = 0 ORDER BY created_at DESC")
    List<Interaction> findByCreatedAtBetween(@Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime);

    /**
     * 查找用户在指定时间范围内的互动记录
     */
    @Select("SELECT * FROM interactions WHERE user_id = #{userId} AND created_at BETWEEN #{startTime} AND #{endTime} AND is_deleted = 0 ORDER BY created_at DESC")
    List<Interaction> findByUserIdAndCreatedAtBetween(@Param("userId") Long userId, @Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime);

    /**
     * 查找小鹅在指定时间范围内的互动记录
     */
    @Select("SELECT * FROM interactions WHERE goose_id = #{gooseId} AND created_at BETWEEN #{startTime} AND #{endTime} AND is_deleted = 0 ORDER BY created_at DESC")
    List<Interaction> findByGooseIdAndCreatedAtBetween(@Param("gooseId") Long gooseId, @Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime);

    /**
     * 统计用户互动次数
     */
    @Select("SELECT COUNT(*) FROM interactions WHERE user_id = #{userId} AND is_deleted = 0")
    long countByUserId(@Param("userId") Long userId);

    /**
     * 统计小鹅互动次数
     */
    @Select("SELECT COUNT(*) FROM interactions WHERE goose_id = #{gooseId} AND is_deleted = 0")
    long countByGooseId(@Param("gooseId") Long gooseId);

    /**
     * 统计用户指定类型的互动次数
     */
    @Select("SELECT COUNT(*) FROM interactions WHERE user_id = #{userId} AND interaction_type = #{interactionType} AND is_deleted = 0")
    long countByUserIdAndInteractionType(@Param("userId") Long userId, @Param("interactionType") String interactionType);

    /**
     * 统计小鹅指定类型的互动次数
     */
    @Select("SELECT COUNT(*) FROM interactions WHERE goose_id = #{gooseId} AND interaction_type = #{interactionType} AND is_deleted = 0")
    long countByGooseIdAndInteractionType(@Param("gooseId") Long gooseId, @Param("interactionType") String interactionType);

    /**
     * 查找用户最近的互动记录
     */
    @Select("SELECT * FROM interactions WHERE user_id = #{userId} AND is_deleted = 0 ORDER BY created_at DESC LIMIT #{limit}")
    List<Interaction> findRecentByUserId(@Param("userId") Long userId, @Param("limit") int limit);

    /**
     * 查找小鹅最近的互动记录
     */
    @Select("SELECT * FROM interactions WHERE goose_id = #{gooseId} AND is_deleted = 0 ORDER BY created_at DESC LIMIT #{limit}")
    List<Interaction> findRecentByGooseId(@Param("gooseId") Long gooseId, @Param("limit") int limit);

    /**
     * 查找用户今日互动记录
     */
    @Select("SELECT * FROM interactions WHERE user_id = #{userId} AND DATE(created_at) = CURDATE() AND is_deleted = 0 ORDER BY created_at DESC")
    List<Interaction> findTodayByUserId(@Param("userId") Long userId);

    /**
     * 查找小鹅今日互动记录
     */
    @Select("SELECT * FROM interactions WHERE goose_id = #{gooseId} AND DATE(created_at) = CURDATE() AND is_deleted = 0 ORDER BY created_at DESC")
    List<Interaction> findTodayByGooseId(@Param("gooseId") Long gooseId);

    /**
     * 统计用户今日互动次数
     */
    @Select("SELECT COUNT(*) FROM interactions WHERE user_id = #{userId} AND DATE(created_at) = CURDATE() AND is_deleted = 0")
    long countTodayByUserId(@Param("userId") Long userId);

    /**
     * 统计小鹅今日互动次数
     */
    @Select("SELECT COUNT(*) FROM interactions WHERE goose_id = #{gooseId} AND DATE(created_at) = CURDATE() AND is_deleted = 0")
    long countTodayByGooseId(@Param("gooseId") Long gooseId);

    /**
     * 分页查询用户互动记录
     */
    @Select("SELECT * FROM interactions WHERE user_id = #{userId} AND is_deleted = 0 ORDER BY created_at DESC")
    IPage<Interaction> findByUserId(@Param("userId") Long userId, Page<Interaction> page);

    /**
     * 分页查询小鹅互动记录
     */
    @Select("SELECT * FROM interactions WHERE goose_id = #{gooseId} AND is_deleted = 0 ORDER BY created_at DESC")
    IPage<Interaction> findByGooseId(@Param("gooseId") Long gooseId, Page<Interaction> page);

    /**
     * 获取互动统计信息
     */
    @Select("SELECT interaction_type, COUNT(*) as count FROM interactions WHERE user_id = #{userId} AND is_deleted = 0 GROUP BY interaction_type")
    List<Object[]> getInteractionStatsByUserId(@Param("userId") Long userId);

    /**
     * 获取小鹅互动统计信息
     */
    @Select("SELECT interaction_type, COUNT(*) as count FROM interactions WHERE goose_id = #{gooseId} AND is_deleted = 0 GROUP BY interaction_type")
    List<Object[]> getInteractionStatsByGooseId(@Param("gooseId") Long gooseId);

    /**
     * 查找最近的互动记录
     */
    @Select("SELECT * FROM interactions WHERE is_deleted = 0 ORDER BY created_at DESC LIMIT #{limit}")
    List<Interaction> findRecentInteractions(@Param("limit") int limit);

    /**
     * 统计总互动次数
     */
    @Select("SELECT COUNT(*) FROM interactions WHERE is_deleted = 0")
    long countTotalInteractions();

    /**
     * 统计今日总互动次数
     */
    @Select("SELECT COUNT(*) FROM interactions WHERE DATE(created_at) = CURDATE() AND is_deleted = 0")
    long countTodayInteractions();

    /**
     * 查找用户最后一次指定类型的互动
     */
    @Select("SELECT * FROM interactions WHERE user_id = #{userId} AND goose_id = #{gooseId} AND interaction_type = #{interactionType} AND is_deleted = 0 ORDER BY created_at DESC LIMIT 1")
    Interaction findLastInteraction(@Param("userId") Long userId, @Param("gooseId") Long gooseId, @Param("interactionType") String interactionType);

    /**
     * 检查用户是否可以进行指定类型的互动（基于冷却时间）
     */
    @Select("SELECT COUNT(*) = 0 FROM interactions WHERE user_id = #{userId} AND goose_id = #{gooseId} AND interaction_type = #{interactionType} " +
            "AND created_at > DATE_SUB(NOW(), INTERVAL #{cooldownMinutes} MINUTE) AND is_deleted = 0")
    boolean canInteract(@Param("userId") Long userId, @Param("gooseId") Long gooseId, @Param("interactionType") String interactionType, @Param("cooldownMinutes") int cooldownMinutes);

    /**
     * 检查是否在冷却期内
     */
    @Select("SELECT COUNT(*) > 0 FROM interactions WHERE user_id = #{userId} AND goose_id = #{gooseId} AND interaction_type = #{interactionType} " +
            "AND created_at > #{cooldownThreshold} AND is_deleted = 0")
    boolean isInCooldown(@Param("userId") Long userId, @Param("gooseId") Long gooseId, @Param("interactionType") String interactionType, @Param("cooldownThreshold") LocalDateTime cooldownThreshold);

    /**
     * 查找最近的互动记录
     */
    @Select("SELECT * FROM interactions WHERE user_id = #{userId} AND goose_id = #{gooseId} AND interaction_type = #{interactionType} " +
            "AND is_deleted = 0 ORDER BY created_at DESC LIMIT 1")
    Interaction findTopByUserIdAndGooseIdAndInteractionTypeOrderByCreatedAtDesc(@Param("userId") Long userId, @Param("gooseId") Long gooseId, @Param("interactionType") String interactionType);

    /**
     * 根据用户ID和小鹅ID查找互动记录
     */
    @Select("SELECT * FROM interactions WHERE user_id = #{userId} AND goose_id = #{gooseId} AND is_deleted = 0 ORDER BY created_at DESC")
    List<Interaction> findByUserIdAndGooseIdOrderByCreatedAtDesc(@Param("userId") Long userId, @Param("gooseId") Long gooseId);

    /**
     * 根据用户ID查找互动记录（按时间倒序）
     */
    @Select("SELECT * FROM interactions WHERE user_id = #{userId} AND is_deleted = 0 ORDER BY created_at DESC")
    List<Interaction> findByUserIdOrderByCreatedAtDesc(@Param("userId") Long userId);

    /**
     * 查找小鹅今日互动记录
     */
    @Select("SELECT * FROM interactions WHERE goose_id = #{gooseId} AND DATE(created_at) = CURDATE() AND is_deleted = 0 ORDER BY created_at DESC")
    List<Interaction> findGooseTodayInteractions(@Param("gooseId") Long gooseId);

    /**
     * 查找用户今日互动记录
     */
    @Select("SELECT * FROM interactions WHERE user_id = #{userId} AND DATE(created_at) = CURDATE() AND is_deleted = 0 ORDER BY created_at DESC")
    List<Interaction> findTodayInteractions(@Param("userId") Long userId);

    /**
     * 按类型统计互动次数
     */
    @Select("SELECT interaction_type, COUNT(*) FROM interactions WHERE user_id = #{userId} AND is_deleted = 0 GROUP BY interaction_type")
    List<Object[]> countInteractionsByType(@Param("userId") Long userId);

    /**
     * 获取周统计数据
     */
    @Select("SELECT DATE(created_at) as date, COUNT(*) as count FROM interactions WHERE user_id = #{userId} AND created_at >= #{startDate} AND is_deleted = 0 GROUP BY DATE(created_at) ORDER BY date")
    List<Object[]> getWeeklyInteractionStats(@Param("userId") Long userId, @Param("startDate") LocalDateTime startDate);

    /**
     * 获取用户总经验值
     */
    @Select("SELECT COALESCE(SUM(experience_gained), 0) FROM interactions WHERE user_id = #{userId} AND is_deleted = 0")
    long getTotalExperienceGained(@Param("userId") Long userId);

    /**
     * 计算连续天数
     */
    @Select("SELECT COUNT(DISTINCT DATE(created_at)) FROM interactions WHERE user_id = #{userId} AND created_at >= #{startDate} AND is_deleted = 0")
    long countContinuousDays(@Param("userId") Long userId, @Param("startDate") LocalDateTime startDate);

    /**
     * 查找最活跃的小鹅
     */
    @Select("SELECT goose_id, COUNT(*) as count FROM interactions WHERE user_id = #{userId} AND is_deleted = 0 GROUP BY goose_id ORDER BY count DESC")
    List<Object[]> findMostActiveGeese(@Param("userId") Long userId);

    /**
     * 根据小鹅ID查找互动记录（按时间倒序）
     */
    @Select("SELECT * FROM interactions WHERE goose_id = #{gooseId} AND is_deleted = 0 ORDER BY created_at DESC")
    List<Interaction> findByGooseIdOrderByCreatedAtDesc(@Param("gooseId") Long gooseId);

    /**
     * 统计用户今日互动次数
     */
    @Select("SELECT COUNT(*) FROM interactions WHERE user_id = #{userId} AND DATE(created_at) = CURDATE() AND is_deleted = 0")
    long countTodayInteractions(@Param("userId") Long userId);

}
