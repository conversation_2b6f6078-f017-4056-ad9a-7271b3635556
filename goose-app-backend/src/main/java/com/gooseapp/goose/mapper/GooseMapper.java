package com.gooseapp.goose.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gooseapp.goose.entity.Goose;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * 小鹅数据访问层 - MyBatis Plus版本
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-12-19
 */
@Mapper
public interface GooseMapper extends BaseMapper<Goose> {

    /**
     * 根据用户ID查找小鹅列表
     */
    @Select("SELECT * FROM geese WHERE user_id = #{userId} AND status = #{status} AND is_deleted = 0 ORDER BY created_at DESC")
    List<Goose> findByUserIdAndStatusOrderByCreatedAtDesc(@Param("userId") Long userId, @Param("status") Integer status);

    /**
     * 根据用户ID查找所有小鹅（包括已转赠）
     */
    @Select("SELECT * FROM geese WHERE user_id = #{userId} AND is_deleted = 0 ORDER BY created_at DESC")
    List<Goose> findByUserIdOrderByCreatedAtDesc(@Param("userId") Long userId);

    /**
     * 根据用户ID和小鹅ID查找小鹅
     */
    @Select("SELECT * FROM geese WHERE id = #{id} AND user_id = #{userId} AND is_deleted = 0")
    Optional<Goose> findByIdAndUserId(@Param("id") Long id, @Param("userId") Long userId);

    /**
     * 根据品种查找小鹅
     */
    @Select("SELECT * FROM geese WHERE breed = #{breed} AND status = #{status} AND is_deleted = 0 ORDER BY created_at DESC")
    List<Goose> findByBreedAndStatusOrderByCreatedAtDesc(@Param("breed") String breed, @Param("status") Integer status);

    /**
     * 查找需要照顾的小鹅
     */
    @Select("SELECT * FROM geese WHERE user_id = #{userId} AND status = 1 AND is_deleted = 0 " +
            "AND (hunger > 80 OR thirst > 80 OR cleanliness < 30 OR health < 50)")
    List<Goose> findGeeseNeedingCare(@Param("userId") Long userId);

    /**
     * 根据等级范围查找小鹅
     */
    @Select("SELECT * FROM geese WHERE level BETWEEN #{minLevel} AND #{maxLevel} AND status = #{status} AND is_deleted = 0 ORDER BY level DESC")
    List<Goose> findByLevelBetweenAndStatusOrderByLevelDesc(@Param("minLevel") Integer minLevel, 
                                                           @Param("maxLevel") Integer maxLevel, 
                                                           @Param("status") Integer status);

    /**
     * 查找用户的小鹅数量
     */
    @Select("SELECT COUNT(*) FROM geese WHERE user_id = #{userId} AND status = #{status} AND is_deleted = 0")
    long countByUserIdAndStatus(@Param("userId") Long userId, @Param("status") Integer status);

    /**
     * 查找所有正常状态的小鹅数量
     */
    @Select("SELECT COUNT(*) FROM geese WHERE status = #{status} AND is_deleted = 0")
    long countByStatus(@Param("status") Integer status);

    /**
     * 分页查找小鹅
     */
    @Select("SELECT * FROM geese WHERE user_id = #{userId} AND status = #{status} AND is_deleted = 0 ORDER BY created_at DESC")
    IPage<Goose> findByUserIdAndStatusOrderByCreatedAtDesc(@Param("userId") Long userId, 
                                                          @Param("status") Integer status, 
                                                          Page<Goose> page);

    /**
     * 查找最近创建的小鹅
     */
    @Select("SELECT * FROM geese WHERE status = #{status} AND is_deleted = 0 ORDER BY created_at DESC LIMIT 10")
    List<Goose> findTop10ByStatusOrderByCreatedAtDesc(@Param("status") Integer status);

    /**
     * 查找等级最高的小鹅
     */
    @Select("SELECT * FROM geese WHERE status = #{status} AND is_deleted = 0 ORDER BY level DESC, experience DESC LIMIT 10")
    List<Goose> findTop10ByStatusOrderByLevelDescExperienceDesc(@Param("status") Integer status);

    /**
     * 根据名称模糊查找小鹅
     */
    @Select("SELECT * FROM geese WHERE user_id = #{userId} AND name LIKE CONCAT('%', #{name}, '%') " +
            "AND status = #{status} AND is_deleted = 0 ORDER BY created_at DESC")
    List<Goose> findByUserIdAndNameContainingIgnoreCaseAndStatusOrderByCreatedAtDesc(@Param("userId") Long userId, 
                                                                                    @Param("name") String name, 
                                                                                    @Param("status") Integer status);

    /**
     * 查找长时间未互动的小鹅
     */
    @Select("SELECT * FROM geese WHERE user_id = #{userId} AND status = 1 AND is_deleted = 0 " +
            "AND (last_feed_time IS NULL OR last_feed_time < #{threshold} " +
            "OR last_water_time IS NULL OR last_water_time < #{threshold} " +
            "OR last_clean_time IS NULL OR last_clean_time < #{threshold})")
    List<Goose> findGeeseNotInteractedSince(@Param("userId") Long userId, @Param("threshold") LocalDateTime threshold);

    /**
     * 查找健康度低于指定值的小鹅
     */
    @Select("SELECT * FROM geese WHERE user_id = #{userId} AND health < #{healthThreshold} " +
            "AND status = #{status} AND is_deleted = 0 ORDER BY health ASC")
    List<Goose> findByUserIdAndHealthLessThanAndStatusOrderByHealthAsc(@Param("userId") Long userId, 
                                                                      @Param("healthThreshold") Integer healthThreshold, 
                                                                      @Param("status") Integer status);

    /**
     * 查找快乐度高的小鹅
     */
    @Select("SELECT * FROM geese WHERE user_id = #{userId} AND happiness > #{happinessThreshold} " +
            "AND status = #{status} AND is_deleted = 0 ORDER BY happiness DESC")
    List<Goose> findByUserIdAndHappinessGreaterThanAndStatusOrderByHappinessDesc(@Param("userId") Long userId, 
                                                                                @Param("happinessThreshold") Integer happinessThreshold, 
                                                                                @Param("status") Integer status);

    /**
     * 统计用户各品种小鹅数量
     */
    @Select("SELECT breed, COUNT(*) FROM geese WHERE user_id = #{userId} AND status = 1 AND is_deleted = 0 GROUP BY breed")
    List<Object[]> countByUserIdAndBreed(@Param("userId") Long userId);

    /**
     * 查找用户等级最高的小鹅
     */
    @Select("SELECT * FROM geese WHERE user_id = #{userId} AND status = #{status} AND is_deleted = 0 " +
            "ORDER BY level DESC, experience DESC LIMIT 1")
    Optional<Goose> findTopByUserIdAndStatusOrderByLevelDescExperienceDesc(@Param("userId") Long userId, 
                                                                          @Param("status") Integer status);

    /**
     * 检查用户是否拥有指定品种的小鹅
     */
    @Select("SELECT COUNT(*) > 0 FROM geese WHERE user_id = #{userId} AND breed = #{breed} " +
            "AND status = #{status} AND is_deleted = 0")
    boolean existsByUserIdAndBreedAndStatus(@Param("userId") Long userId, 
                                          @Param("breed") String breed, 
                                          @Param("status") Integer status);

    /**
     * 查找可以升级的小鹅
     */
    @Select("SELECT * FROM geese WHERE user_id = #{userId} AND status = 1 AND is_deleted = 0 " +
            "AND experience >= (level * 100)")
    List<Goose> findGeeseReadyToLevelUp(@Param("userId") Long userId);

    /**
     * 根据状态查找所有小鹅
     */
    @Select("SELECT * FROM geese WHERE status = #{status} AND is_deleted = 0")
    List<Goose> findByStatus(@Param("status") Integer status);

    // ========== 归属权相关查询方法 ==========

    /**
     * 根据归属状态查找小鹅（分页）
     */
    @Select("SELECT * FROM geese WHERE ownership_status = #{ownershipStatus} AND is_deleted = 0")
    IPage<Goose> findByOwnershipStatus(@Param("ownershipStatus") Integer ownershipStatus, Page<Goose> page);

    /**
     * 根据归属状态查找小鹅列表
     */
    @Select("SELECT * FROM geese WHERE ownership_status = #{ownershipStatus} AND is_deleted = 0")
    List<Goose> findByOwnershipStatus(@Param("ownershipStatus") Integer ownershipStatus);

    /**
     * 统计归属状态的小鹅数量
     */
    @Select("SELECT COUNT(*) FROM geese WHERE ownership_status = #{ownershipStatus} AND is_deleted = 0")
    long countByOwnershipStatus(@Param("ownershipStatus") Integer ownershipStatus);

    /**
     * 根据用户ID查找小鹅（分页）
     */
    @Select("SELECT * FROM geese WHERE user_id = #{userId} AND is_deleted = 0")
    IPage<Goose> findByUserId(@Param("userId") Long userId, Page<Goose> page);

    /**
     * 根据品种查找小鹅（分页）
     */
    @Select("SELECT * FROM geese WHERE breed = #{breed} AND is_deleted = 0")
    IPage<Goose> findByBreed(@Param("breed") String breed, Page<Goose> page);

    /**
     * 查找待售且正常状态的小鹅
     */
    @Select("SELECT * FROM geese WHERE ownership_status = 0 AND status = 1 AND is_deleted = 0 ORDER BY created_at DESC")
    List<Goose> findAvailableGeese();

    /**
     * 查找待售且正常状态的小鹅（分页）
     */
    @Select("SELECT * FROM geese WHERE ownership_status = 0 AND status = 1 AND is_deleted = 0 ORDER BY created_at DESC")
    IPage<Goose> findAvailableGeese(Page<Goose> page);

    /**
     * 根据品种查找待售小鹅
     */
    @Select("SELECT * FROM geese WHERE ownership_status = 0 AND status = 1 AND breed = #{breed} " +
            "AND is_deleted = 0 ORDER BY created_at DESC")
    List<Goose> findAvailableGeeseByBreed(@Param("breed") String breed);

    /**
     * 查找用户已归属的小鹅
     */
    @Select("SELECT * FROM geese WHERE user_id = #{userId} AND ownership_status = 1 AND is_deleted = 0 " +
            "ORDER BY purchase_time DESC")
    List<Goose> findOwnedGeeseByUserId(@Param("userId") Long userId);

    /**
     * 查找用户已归属的小鹅（分页）
     */
    @Select("SELECT * FROM geese WHERE user_id = #{userId} AND ownership_status = 1 AND is_deleted = 0 " +
            "ORDER BY purchase_time DESC")
    IPage<Goose> findOwnedGeeseByUserId(@Param("userId") Long userId, Page<Goose> page);

    /**
     * 统计用户已归属的小鹅数量
     */
    @Select("SELECT COUNT(*) FROM geese WHERE user_id = #{userId} AND ownership_status = #{ownershipStatus} AND is_deleted = 0")
    long countByUserIdAndOwnershipStatus(@Param("userId") Long userId, @Param("ownershipStatus") Integer ownershipStatus);

    /**
     * 检查小鹅是否可以被购买
     */
    @Select("SELECT COUNT(*) > 0 FROM geese WHERE id = #{gooseId} AND ownership_status = 0 AND status = 1 AND is_deleted = 0")
    boolean isGooseAvailableForPurchase(@Param("gooseId") Long gooseId);

    // ========== 商城相关查询方法 ==========

    /**
     * 根据品种和归属状态查找小鹅
     */
    @Select("SELECT * FROM geese WHERE breed = #{breed} AND ownership_status = #{ownershipStatus} AND is_deleted = 0")
    List<Goose> findByBreedAndOwnershipStatus(@Param("breed") String breed, @Param("ownershipStatus") Integer ownershipStatus);

    /**
     * 获取所有不同的品种
     */
    @Select("SELECT DISTINCT breed FROM geese WHERE breed IS NOT NULL AND is_deleted = 0 ORDER BY breed")
    List<String> findDistinctBreeds();

    /**
     * 根据名称模糊查找和归属状态查找小鹅（分页）
     */
    @Select("SELECT * FROM geese WHERE name LIKE CONCAT('%', #{name}, '%') AND ownership_status = #{ownershipStatus} " +
            "AND is_deleted = 0")
    IPage<Goose> findByNameContainingIgnoreCaseAndOwnershipStatus(@Param("name") String name, 
                                                                 @Param("ownershipStatus") Integer ownershipStatus, 
                                                                 Page<Goose> page);

    /**
     * 根据用户ID和名称模糊查找已归属的小鹅
     */
    @Select("SELECT * FROM geese WHERE user_id = #{userId} AND ownership_status = 1 " +
            "AND name LIKE CONCAT('%', #{name}, '%') AND is_deleted = 0 ORDER BY purchase_time DESC")
    List<Goose> findOwnedGeeseByUserIdAndNameContaining(@Param("userId") Long userId, @Param("name") String name);

    /**
     * 分页查询所有小鹅
     */
    @Select("SELECT * FROM geese WHERE is_deleted = 0 ORDER BY created_at DESC")
    IPage<Goose> findAll(Page<Goose> page);

    /**
     * 统计所有小鹅数量
     */
    @Select("SELECT COUNT(*) FROM geese WHERE is_deleted = 0")
    long count();

    /**
     * 根据名称模糊查找小鹅（分页）
     */
    @Select("SELECT * FROM geese WHERE name LIKE CONCAT('%', #{name}, '%') AND is_deleted = 0 ORDER BY created_at DESC")
    IPage<Goose> findByNameContainingIgnoreCase(@Param("name") String name, Page<Goose> page);
}
