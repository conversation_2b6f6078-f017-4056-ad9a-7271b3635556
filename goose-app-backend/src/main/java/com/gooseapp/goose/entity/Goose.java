package com.gooseapp.goose.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.gooseapp.common.entity.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 小鹅实体类
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-12-19
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("geese")
public class Goose extends BaseEntity {

    /**
     * 所属用户ID（可为空，待售状态时为空）
     */
    private Long userId;

    /**
     * 小鹅名称
     */
    private String name;

    /**
     * 品种
     */
    private String breed;

    /**
     * 性别：1-雄性，2-雌性
     */
    private Integer gender;

    /**
     * 年龄（周）
     */
    private Integer age = 0;

    /**
     * 健康度（0-100）
     */
    private Integer health = 100;

    /**
     * 饥饿度（0-100）
     */
    private Integer hunger = 50;

    /**
     * 口渴度（0-100）
     */
    private Integer thirst = 50;

    /**
     * 清洁度（0-100）
     */
    private Integer cleanliness = 100;

    /**
     * 快乐度（0-100）
     */
    private Integer happiness = 80;

    /**
     * 等级
     */
    private Integer level = 1;

    /**
     * 经验值
     */
    private Integer experience = 0;

    /**
     * 小鹅图片URL
     */
    private String imageUrl;

    /**
     * 直播视频URL
     */
    private String videoUrl;

    /**
     * 最后喂食时间
     */
    private LocalDateTime lastFeedTime;

    /**
     * 最后喂水时间
     */
    private LocalDateTime lastWaterTime;

    /**
     * 最后清洁时间
     */
    private LocalDateTime lastCleanTime;

    /**
     * 归属状态：0-待售，1-已归属
     */
    private Integer ownershipStatus = 0;

    /**
     * 购买时间
     */
    private LocalDateTime purchaseTime;

    /**
     * 性别枚举
     */
    public enum Gender {
        MALE(1, "雄性"),
        FEMALE(2, "雌性");

        private final Integer code;
        private final String description;

        Gender(Integer code, String description) {
            this.code = code;
            this.description = description;
        }

        public Integer getCode() {
            return code;
        }

        public String getDescription() {
            return description;
        }

        public static Gender fromCode(Integer code) {
            for (Gender gender : values()) {
                if (gender.code.equals(code)) {
                    return gender;
                }
            }
            return MALE;
        }
    }

    /**
     * 状态枚举
     */
    public enum Status {
        GIFTED(0, "已转赠"),
        NORMAL(1, "正常"),
        SICK(2, "生病");

        private final Integer code;
        private final String description;

        Status(Integer code, String description) {
            this.code = code;
            this.description = description;
        }

        public Integer getCode() {
            return code;
        }

        public String getDescription() {
            return description;
        }

        public static Status fromCode(Integer code) {
            for (Status status : values()) {
                if (status.code.equals(code)) {
                    return status;
                }
            }
            return NORMAL;
        }
    }

    /**
     * 归属状态枚举
     */
    public enum OwnershipStatus {
        AVAILABLE(0, "待售"),
        OWNED(1, "已归属");

        private final Integer code;
        private final String description;

        OwnershipStatus(Integer code, String description) {
            this.code = code;
            this.description = description;
        }

        public Integer getCode() {
            return code;
        }

        public String getDescription() {
            return description;
        }

        public static OwnershipStatus fromCode(Integer code) {
            for (OwnershipStatus status : values()) {
                if (status.code.equals(code)) {
                    return status;
                }
            }
            return AVAILABLE;
        }
    }

    /**
     * 品种枚举
     */
    public enum Breed {
        WHITE("白鹅", "温顺可爱的白鹅"),
        GRAY("灰鹅", "聪明活泼的灰鹅"),
        FLOWER("花鹅", "稀有品种花鹅"),
        BLACK("黑鹅", "神秘优雅的黑鹅"),
        GOLDEN("金鹅", "珍贵的金色小鹅"),
        BROWN("棕鹅", "朴实可靠的棕鹅");

        private final String name;
        private final String description;

        Breed(String name, String description) {
            this.name = name;
            this.description = description;
        }

        public String getName() {
            return name;
        }

        public String getDescription() {
            return description;
        }

        /**
         * 根据中文名称获取品种枚举
         */
        public static Breed fromName(String name) {
            for (Breed breed : values()) {
                if (breed.name.equals(name)) {
                    return breed;
                }
            }
            return WHITE; // 默认返回白鹅
        }
    }

    // 业务方法

    /**
     * 检查是否为正常状态
     */
    public boolean isNormal() {
        return Status.NORMAL.getCode().equals(this.status);
    }

    /**
     * 检查是否生病
     */
    public boolean isSick() {
        return Status.SICK.getCode().equals(this.status);
    }

    /**
     * 检查是否已转赠
     */
    public boolean isGifted() {
        return Status.GIFTED.getCode().equals(this.status);
    }

    /**
     * 获取整体状态评分
     */
    public double getOverallScore() {
        return (health + (100 - hunger) + (100 - thirst) + cleanliness + happiness) / 5.0;
    }

    /**
     * 检查是否需要照顾
     */
    public boolean needsCare() {
        return hunger > 80 || thirst > 80 || cleanliness < 30 || health < 50;
    }

    /**
     * 获取下一级所需经验
     */
    public int getExpToNextLevel() {
        return level * 100 - experience;
    }

    /**
     * 检查是否为待售状态
     */
    public boolean isAvailable() {
        return OwnershipStatus.AVAILABLE.getCode().equals(this.ownershipStatus);
    }

    /**
     * 检查是否已归属
     */
    public boolean isOwned() {
        return OwnershipStatus.OWNED.getCode().equals(this.ownershipStatus);
    }

    /**
     * 设置为待售状态
     */
    public void setAvailable() {
        this.ownershipStatus = OwnershipStatus.AVAILABLE.getCode();
        this.userId = null;
        this.purchaseTime = null;
    }

    /**
     * 设置为已归属状态
     */
    public void setOwned(Long userId) {
        this.ownershipStatus = OwnershipStatus.OWNED.getCode();
        this.userId = userId;
        this.purchaseTime = LocalDateTime.now();
    }

    /**
     * 检查是否可以被购买
     */
    public boolean canBePurchased() {
        return isAvailable() && isNormal();
    }
}
