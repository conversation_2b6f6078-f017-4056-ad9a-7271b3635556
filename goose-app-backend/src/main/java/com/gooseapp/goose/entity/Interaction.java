package com.gooseapp.goose.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.gooseapp.common.entity.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 互动记录实体类
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-12-19
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("interactions")
public class Interaction extends BaseEntity {

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 小鹅ID
     */
    private Long gooseId;

    /**
     * 互动类型：feed-喂食，water-喂水，clean-清洁，play-玩耍
     */
    private String interactionType;

    /**
     * 数值变化
     */
    private Integer valueChange = 0;

    /**
     * 获得经验
     */
    private Integer experienceGained = 0;

    /**
     * 冷却时间（分钟）
     */
    private Integer cooldownMinutes = 0;

    /**
     * 互动类型枚举
     */
    public enum Type {
        FEED("feed", "喂食", 60, 15, 5),
        WATER("water", "喂水", 30, 10, 3),
        CLEAN("clean", "清洁", 120, 20, 8),
        PLAY("play", "玩耍", 45, 8, 6);

        private final String code;
        private final String description;
        private final Integer cooldownMinutes;
        private final Integer valueChange;
        private final Integer experienceGained;

        Type(String code, String description, Integer cooldownMinutes,
             Integer valueChange, Integer experienceGained) {
            this.code = code;
            this.description = description;
            this.cooldownMinutes = cooldownMinutes;
            this.valueChange = valueChange;
            this.experienceGained = experienceGained;
        }

        public String getCode() {
            return code;
        }

        public String getDescription() {
            return description;
        }

        public Integer getCooldownMinutes() {
            return cooldownMinutes;
        }

        public Integer getValueChange() {
            return valueChange;
        }

        public Integer getExperienceGained() {
            return experienceGained;
        }

        public static Type fromCode(String code) {
            for (Type type : values()) {
                if (type.code.equals(code)) {
                    return type;
                }
            }
            return FEED;
        }
    }

    // 构造方法

    public Interaction() {}

    public Interaction(Long userId, Long gooseId, Type type) {
        this.userId = userId;
        this.gooseId = gooseId;
        this.interactionType = type.getCode();
        this.valueChange = type.getValueChange();
        this.experienceGained = type.getExperienceGained();
        this.cooldownMinutes = type.getCooldownMinutes();
    }

    // 业务方法

    /**
     * 获取互动类型枚举
     */
    public Type getType() {
        if (this.interactionType == null) {
            return Type.FEED; // 默认返回喂食
        }
        return Type.fromCode(this.interactionType);
    }

    /**
     * 检查是否在冷却期
     */
    public boolean isInCooldown() {
        if (this.createdAt == null || this.cooldownMinutes == null) {
            return false;
        }
        LocalDateTime cooldownEnd = this.createdAt.plusMinutes(this.cooldownMinutes);
        return LocalDateTime.now().isBefore(cooldownEnd);
    }

    /**
     * 获取剩余冷却时间（分钟）
     */
    public long getRemainingCooldownMinutes() {
        if (!isInCooldown()) {
            return 0;
        }
        LocalDateTime cooldownEnd = this.createdAt.plusMinutes(this.cooldownMinutes);
        return java.time.Duration.between(LocalDateTime.now(), cooldownEnd).toMinutes();
    }

    /**
     * 获取冷却结束时间
     */
    public LocalDateTime getCooldownEndTime() {
        if (this.createdAt == null || this.cooldownMinutes == null) {
            return null;
        }
        return this.createdAt.plusMinutes(this.cooldownMinutes);
    }
}
