package com.gooseapp.shop.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.gooseapp.common.constant.ErrorCode;
import com.gooseapp.common.exception.BusinessException;
import com.gooseapp.goose.dto.GooseDTO;
import com.gooseapp.goose.entity.Goose;
import com.gooseapp.goose.mapper.GooseMapper;
import com.gooseapp.shop.dto.GoosePurchaseDTO;
import com.gooseapp.shop.dto.PurchaseGooseRequest;
import com.gooseapp.shop.dto.PurchaseResultDTO;
import com.gooseapp.shop.entity.GoosePurchase;
import com.gooseapp.shop.mapper.GoosePurchaseMapper;
import com.gooseapp.shop.service.PurchaseService;
import com.gooseapp.user.mapper.UserMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 购买服务实现类
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-12-19
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class PurchaseServiceImpl extends ServiceImpl<GoosePurchaseMapper, GoosePurchase> implements PurchaseService {

    private final GoosePurchaseMapper goosePurchaseMapper;
    private final GooseMapper gooseMapper;
    private final UserMapper userMapper;

    @Override
    @Transactional
    public PurchaseResultDTO purchaseGoose(Long userId, PurchaseGooseRequest request) {
        log.info("用户[{}]尝试购买小鹅[{}]，价格：{}", userId, request.getGooseId(), request.getPurchasePrice());

        // 验证请求参数
        request.validate();

        // 验证用户是否存在
        if (!userMapper.existsById(userId)) {
            throw new BusinessException(ErrorCode.USER_NOT_FOUND, "用户不存在");
        }

        // 获取小鹅信息
        Goose goose = Optional.ofNullable(gooseMapper.selectById(request.getGooseId()))
                .orElseThrow(() -> new BusinessException(ErrorCode.GOOSE_NOT_FOUND, "小鹅不存在"));

        // 检查小鹅是否可以购买
        if (!goose.canBePurchased()) {
            String reason = goose.isOwned() ? "小鹅已被购买" : "小鹅状态异常，无法购买";
            throw new BusinessException(ErrorCode.GOOSE_NOT_AVAILABLE, reason);
        }

        // 检查小鹅是否已有购买记录
        if (goosePurchaseMapper.existsByGooseIdAndStatus(request.getGooseId(), 1)) {
            throw new BusinessException(ErrorCode.GOOSE_ALREADY_PURCHASED, "小鹅已被购买");
        }

        try {
            // 创建购买记录
            GoosePurchase purchase = new GoosePurchase(
                    userId,
                    request.getGooseId(),
                    request.getProductId(),
                    request.getPurchasePrice()
            );
            purchase.setSuccess(); // 设置为成功状态

            // 保存购买记录
            goosePurchaseMapper.insert(purchase);

            // 更新小鹅归属
            goose.setOwned(userId);
            gooseMapper.updateById(goose);

            // 构建返回结果
            GooseDTO gooseDTO = convertGooseToDTO(goose);
            PurchaseResultDTO result = new PurchaseResultDTO(
                    purchase.getId(),
                    userId,
                    gooseDTO,
                    request.getPurchasePrice(),
                    purchase.getPurchaseTime(),
                    1 // 成功状态
            );
            result.setProductId(request.getProductId());

            log.info("用户[{}]成功购买小鹅[{}]，购买记录ID：{}", userId, request.getGooseId(), purchase.getId());
            return result;

        } catch (Exception e) {
            log.error("用户[{}]购买小鹅[{}]失败：{}", userId, request.getGooseId(), e.getMessage(), e);

            // 创建失败的购买记录
            GoosePurchase failedPurchase = new GoosePurchase(
                    userId,
                    request.getGooseId(),
                    request.getProductId(),
                    request.getPurchasePrice()
            );
            failedPurchase.setFailed(); // 设置为失败状态
            goosePurchaseMapper.insert(failedPurchase);

            throw new BusinessException(ErrorCode.PURCHASE_FAILED, "购买失败：" + e.getMessage());
        }
    }

    @Override
    public org.springframework.data.domain.Page<GoosePurchaseDTO> getUserPurchaseHistory(Long userId, Pageable pageable) {
        // 将Spring Data Pageable转换为MyBatis Plus Page
        Page<GoosePurchase> mybatisPage = new Page<>(pageable.getPageNumber() + 1, pageable.getPageSize());
        IPage<GoosePurchase> purchasePage = goosePurchaseMapper.findByUserId(userId, mybatisPage);
        
        // 转换为DTO列表
        List<GoosePurchaseDTO> dtoList = purchasePage.getRecords().stream()
                .map(this::convertPurchaseToDTO)
                .collect(Collectors.toList());
        
        // 返回Spring Data Page
        return new PageImpl<>(dtoList, pageable, purchasePage.getTotal());
    }

    @Override
    public List<GoosePurchaseDTO> getUserPurchaseList(Long userId) {
        List<GoosePurchase> purchases = goosePurchaseMapper.findByUserId(userId);
        return purchases.stream()
                .map(this::convertPurchaseToDTO)
                .collect(Collectors.toList());
    }

    @Override
    public GoosePurchaseDTO getPurchaseById(Long purchaseId) {
        GoosePurchase purchase = goosePurchaseMapper.selectById(purchaseId);
        if (purchase == null) {
            throw new BusinessException(ErrorCode.PURCHASE_NOT_FOUND, "购买记录不存在");
        }
        return convertPurchaseToDTO(purchase);
    }

    @Override
    public GoosePurchaseDTO getPurchaseByGooseId(Long gooseId) {
        Optional<GoosePurchase> purchase = goosePurchaseMapper.findByGooseId(gooseId);
        return purchase.map(this::convertPurchaseToDTO).orElse(null);
    }

    @Override
    public boolean isGooseAvailableForPurchase(Long gooseId) {
        return gooseMapper.isGooseAvailableForPurchase(gooseId);
    }

    @Override
    public UserPurchaseStatistics getUserPurchaseStatistics(Long userId) {
        Long totalPurchases = goosePurchaseMapper.countByUserId(userId);
        Long successfulPurchases = goosePurchaseMapper.countByUserIdAndStatus(userId, 1);
        Long failedPurchases = totalPurchases - successfulPurchases;
        BigDecimal totalSpent = goosePurchaseMapper.sumPurchasePriceByUserIdAndStatus(userId, 1);

        // 获取第一次和最后一次购买时间
        List<GoosePurchase> recentPurchases = goosePurchaseMapper.findRecentPurchasesByUserId(userId, 1);
        LocalDateTime lastPurchaseTime = recentPurchases.isEmpty() ? null : recentPurchases.get(0).getPurchaseTime();

        // 这里简化处理，实际可以添加查询最早购买时间的方法
        LocalDateTime firstPurchaseTime = lastPurchaseTime; // 简化处理

        return new UserPurchaseStatistics(
                totalPurchases,
                successfulPurchases,
                failedPurchases,
                totalSpent != null ? totalSpent : BigDecimal.ZERO,
                firstPurchaseTime,
                lastPurchaseTime
        );
    }

    @Override
    public List<GoosePurchaseDTO> getPurchasesByTimeRange(Long userId, LocalDateTime startTime, LocalDateTime endTime) {
        List<GoosePurchase> purchases = goosePurchaseMapper.findByUserIdAndPurchaseTimeBetween(userId, startTime, endTime);
        return purchases.stream()
                .map(this::convertPurchaseToDTO)
                .collect(Collectors.toList());
    }

    @Override
    public org.springframework.data.domain.Page<GoosePurchaseDTO> getAllPurchases(Pageable pageable) {
        // 将Spring Data Pageable转换为MyBatis Plus Page
        Page<GoosePurchase> mybatisPage = new Page<>(pageable.getPageNumber() + 1, pageable.getPageSize());
        IPage<GoosePurchase> purchasePage = goosePurchaseMapper.findAll(mybatisPage);
        
        // 转换为DTO列表
        List<GoosePurchaseDTO> dtoList = purchasePage.getRecords().stream()
                .map(this::convertPurchaseToDTO)
                .collect(Collectors.toList());
        
        // 返回Spring Data Page
        return new PageImpl<>(dtoList, pageable, purchasePage.getTotal());
    }

    @Override
    public org.springframework.data.domain.Page<GoosePurchaseDTO> getPurchasesByStatus(Integer status, Pageable pageable) {
        // 将Spring Data Pageable转换为MyBatis Plus Page
        Page<GoosePurchase> mybatisPage = new Page<>(pageable.getPageNumber() + 1, pageable.getPageSize());
        IPage<GoosePurchase> purchasePage = goosePurchaseMapper.findByStatus(status, mybatisPage);
        
        // 转换为DTO列表
        List<GoosePurchaseDTO> dtoList = purchasePage.getRecords().stream()
                .map(this::convertPurchaseToDTO)
                .collect(Collectors.toList());
        
        // 返回Spring Data Page
        return new PageImpl<>(dtoList, pageable, purchasePage.getTotal());
    }

    @Override
    public List<PurchaseStatistics> getPurchaseStatistics(LocalDateTime startTime, LocalDateTime endTime) {
        List<Object[]> statistics = goosePurchaseMapper.getPurchaseStatistics(startTime, endTime);

        return statistics.stream()
                .map(stat -> new PurchaseStatistics(
                        (LocalDateTime) stat[0],  // 日期
                        (Long) stat[1],           // 购买次数
                        (BigDecimal) stat[2]      // 总金额
                ))
                .collect(Collectors.toList());
    }

    /**
     * 转换购买记录为DTO
     */
    private GoosePurchaseDTO convertPurchaseToDTO(GoosePurchase purchase) {
        if (purchase == null) {
            return null;
        }

        GoosePurchaseDTO dto = GoosePurchaseDTO.fromEntity(purchase);

        // 获取小鹅信息
        Goose goose = gooseMapper.selectById(purchase.getGooseId());
        if (goose != null) {
            dto.setGooseName(goose.getName());
            dto.setGooseBreed(goose.getBreed());
        }

        return dto;
    }

    /**
     * 转换小鹅实体为DTO
     */
    private GooseDTO convertGooseToDTO(Goose goose) {
        if (goose == null) {
            return null;
        }

        GooseDTO dto = new GooseDTO();
        dto.setId(goose.getId());
        dto.setUserId(goose.getUserId());
        dto.setName(goose.getName());
        dto.setBreed(goose.getBreed());
        dto.setGender(goose.getGender());
        dto.setAge(goose.getAge());
        dto.setHealth(goose.getHealth());
        dto.setHunger(goose.getHunger());
        dto.setThirst(goose.getThirst());
        dto.setCleanliness(goose.getCleanliness());
        dto.setHappiness(goose.getHappiness());
        dto.setLevel(goose.getLevel());
        dto.setExperience(goose.getExperience());
        dto.setImageUrl(goose.getImageUrl());
        dto.setVideoUrl(goose.getVideoUrl());
        dto.setOwnershipStatus(goose.getOwnershipStatus());
        dto.setPurchaseTime(goose.getPurchaseTime());
        dto.setStatus(goose.getStatus());
        dto.setCreatedAt(goose.getCreatedAt());
        dto.setUpdatedAt(goose.getUpdatedAt());

        return dto;
    }
}
