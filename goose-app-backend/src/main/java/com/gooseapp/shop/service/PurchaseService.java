package com.gooseapp.shop.service;

import com.gooseapp.shop.dto.GoosePurchaseDTO;
import com.gooseapp.shop.dto.PurchaseGooseRequest;
import com.gooseapp.shop.dto.PurchaseResultDTO;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 购买服务接口
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-12-19
 */
public interface PurchaseService {

    /**
     * 购买小鹅
     * 
     * @param userId 用户ID
     * @param request 购买请求
     * @return 购买结果
     */
    PurchaseResultDTO purchaseGoose(Long userId, PurchaseGooseRequest request);

    /**
     * 获取用户的购买记录
     * 
     * @param userId 用户ID
     * @param pageable 分页参数
     * @return 购买记录分页列表
     */
    Page<GoosePurchaseDTO> getUserPurchaseHistory(Long userId, Pageable pageable);

    /**
     * 获取用户的购买记录列表
     * 
     * @param userId 用户ID
     * @return 购买记录列表
     */
    List<GoosePurchaseDTO> getUserPurchaseList(Long userId);

    /**
     * 根据购买记录ID获取详情
     * 
     * @param purchaseId 购买记录ID
     * @return 购买记录详情
     */
    GoosePurchaseDTO getPurchaseById(Long purchaseId);

    /**
     * 根据小鹅ID获取购买记录
     * 
     * @param gooseId 小鹅ID
     * @return 购买记录（可选）
     */
    GoosePurchaseDTO getPurchaseByGooseId(Long gooseId);

    /**
     * 检查小鹅是否可以购买
     * 
     * @param gooseId 小鹅ID
     * @return 是否可以购买
     */
    boolean isGooseAvailableForPurchase(Long gooseId);

    /**
     * 获取用户购买统计信息
     * 
     * @param userId 用户ID
     * @return 购买统计信息
     */
    UserPurchaseStatistics getUserPurchaseStatistics(Long userId);

    /**
     * 获取时间范围内的购买记录
     * 
     * @param userId 用户ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 购买记录列表
     */
    List<GoosePurchaseDTO> getPurchasesByTimeRange(Long userId, LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 获取所有购买记录（管理员功能）
     * 
     * @param pageable 分页参数
     * @return 购买记录分页列表
     */
    Page<GoosePurchaseDTO> getAllPurchases(Pageable pageable);

    /**
     * 根据状态获取购买记录（管理员功能）
     * 
     * @param status 状态
     * @param pageable 分页参数
     * @return 购买记录分页列表
     */
    Page<GoosePurchaseDTO> getPurchasesByStatus(Integer status, Pageable pageable);

    /**
     * 获取购买统计信息（管理员功能）
     * 
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 购买统计信息
     */
    List<PurchaseStatistics> getPurchaseStatistics(LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 用户购买统计信息
     */
    class UserPurchaseStatistics {
        private Long totalPurchases;
        private Long successfulPurchases;
        private Long failedPurchases;
        private BigDecimal totalSpent;
        private LocalDateTime firstPurchaseTime;
        private LocalDateTime lastPurchaseTime;

        // 构造函数
        public UserPurchaseStatistics(Long totalPurchases, Long successfulPurchases, Long failedPurchases,
                                    BigDecimal totalSpent, LocalDateTime firstPurchaseTime, LocalDateTime lastPurchaseTime) {
            this.totalPurchases = totalPurchases;
            this.successfulPurchases = successfulPurchases;
            this.failedPurchases = failedPurchases;
            this.totalSpent = totalSpent;
            this.firstPurchaseTime = firstPurchaseTime;
            this.lastPurchaseTime = lastPurchaseTime;
        }

        // Getters
        public Long getTotalPurchases() { return totalPurchases; }
        public Long getSuccessfulPurchases() { return successfulPurchases; }
        public Long getFailedPurchases() { return failedPurchases; }
        public BigDecimal getTotalSpent() { return totalSpent; }
        public LocalDateTime getFirstPurchaseTime() { return firstPurchaseTime; }
        public LocalDateTime getLastPurchaseTime() { return lastPurchaseTime; }

        // Setters
        public void setTotalPurchases(Long totalPurchases) { this.totalPurchases = totalPurchases; }
        public void setSuccessfulPurchases(Long successfulPurchases) { this.successfulPurchases = successfulPurchases; }
        public void setFailedPurchases(Long failedPurchases) { this.failedPurchases = failedPurchases; }
        public void setTotalSpent(BigDecimal totalSpent) { this.totalSpent = totalSpent; }
        public void setFirstPurchaseTime(LocalDateTime firstPurchaseTime) { this.firstPurchaseTime = firstPurchaseTime; }
        public void setLastPurchaseTime(LocalDateTime lastPurchaseTime) { this.lastPurchaseTime = lastPurchaseTime; }
    }

    /**
     * 购买统计信息
     */
    class PurchaseStatistics {
        private LocalDateTime date;
        private Long purchaseCount;
        private BigDecimal totalAmount;

        public PurchaseStatistics(LocalDateTime date, Long purchaseCount, BigDecimal totalAmount) {
            this.date = date;
            this.purchaseCount = purchaseCount;
            this.totalAmount = totalAmount;
        }

        // Getters and Setters
        public LocalDateTime getDate() { return date; }
        public void setDate(LocalDateTime date) { this.date = date; }
        public Long getPurchaseCount() { return purchaseCount; }
        public void setPurchaseCount(Long purchaseCount) { this.purchaseCount = purchaseCount; }
        public BigDecimal getTotalAmount() { return totalAmount; }
        public void setTotalAmount(BigDecimal totalAmount) { this.totalAmount = totalAmount; }
    }
}
