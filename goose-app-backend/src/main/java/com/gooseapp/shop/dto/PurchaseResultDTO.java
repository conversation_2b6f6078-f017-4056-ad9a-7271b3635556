package com.gooseapp.shop.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.gooseapp.goose.dto.GooseDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 购买结果DTO
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-12-19
 */
@Data
@Schema(description = "购买结果信息")
public class PurchaseResultDTO {

    /**
     * 购买记录ID
     */
    @Schema(description = "购买记录ID", example = "1")
    private Long purchaseId;

    /**
     * 购买用户ID
     */
    @Schema(description = "购买用户ID", example = "1")
    private Long userId;

    /**
     * 购买的小鹅信息
     */
    @Schema(description = "购买的小鹅信息")
    private GooseDTO goose;

    /**
     * 购买价格
     */
    @Schema(description = "购买价格", example = "99.99")
    private BigDecimal purchasePrice;

    /**
     * 购买时间
     */
    @Schema(description = "购买时间", example = "2024-12-19T10:30:00")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime purchaseTime;

    /**
     * 购买状态
     */
    @Schema(description = "购买状态：1-成功，0-失败", example = "1")
    private Integer status;

    /**
     * 购买状态描述
     */
    @Schema(description = "购买状态描述", example = "购买成功")
    private String statusDescription;

    /**
     * 关联商品ID
     */
    @Schema(description = "关联商品ID", example = "1")
    private Long productId;

    /**
     * 默认构造函数
     */
    public PurchaseResultDTO() {
    }

    /**
     * 构造函数
     * 
     * @param purchaseId 购买记录ID
     * @param userId 用户ID
     * @param goose 小鹅信息
     * @param purchasePrice 购买价格
     * @param purchaseTime 购买时间
     * @param status 状态
     */
    public PurchaseResultDTO(Long purchaseId, Long userId, GooseDTO goose, 
                           BigDecimal purchasePrice, LocalDateTime purchaseTime, Integer status) {
        this.purchaseId = purchaseId;
        this.userId = userId;
        this.goose = goose;
        this.purchasePrice = purchasePrice;
        this.purchaseTime = purchaseTime;
        this.status = status;
        this.statusDescription = getStatusText(status);
    }

    /**
     * 检查购买是否成功
     */
    public boolean isSuccess() {
        return Integer.valueOf(1).equals(status);
    }

    /**
     * 检查购买是否失败
     */
    public boolean isFailed() {
        return Integer.valueOf(0).equals(status);
    }

    /**
     * 获取状态描述
     */
    private String getStatusText(Integer status) {
        if (status == null) {
            return "未知状态";
        }
        return status == 1 ? "购买成功" : "购买失败";
    }

    /**
     * 设置状态并更新描述
     */
    public void setStatus(Integer status) {
        this.status = status;
        this.statusDescription = getStatusText(status);
    }

    @Override
    public String toString() {
        return String.format("PurchaseResultDTO{purchaseId=%d, userId=%d, gooseId=%d, price=%s, status=%s}", 
                           purchaseId, userId, goose != null ? goose.getId() : null, 
                           purchasePrice, statusDescription);
    }
}
