package com.gooseapp.shop.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 小鹅购买记录DTO
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-12-19
 */
@Data
@Schema(description = "小鹅购买记录信息")
public class GoosePurchaseDTO {

    /**
     * 购买记录ID
     */
    @Schema(description = "购买记录ID", example = "1")
    private Long id;

    /**
     * 购买用户ID
     */
    @Schema(description = "购买用户ID", example = "1")
    private Long userId;

    /**
     * 购买的小鹅ID
     */
    @Schema(description = "购买的小鹅ID", example = "1")
    private Long gooseId;

    /**
     * 小鹅名称
     */
    @Schema(description = "小鹅名称", example = "小白")
    private String gooseName;

    /**
     * 小鹅品种
     */
    @Schema(description = "小鹅品种", example = "白鹅")
    private String gooseBreed;

    /**
     * 关联商品ID
     */
    @Schema(description = "关联商品ID", example = "1")
    private Long productId;

    /**
     * 购买价格
     */
    @Schema(description = "购买价格", example = "99.99")
    private BigDecimal purchasePrice;

    /**
     * 购买时间
     */
    @Schema(description = "购买时间", example = "2024-12-19T10:30:00")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime purchaseTime;

    /**
     * 状态：1-成功，0-失败
     */
    @Schema(description = "状态：1-成功，0-失败", example = "1")
    private Integer status;

    /**
     * 状态描述
     */
    @Schema(description = "状态描述", example = "购买成功")
    private String statusDescription;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间", example = "2024-12-19T10:30:00")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @Schema(description = "更新时间", example = "2024-12-19T10:30:00")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updatedAt;

    /**
     * 默认构造函数
     */
    public GoosePurchaseDTO() {
    }

    /**
     * 从实体对象创建DTO
     * 
     * @param purchase 购买记录实体
     * @return 购买记录DTO
     */
    public static GoosePurchaseDTO fromEntity(com.gooseapp.shop.entity.GoosePurchase purchase) {
        if (purchase == null) {
            return null;
        }

        GoosePurchaseDTO dto = new GoosePurchaseDTO();
        dto.setId(purchase.getId());
        dto.setUserId(purchase.getUserId());
        dto.setGooseId(purchase.getGooseId());
        dto.setProductId(purchase.getProductId());
        dto.setPurchasePrice(purchase.getPurchasePrice());
        dto.setPurchaseTime(purchase.getPurchaseTime());
        dto.setStatus(purchase.getStatus());
        dto.setStatusDescription(purchase.getStatusDescription());
        dto.setCreatedAt(purchase.getCreatedAt());
        dto.setUpdatedAt(purchase.getUpdatedAt());

        return dto;
    }

    /**
     * 转换为实体对象
     * 
     * @return 购买记录实体
     */
    public com.gooseapp.shop.entity.GoosePurchase toEntity() {
        com.gooseapp.shop.entity.GoosePurchase purchase = new com.gooseapp.shop.entity.GoosePurchase();
        purchase.setId(this.id);
        purchase.setUserId(this.userId);
        purchase.setGooseId(this.gooseId);
        purchase.setProductId(this.productId);
        purchase.setPurchasePrice(this.purchasePrice);
        purchase.setPurchaseTime(this.purchaseTime);
        purchase.setStatus(this.status);
        purchase.setCreatedAt(this.createdAt);
        purchase.setUpdatedAt(this.updatedAt);

        return purchase;
    }

    /**
     * 检查购买是否成功
     */
    public boolean isSuccess() {
        return Integer.valueOf(1).equals(status);
    }

    /**
     * 检查购买是否失败
     */
    public boolean isFailed() {
        return Integer.valueOf(0).equals(status);
    }

    @Override
    public String toString() {
        return String.format("GoosePurchaseDTO{id=%d, userId=%d, gooseId=%d, price=%s, status=%s}", 
                           id, userId, gooseId, purchasePrice, statusDescription);
    }
}
