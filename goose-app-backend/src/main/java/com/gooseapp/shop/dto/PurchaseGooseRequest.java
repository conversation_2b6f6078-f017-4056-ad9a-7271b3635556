package com.gooseapp.shop.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 购买小鹅请求DTO
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-12-19
 */
@Data
@Schema(description = "购买小鹅请求")
public class PurchaseGooseRequest {

    /**
     * 小鹅ID
     */
    @NotNull(message = "小鹅ID不能为空")
    @Schema(description = "小鹅ID", example = "1", required = true)
    private Long gooseId;

    /**
     * 购买价格
     */
    @NotNull(message = "购买价格不能为空")
    @Positive(message = "购买价格必须大于0")
    @Schema(description = "购买价格", example = "99.99", required = true)
    private BigDecimal purchasePrice;

    /**
     * 关联商品ID（可选）
     */
    @Schema(description = "关联商品ID", example = "1")
    private Long productId;

    /**
     * 备注信息
     */
    @Schema(description = "备注信息", example = "购买小鹅作为宠物")
    private String remark;

    /**
     * 验证请求参数
     */
    public void validate() {
        if (remark != null) {
            remark = remark.trim();
        }
    }

    @Override
    public String toString() {
        return String.format("PurchaseGooseRequest{gooseId=%d, price=%s, productId=%d}", 
                           gooseId, purchasePrice, productId);
    }
}
