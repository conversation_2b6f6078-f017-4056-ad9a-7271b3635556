package com.gooseapp.shop.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.gooseapp.common.entity.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 小鹅购买记录实体类
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-12-19
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("goose_purchases")
public class GoosePurchase extends BaseEntity {

    /**
     * 购买用户ID
     */
    private Long userId;

    /**
     * 购买的小鹅ID
     */
    private Long gooseId;

    /**
     * 关联商品ID（可为空）
     */
    private Long productId;

    /**
     * 购买价格
     */
    private BigDecimal purchasePrice;

    /**
     * 购买时间
     */
    private LocalDateTime purchaseTime;

    /**
     * 默认构造函数
     */
    public GoosePurchase() {
        this.purchaseTime = LocalDateTime.now();
    }

    /**
     * 构造函数
     * 
     * @param userId 用户ID
     * @param gooseId 小鹅ID
     * @param purchasePrice 购买价格
     */
    public GoosePurchase(Long userId, Long gooseId, BigDecimal purchasePrice) {
        this.userId = userId;
        this.gooseId = gooseId;
        this.purchasePrice = purchasePrice;
        this.purchaseTime = LocalDateTime.now();
        this.status = 1;
    }

    /**
     * 构造函数（包含商品ID）
     * 
     * @param userId 用户ID
     * @param gooseId 小鹅ID
     * @param productId 商品ID
     * @param purchasePrice 购买价格
     */
    public GoosePurchase(Long userId, Long gooseId, Long productId, BigDecimal purchasePrice) {
        this.userId = userId;
        this.gooseId = gooseId;
        this.productId = productId;
        this.purchasePrice = purchasePrice;
        this.purchaseTime = LocalDateTime.now();
        this.status = 1;
    }

    /**
     * 购买状态枚举
     */
    public enum PurchaseStatus {
        FAILED(0, "失败"),
        SUCCESS(1, "成功");

        private final Integer code;
        private final String description;

        PurchaseStatus(Integer code, String description) {
            this.code = code;
            this.description = description;
        }

        public Integer getCode() {
            return code;
        }

        public String getDescription() {
            return description;
        }

        public static PurchaseStatus fromCode(Integer code) {
            for (PurchaseStatus status : values()) {
                if (status.code.equals(code)) {
                    return status;
                }
            }
            return FAILED;
        }
    }

    /**
     * 检查购买是否成功
     */
    public boolean isSuccess() {
        return PurchaseStatus.SUCCESS.getCode().equals(this.status);
    }

    /**
     * 检查购买是否失败
     */
    public boolean isFailed() {
        return PurchaseStatus.FAILED.getCode().equals(this.status);
    }

    /**
     * 设置购买成功
     */
    public void setSuccess() {
        this.status = PurchaseStatus.SUCCESS.getCode();
    }

    /**
     * 设置购买失败
     */
    public void setFailed() {
        this.status = PurchaseStatus.FAILED.getCode();
    }

    /**
     * 获取购买状态描述
     */
    public String getStatusDescription() {
        return PurchaseStatus.fromCode(this.status).getDescription();
    }

    @Override
    public String toString() {
        return String.format("GoosePurchase{id=%d, userId=%d, gooseId=%d, price=%s, status=%s, purchaseTime=%s}", 
                           id, userId, gooseId, purchasePrice, getStatusDescription(), purchaseTime);
    }
}
