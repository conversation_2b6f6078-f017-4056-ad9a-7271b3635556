package com.gooseapp.shop.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gooseapp.shop.entity.GoosePurchase;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Delete;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * 小鹅购买记录数据访问层 - MyBatis Plus版本
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-12-19
 */
@Mapper
public interface GoosePurchaseMapper extends BaseMapper<GoosePurchase> {

    /**
     * 根据用户ID查找购买记录
     */
    @Select("SELECT * FROM goose_purchases WHERE user_id = #{userId} AND is_deleted = 0 ORDER BY purchase_time DESC")
    IPage<GoosePurchase> findByUserId(@Param("userId") Long userId, Page<GoosePurchase> page);

    /**
     * 根据用户ID查找购买记录列表
     */
    @Select("SELECT * FROM goose_purchases WHERE user_id = #{userId} AND is_deleted = 0 ORDER BY purchase_time DESC")
    List<GoosePurchase> findByUserId(@Param("userId") Long userId);

    /**
     * 根据小鹅ID查找购买记录
     */
    @Select("SELECT * FROM goose_purchases WHERE goose_id = #{gooseId} AND is_deleted = 0")
    Optional<GoosePurchase> findByGooseId(@Param("gooseId") Long gooseId);

    /**
     * 根据用户ID和小鹅ID查找购买记录
     */
    @Select("SELECT * FROM goose_purchases WHERE user_id = #{userId} AND goose_id = #{gooseId} AND is_deleted = 0")
    Optional<GoosePurchase> findByUserIdAndGooseId(@Param("userId") Long userId, @Param("gooseId") Long gooseId);

    /**
     * 根据状态查找购买记录
     */
    @Select("SELECT * FROM goose_purchases WHERE status = #{status} AND is_deleted = 0 ORDER BY purchase_time DESC")
    List<GoosePurchase> findByStatus(@Param("status") Integer status);

    /**
     * 分页查询所有购买记录
     */
    @Select("SELECT * FROM goose_purchases WHERE is_deleted = 0 ORDER BY purchase_time DESC")
    IPage<GoosePurchase> findAll(Page<GoosePurchase> page);

    /**
     * 根据状态分页查询购买记录
     */
    @Select("SELECT * FROM goose_purchases WHERE status = #{status} AND is_deleted = 0 ORDER BY purchase_time DESC")
    IPage<GoosePurchase> findByStatus(@Param("status") Integer status, Page<GoosePurchase> page);

    /**
     * 根据用户ID和状态查找购买记录
     */
    @Select("SELECT * FROM goose_purchases WHERE user_id = #{userId} AND status = #{status} AND is_deleted = 0 ORDER BY purchase_time DESC")
    List<GoosePurchase> findByUserIdAndStatus(@Param("userId") Long userId, @Param("status") Integer status);

    /**
     * 根据时间范围查找购买记录
     */
    @Select("SELECT * FROM goose_purchases WHERE purchase_time BETWEEN #{startTime} AND #{endTime} AND is_deleted = 0 ORDER BY purchase_time DESC")
    List<GoosePurchase> findByPurchaseTimeBetween(@Param("startTime") LocalDateTime startTime, 
                                                 @Param("endTime") LocalDateTime endTime);

    /**
     * 根据用户ID和时间范围查找购买记录
     */
    @Select("SELECT * FROM goose_purchases WHERE user_id = #{userId} AND purchase_time BETWEEN #{startTime} AND #{endTime} AND is_deleted = 0 ORDER BY purchase_time DESC")
    List<GoosePurchase> findByUserIdAndPurchaseTimeBetween(@Param("userId") Long userId, 
                                                          @Param("startTime") LocalDateTime startTime, 
                                                          @Param("endTime") LocalDateTime endTime);

    /**
     * 统计用户购买次数
     */
    @Select("SELECT COUNT(*) FROM goose_purchases WHERE user_id = #{userId} AND is_deleted = 0")
    long countByUserId(@Param("userId") Long userId);

    /**
     * 统计用户成功购买次数
     */
    @Select("SELECT COUNT(*) FROM goose_purchases WHERE user_id = #{userId} AND status = #{status} AND is_deleted = 0")
    long countByUserIdAndStatus(@Param("userId") Long userId, @Param("status") Integer status);

    /**
     * 计算用户总消费金额
     */
    @Select("SELECT COALESCE(SUM(purchase_price), 0) FROM goose_purchases WHERE user_id = #{userId} AND status = 1 AND is_deleted = 0")
    BigDecimal sumPurchasePriceByUserId(@Param("userId") Long userId);

    /**
     * 计算用户指定状态的消费金额
     */
    @Select("SELECT COALESCE(SUM(purchase_price), 0) FROM goose_purchases WHERE user_id = #{userId} AND status = #{status} AND is_deleted = 0")
    BigDecimal sumPurchasePriceByUserIdAndStatus(@Param("userId") Long userId, @Param("status") int status);

    /**
     * 计算指定时间范围内的总销售额
     */
    @Select("SELECT COALESCE(SUM(purchase_price), 0) FROM goose_purchases WHERE status = 1 AND purchase_time BETWEEN #{startTime} AND #{endTime} AND is_deleted = 0")
    BigDecimal sumPurchasePriceByTimeBetween(@Param("startTime") LocalDateTime startTime, 
                                            @Param("endTime") LocalDateTime endTime);

    /**
     * 统计指定时间范围内的购买次数
     */
    @Select("SELECT COUNT(*) FROM goose_purchases WHERE status = 1 AND purchase_time BETWEEN #{startTime} AND #{endTime} AND is_deleted = 0")
    long countByStatusAndPurchaseTimeBetween(@Param("startTime") LocalDateTime startTime, 
                                            @Param("endTime") LocalDateTime endTime);

    /**
     * 查找用户最近的购买记录
     */
    @Select("SELECT * FROM goose_purchases WHERE user_id = #{userId} AND is_deleted = 0 ORDER BY purchase_time DESC LIMIT #{limit}")
    List<GoosePurchase> findRecentPurchasesByUserId(@Param("userId") Long userId, @Param("limit") int limit);

    /**
     * 查找最近的购买记录
     */
    @Select("SELECT * FROM goose_purchases WHERE is_deleted = 0 ORDER BY purchase_time DESC LIMIT #{limit}")
    List<GoosePurchase> findRecentPurchases(@Param("limit") int limit);

    /**
     * 查找今日购买记录
     */
    @Select("SELECT * FROM goose_purchases WHERE DATE(purchase_time) = CURDATE() AND is_deleted = 0 ORDER BY purchase_time DESC")
    List<GoosePurchase> findTodayPurchases();

    /**
     * 统计今日购买次数
     */
    @Select("SELECT COUNT(*) FROM goose_purchases WHERE DATE(purchase_time) = CURDATE() AND status = 1 AND is_deleted = 0")
    long countTodayPurchases();

    /**
     * 统计今日销售额
     */
    @Select("SELECT COALESCE(SUM(purchase_price), 0) FROM goose_purchases WHERE DATE(purchase_time) = CURDATE() AND status = 1 AND is_deleted = 0")
    BigDecimal sumTodayPurchases();

    /**
     * 查找用户今日购买记录
     */
    @Select("SELECT * FROM goose_purchases WHERE user_id = #{userId} AND DATE(purchase_time) = CURDATE() AND is_deleted = 0 ORDER BY purchase_time DESC")
    List<GoosePurchase> findTodayPurchasesByUserId(@Param("userId") Long userId);

    /**
     * 统计用户今日购买次数
     */
    @Select("SELECT COUNT(*) FROM goose_purchases WHERE user_id = #{userId} AND DATE(purchase_time) = CURDATE() AND is_deleted = 0")
    long countTodayPurchasesByUserId(@Param("userId") Long userId);

    /**
     * 获取购买统计信息
     */
    @Select("SELECT DATE(purchase_time) as date, COUNT(*) as count, SUM(purchase_price) as total FROM goose_purchases " +
            "WHERE status = 1 AND purchase_time BETWEEN #{startTime} AND #{endTime} AND is_deleted = 0 " +
            "GROUP BY DATE(purchase_time) ORDER BY DATE(purchase_time)")
    List<Object[]> getPurchaseStatistics(@Param("startTime") LocalDateTime startTime, 
                                        @Param("endTime") LocalDateTime endTime);

    /**
     * 检查小鹅是否已被购买
     */
    @Select("SELECT COUNT(*) > 0 FROM goose_purchases WHERE goose_id = #{gooseId} AND status = #{status} AND is_deleted = 0")
    boolean existsByGooseIdAndStatus(@Param("gooseId") Long gooseId, @Param("status") Integer status);

    /**
     * 删除用户的所有购买记录
     */
    @Delete("UPDATE goose_purchases SET is_deleted = 1 WHERE user_id = #{userId}")
    void deleteByUserId(@Param("userId") Long userId);

    /**
     * 根据商品ID查找购买记录
     */
    @Select("SELECT * FROM goose_purchases WHERE product_id = #{productId} AND is_deleted = 0 ORDER BY purchase_time DESC")
    IPage<GoosePurchase> findByProductId(@Param("productId") Long productId, Page<GoosePurchase> page);

    /**
     * 统计商品销售次数
     */
    @Select("SELECT COUNT(*) FROM goose_purchases WHERE product_id = #{productId} AND status = #{status} AND is_deleted = 0")
    long countByProductIdAndStatus(@Param("productId") Long productId, @Param("status") Integer status);

    /**
     * 查找价格范围内的购买记录
     */
    @Select("SELECT * FROM goose_purchases WHERE purchase_price BETWEEN #{minPrice} AND #{maxPrice} AND is_deleted = 0 ORDER BY purchase_time DESC")
    List<GoosePurchase> findByPurchasePriceBetween(@Param("minPrice") BigDecimal minPrice, 
                                                  @Param("maxPrice") BigDecimal maxPrice);

    /**
     * 查找用户价格范围内的购买记录
     */
    @Select("SELECT * FROM goose_purchases WHERE user_id = #{userId} AND purchase_price BETWEEN #{minPrice} AND #{maxPrice} AND is_deleted = 0 ORDER BY purchase_time DESC")
    List<GoosePurchase> findByUserIdAndPurchasePriceBetween(@Param("userId") Long userId, 
                                                           @Param("minPrice") BigDecimal minPrice, 
                                                           @Param("maxPrice") BigDecimal maxPrice);

    /**
     * 获取用户购买统计信息
     */
    @Select("SELECT status, COUNT(*) as count, COALESCE(SUM(purchase_price), 0) as total FROM goose_purchases " +
            "WHERE user_id = #{userId} AND is_deleted = 0 GROUP BY status")
    List<Object[]> getUserPurchaseStatistics(@Param("userId") Long userId);

    /**
     * 查找失败的购买记录
     */
    @Select("SELECT * FROM goose_purchases WHERE status = 0 AND is_deleted = 0 ORDER BY purchase_time DESC")
    List<GoosePurchase> findFailedPurchases();

    /**
     * 查找用户失败的购买记录
     */
    @Select("SELECT * FROM goose_purchases WHERE user_id = #{userId} AND status = 0 AND is_deleted = 0 ORDER BY purchase_time DESC")
    List<GoosePurchase> findFailedPurchasesByUserId(@Param("userId") Long userId);

    /**
     * 获取热门商品统计（根据购买次数）
     */
    @Select("SELECT product_id, COUNT(*) as purchase_count FROM goose_purchases " +
            "WHERE product_id IS NOT NULL AND status = 1 AND is_deleted = 0 " +
            "GROUP BY product_id ORDER BY purchase_count DESC LIMIT #{limit}")
    List<Object[]> getPopularProducts(@Param("limit") int limit);

    /**
     * 获取用户购买偏好统计
     */
    @Select("SELECT product_id, COUNT(*) as purchase_count FROM goose_purchases " +
            "WHERE user_id = #{userId} AND product_id IS NOT NULL AND status = 1 AND is_deleted = 0 " +
            "GROUP BY product_id ORDER BY purchase_count DESC")
    List<Object[]> getUserPurchasePreferences(@Param("userId") Long userId);
}
