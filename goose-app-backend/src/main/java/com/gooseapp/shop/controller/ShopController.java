package com.gooseapp.shop.controller;

import cn.dev33.satoken.stp.StpUtil;
import com.gooseapp.common.response.ApiResponse;
import com.gooseapp.goose.dto.GooseDTO;
import com.gooseapp.goose.service.GooseService;
import com.gooseapp.shop.dto.GoosePurchaseDTO;
import com.gooseapp.shop.dto.PurchaseGooseRequest;
import com.gooseapp.shop.dto.PurchaseResultDTO;
import com.gooseapp.shop.service.PurchaseService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 商城控制器
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-12-19
 */
@Slf4j
@RestController
@RequestMapping("/api/v1/shop")
@RequiredArgsConstructor
@Tag(name = "商城管理", description = "小鹅商城相关接口")
public class ShopController {

    private final PurchaseService purchaseService;
    private final GooseService gooseService;

    @GetMapping("/geese/available")
    @Operation(summary = "查看待售小鹅", description = "获取商城中可购买的小鹅列表")
    public ApiResponse<Page<GooseDTO>> getAvailableGeese(
            @Parameter(description = "页码，从0开始") @RequestParam(defaultValue = "0") int page,
            @Parameter(description = "每页大小") @RequestParam(defaultValue = "20") int size) {
        log.debug("查看待售小鹅: page={}, size={}", page, size);
        
        Pageable pageable = PageRequest.of(page, size);
        Page<GooseDTO> geesePage = gooseService.getAvailableGeese(pageable);
        
        return ApiResponse.success("查询成功", geesePage);
    }

    @GetMapping("/geese/available/breed/{breed}")
    @Operation(summary = "按品种查看待售小鹅", description = "根据品种筛选可购买的小鹅")
    public ApiResponse<List<GooseDTO>> getAvailableGeeseByBreed(
            @Parameter(description = "品种") @PathVariable String breed) {
        log.debug("按品种查看待售小鹅: breed={}", breed);
        
        List<GooseDTO> geese = gooseService.getAvailableGeeseByBreed(breed);
        
        return ApiResponse.success("查询成功", geese);
    }

    @GetMapping("/geese/{gooseId}")
    @Operation(summary = "查看小鹅详情", description = "获取指定小鹅的详细信息")
    public ApiResponse<GooseDTO> getGooseDetail(
            @Parameter(description = "小鹅ID") @PathVariable Long gooseId) {
        log.debug("查看小鹅详情: gooseId={}", gooseId);
        
        GooseDTO goose = gooseService.getGooseById(gooseId);
        
        return ApiResponse.success("查询成功", goose);
    }

    @GetMapping("/geese/{gooseId}/available")
    @Operation(summary = "检查小鹅是否可购买", description = "检查指定小鹅是否可以购买")
    public ApiResponse<Boolean> checkGooseAvailable(
            @Parameter(description = "小鹅ID") @PathVariable Long gooseId) {
        log.debug("检查小鹅是否可购买: gooseId={}", gooseId);
        
        boolean available = purchaseService.isGooseAvailableForPurchase(gooseId);
        
        return ApiResponse.success("查询成功", available);
    }

    @PostMapping("/purchase")
    @Operation(summary = "购买小鹅", description = "用户购买指定的小鹅")
    public ApiResponse<PurchaseResultDTO> purchaseGoose(
            @Valid @RequestBody PurchaseGooseRequest request) {
        log.info("用户购买小鹅: gooseId={}, price={}", request.getGooseId(), request.getPurchasePrice());
        
        // 获取当前登录用户ID
        Long userId = StpUtil.getLoginIdAsLong();
        
        PurchaseResultDTO result = purchaseService.purchaseGoose(userId, request);
        
        return ApiResponse.success("购买成功", result);
    }

    @GetMapping("/purchases/my")
    @Operation(summary = "我的购买记录", description = "获取当前用户的购买记录")
    public ApiResponse<Page<GoosePurchaseDTO>> getMyPurchases(
            @Parameter(description = "页码，从0开始") @RequestParam(defaultValue = "0") int page,
            @Parameter(description = "每页大小") @RequestParam(defaultValue = "20") int size) {
        log.debug("查看我的购买记录: page={}, size={}", page, size);
        
        // 获取当前登录用户ID
        Long userId = StpUtil.getLoginIdAsLong();
        
        Pageable pageable = PageRequest.of(page, size);
        Page<GoosePurchaseDTO> purchasePage = purchaseService.getUserPurchaseHistory(userId, pageable);
        
        return ApiResponse.success("查询成功", purchasePage);
    }

    @GetMapping("/purchases/{purchaseId}")
    @Operation(summary = "查看购买记录详情", description = "获取指定购买记录的详细信息")
    public ApiResponse<GoosePurchaseDTO> getPurchaseDetail(
            @Parameter(description = "购买记录ID") @PathVariable Long purchaseId) {
        log.debug("查看购买记录详情: purchaseId={}", purchaseId);
        
        GoosePurchaseDTO purchase = purchaseService.getPurchaseById(purchaseId);
        
        return ApiResponse.success("查询成功", purchase);
    }

    @GetMapping("/purchases/statistics/my")
    @Operation(summary = "我的购买统计", description = "获取当前用户的购买统计信息")
    public ApiResponse<PurchaseService.UserPurchaseStatistics> getMyPurchaseStatistics() {
        log.debug("查看我的购买统计");
        
        // 获取当前登录用户ID
        Long userId = StpUtil.getLoginIdAsLong();
        
        PurchaseService.UserPurchaseStatistics statistics = purchaseService.getUserPurchaseStatistics(userId);
        
        return ApiResponse.success("查询成功", statistics);
    }

    @GetMapping("/geese/breeds")
    @Operation(summary = "获取小鹅品种列表", description = "获取所有可用的小鹅品种")
    public ApiResponse<List<String>> getGooseBreeds() {
        log.debug("获取小鹅品种列表");
        
        List<String> breeds = gooseService.getAllBreeds();
        
        return ApiResponse.success("查询成功", breeds);
    }

    @GetMapping("/geese/search")
    @Operation(summary = "搜索小鹅", description = "根据关键词搜索可购买的小鹅")
    public ApiResponse<Page<GooseDTO>> searchGeese(
            @Parameter(description = "搜索关键词") @RequestParam String keyword,
            @Parameter(description = "页码，从0开始") @RequestParam(defaultValue = "0") int page,
            @Parameter(description = "每页大小") @RequestParam(defaultValue = "20") int size) {
        log.debug("搜索小鹅: keyword={}, page={}, size={}", keyword, page, size);
        
        Pageable pageable = PageRequest.of(page, size);
        Page<GooseDTO> geesePage = gooseService.searchAvailableGeese(keyword, pageable);
        
        return ApiResponse.success("搜索成功", geesePage);
    }
}
