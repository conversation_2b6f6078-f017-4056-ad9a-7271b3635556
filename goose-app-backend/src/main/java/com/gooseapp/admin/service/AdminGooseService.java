package com.gooseapp.admin.service;

import com.gooseapp.admin.dto.AdminGooseCreateRequest;
import com.gooseapp.admin.dto.BatchCreateGooseRequest;
import com.gooseapp.goose.dto.GooseDTO;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.List;

/**
 * 管理员小鹅服务接口
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-12-19
 */
public interface AdminGooseService {

    /**
     * 创建小鹅到商品池
     * 
     * @param request 创建请求
     * @return 创建的小鹅信息
     */
    GooseDTO createGooseForSale(AdminGooseCreateRequest request);

    /**
     * 批量创建小鹅
     * 
     * @param request 批量创建请求
     * @return 创建结果列表
     */
    List<GooseDTO> batchCreateGeese(BatchCreateGooseRequest request);

    /**
     * 查看所有小鹅（包括待售和已归属）
     * 
     * @param pageable 分页参数
     * @return 小鹅分页列表
     */
    Page<GooseDTO> getAllGeese(Pageable pageable);

    /**
     * 查看待售小鹅
     * 
     * @param pageable 分页参数
     * @return 待售小鹅分页列表
     */
    Page<GooseDTO> getAvailableGeese(Pageable pageable);

    /**
     * 查看已归属小鹅
     * 
     * @param pageable 分页参数
     * @return 已归属小鹅分页列表
     */
    Page<GooseDTO> getOwnedGeese(Pageable pageable);

    /**
     * 根据用户ID查看用户的小鹅
     * 
     * @param userId 用户ID
     * @param pageable 分页参数
     * @return 用户小鹅分页列表
     */
    Page<GooseDTO> getGeeseByUserId(Long userId, Pageable pageable);

    /**
     * 根据品种查看小鹅
     * 
     * @param breed 品种
     * @param pageable 分页参数
     * @return 小鹅分页列表
     */
    Page<GooseDTO> getGeeseByBreed(String breed, Pageable pageable);

    /**
     * 搜索小鹅
     * 
     * @param keyword 关键词
     * @param pageable 分页参数
     * @return 小鹅分页列表
     */
    Page<GooseDTO> searchGeese(String keyword, Pageable pageable);

    /**
     * 更新小鹅状态
     * 
     * @param gooseId 小鹅ID
     * @param status 新状态
     * @return 更新后的小鹅信息
     */
    GooseDTO updateGooseStatus(Long gooseId, Integer status);

    /**
     * 更新小鹅归属状态
     * 
     * @param gooseId 小鹅ID
     * @param ownershipStatus 归属状态
     * @return 更新后的小鹅信息
     */
    GooseDTO updateGooseOwnershipStatus(Long gooseId, Integer ownershipStatus);

    /**
     * 强制设置小鹅归属
     * 
     * @param gooseId 小鹅ID
     * @param userId 用户ID（null表示设为待售）
     * @return 更新后的小鹅信息
     */
    GooseDTO setGooseOwnership(Long gooseId, Long userId);

    /**
     * 删除小鹅（软删除）
     * 
     * @param gooseId 小鹅ID
     */
    void deleteGoose(Long gooseId);

    /**
     * 批量删除小鹅
     * 
     * @param gooseIds 小鹅ID列表
     */
    void batchDeleteGeese(List<Long> gooseIds);

    /**
     * 获取小鹅统计信息
     * 
     * @return 统计信息
     */
    AdminGooseStatistics getGooseStatistics();

    /**
     * 管理员小鹅统计信息
     */
    class AdminGooseStatistics {
        private Long totalGeese;
        private Long availableGeese;
        private Long ownedGeese;
        private Long sickGeese;
        private Long giftedGeese;

        // 构造函数
        public AdminGooseStatistics(Long totalGeese, Long availableGeese, Long ownedGeese, 
                                   Long sickGeese, Long giftedGeese) {
            this.totalGeese = totalGeese;
            this.availableGeese = availableGeese;
            this.ownedGeese = ownedGeese;
            this.sickGeese = sickGeese;
            this.giftedGeese = giftedGeese;
        }

        // Getters
        public Long getTotalGeese() { return totalGeese; }
        public Long getAvailableGeese() { return availableGeese; }
        public Long getOwnedGeese() { return ownedGeese; }
        public Long getSickGeese() { return sickGeese; }
        public Long getGiftedGeese() { return giftedGeese; }

        // Setters
        public void setTotalGeese(Long totalGeese) { this.totalGeese = totalGeese; }
        public void setAvailableGeese(Long availableGeese) { this.availableGeese = availableGeese; }
        public void setOwnedGeese(Long ownedGeese) { this.ownedGeese = ownedGeese; }
        public void setSickGeese(Long sickGeese) { this.sickGeese = sickGeese; }
        public void setGiftedGeese(Long giftedGeese) { this.giftedGeese = giftedGeese; }
    }
}
