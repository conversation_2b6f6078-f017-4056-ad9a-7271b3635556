package com.gooseapp.admin.service.impl;

import com.gooseapp.admin.dto.AdminGooseCreateRequest;
import com.gooseapp.admin.dto.BatchCreateGooseRequest;
import com.gooseapp.admin.service.AdminGooseService;
import com.gooseapp.common.constant.ErrorCode;
import com.gooseapp.common.exception.BusinessException;
import com.gooseapp.goose.dto.GooseDTO;
import com.gooseapp.goose.entity.Goose;
import com.gooseapp.goose.mapper.GooseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 管理员小鹅服务实现类
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-12-19
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AdminGooseServiceImpl implements AdminGooseService {

    private final GooseMapper gooseMapper;

    @Override
    @Transactional
    public GooseDTO createGooseForSale(AdminGooseCreateRequest request) {
        log.debug("管理员创建小鹅到商品池: {}", request);

        // 验证请求参数
        request.validate();

        // 创建小鹅实体
        Goose goose = new Goose();
        goose.setName(request.getName());
        goose.setBreed(request.getBreed());
        goose.setGender(request.getGender());
        goose.setHealth(request.getInitialHealth());
        goose.setHunger(request.getInitialHunger());
        goose.setThirst(request.getInitialThirst());
        goose.setCleanliness(request.getInitialCleanliness());
        goose.setHappiness(request.getInitialHappiness());
        goose.setLevel(request.getInitialLevel());
        goose.setExperience(request.getInitialExperience());
        goose.setImageUrl(request.getImageUrl());
        goose.setVideoUrl(request.getVideoUrl());

        // 设置为待售状态（关键：不设置用户ID，归属状态为0）
        goose.setAvailable();

        // 保存小鹅
        gooseMapper.insert(goose);

        log.info("成功创建小鹅到商品池: ID={}, 名称={}, 品种={}",
                goose.getId(), goose.getName(), goose.getBreed());

        return convertToDTO(goose);
    }

    @Override
    @Transactional
    public List<GooseDTO> batchCreateGeese(BatchCreateGooseRequest request) {
        log.debug("批量创建小鹅: 数量={}", request.getBatchSize());

        // 验证请求参数
        request.validate();

        List<GooseDTO> results = new ArrayList<>();
        List<String> errors = new ArrayList<>();

        for (int i = 0; i < request.getGeese().size(); i++) {
            AdminGooseCreateRequest gooseRequest = request.getGeese().get(i);
            try {
                GooseDTO createdGoose = createGooseForSale(gooseRequest);
                results.add(createdGoose);
                log.debug("批量创建第{}只小鹅成功: {}", i + 1, createdGoose.getName());
            } catch (Exception e) {
                String error = String.format("第%d只小鹅创建失败: %s", i + 1, e.getMessage());
                errors.add(error);
                log.warn(error, e);

                if (!request.getIgnoreErrors()) {
                    throw new BusinessException(ErrorCode.BUSINESS_RULE_VIOLATION,
                                              "批量创建失败: " + error);
                }
            }
        }

        log.info("批量创建小鹅完成: 成功{}只, 失败{}只", results.size(), errors.size());

        if (!errors.isEmpty() && results.isEmpty()) {
            throw new BusinessException(ErrorCode.BUSINESS_RULE_VIOLATION,
                                      "批量创建全部失败: " + String.join("; ", errors));
        }

        return results;
    }

    @Override
    public org.springframework.data.domain.Page<GooseDTO> getAllGeese(Pageable pageable) {
        Page<Goose> mybatisPage = new Page<>(pageable.getPageNumber() + 1, pageable.getPageSize());
        IPage<Goose> geesePage = gooseMapper.selectPage(mybatisPage, null);
        
        List<GooseDTO> dtoList = geesePage.getRecords().stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());
        
        return new PageImpl<>(dtoList, pageable, geesePage.getTotal());
    }

    @Override
    public org.springframework.data.domain.Page<GooseDTO> getAvailableGeese(Pageable pageable) {
        Page<Goose> mybatisPage = new Page<>(pageable.getPageNumber() + 1, pageable.getPageSize());
        QueryWrapper<Goose> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("ownership_status", 0);
        IPage<Goose> geesePage = gooseMapper.selectPage(mybatisPage, queryWrapper);
        
        List<GooseDTO> dtoList = geesePage.getRecords().stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());
        
        return new PageImpl<>(dtoList, pageable, geesePage.getTotal());
    }

    @Override
    public org.springframework.data.domain.Page<GooseDTO> getOwnedGeese(Pageable pageable) {
        Page<Goose> mybatisPage = new Page<>(pageable.getPageNumber() + 1, pageable.getPageSize());
        QueryWrapper<Goose> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("ownership_status", 1);
        IPage<Goose> geesePage = gooseMapper.selectPage(mybatisPage, queryWrapper);
        
        List<GooseDTO> dtoList = geesePage.getRecords().stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());
        
        return new PageImpl<>(dtoList, pageable, geesePage.getTotal());
    }

    @Override
    public org.springframework.data.domain.Page<GooseDTO> getGeeseByUserId(Long userId, Pageable pageable) {
        Page<Goose> mybatisPage = new Page<>(pageable.getPageNumber() + 1, pageable.getPageSize());
        QueryWrapper<Goose> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("user_id", userId);
        IPage<Goose> geesePage = gooseMapper.selectPage(mybatisPage, queryWrapper);
        
        List<GooseDTO> dtoList = geesePage.getRecords().stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());
        
        return new PageImpl<>(dtoList, pageable, geesePage.getTotal());
    }

    @Override
    public org.springframework.data.domain.Page<GooseDTO> getGeeseByBreed(String breed, Pageable pageable) {
        Page<Goose> mybatisPage = new Page<>(pageable.getPageNumber() + 1, pageable.getPageSize());
        QueryWrapper<Goose> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("breed", breed);
        IPage<Goose> geesePage = gooseMapper.selectPage(mybatisPage, queryWrapper);
        
        List<GooseDTO> dtoList = geesePage.getRecords().stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());
        
        return new PageImpl<>(dtoList, pageable, geesePage.getTotal());
    }

    @Override
    public org.springframework.data.domain.Page<GooseDTO> searchGeese(String keyword, Pageable pageable) {
        Page<Goose> mybatisPage = new Page<>(pageable.getPageNumber() + 1, pageable.getPageSize());
        QueryWrapper<Goose> queryWrapper = new QueryWrapper<>();
        queryWrapper.like("name", keyword);
        IPage<Goose> geesePage = gooseMapper.selectPage(mybatisPage, queryWrapper);
        
        List<GooseDTO> dtoList = geesePage.getRecords().stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());
        
        return new PageImpl<>(dtoList, pageable, geesePage.getTotal());
    }

    @Override
    @Transactional
    public GooseDTO updateGooseStatus(Long gooseId, Integer status) {
        log.debug("更新小鹅状态: ID={}, 新状态={}", gooseId, status);

        Goose goose = Optional.ofNullable(gooseMapper.selectById(gooseId))
                .orElseThrow(() -> new BusinessException(ErrorCode.GOOSE_NOT_FOUND, "小鹅不存在"));

        goose.setStatus(status);
        gooseMapper.updateById(goose);

        log.info("成功更新小鹅状态: ID={}, 状态={}", gooseId, status);
        return convertToDTO(goose);
    }

    @Override
    @Transactional
    public GooseDTO updateGooseOwnershipStatus(Long gooseId, Integer ownershipStatus) {
        log.debug("更新小鹅归属状态: ID={}, 新归属状态={}", gooseId, ownershipStatus);

        Goose goose = Optional.ofNullable(gooseMapper.selectById(gooseId))
                .orElseThrow(() -> new BusinessException(ErrorCode.GOOSE_NOT_FOUND, "小鹅不存在"));

        goose.setOwnershipStatus(ownershipStatus);

        // 如果设为待售状态，清除用户归属
        if (ownershipStatus == 0) {
            goose.setUserId(null);
            goose.setPurchaseTime(null);
        }

        gooseMapper.updateById(goose);

        log.info("成功更新小鹅归属状态: ID={}, 归属状态={}", gooseId, ownershipStatus);
        return convertToDTO(goose);
    }

    @Override
    @Transactional
    public GooseDTO setGooseOwnership(Long gooseId, Long userId) {
        log.debug("设置小鹅归属: 小鹅ID={}, 用户ID={}", gooseId, userId);

        Goose goose = Optional.ofNullable(gooseMapper.selectById(gooseId))
                .orElseThrow(() -> new BusinessException(ErrorCode.GOOSE_NOT_FOUND, "小鹅不存在"));

        if (userId == null) {
            // 设为待售状态
            goose.setAvailable();
        } else {
            // 设为已归属状态
            goose.setOwned(userId);
        }

        gooseMapper.updateById(goose);

        log.info("成功设置小鹅归属: 小鹅ID={}, 用户ID={}, 归属状态={}",
                gooseId, userId, goose.getOwnershipStatus());
        return convertToDTO(goose);
    }

    @Override
    @Transactional
    public void deleteGoose(Long gooseId) {
        log.debug("删除小鹅: ID={}", gooseId);

        Goose goose = Optional.ofNullable(gooseMapper.selectById(gooseId))
                .orElseThrow(() -> new BusinessException(ErrorCode.GOOSE_NOT_FOUND, "小鹅不存在"));

        // 检查小鹅是否已归属
        if (goose.isOwned()) {
            throw new BusinessException(ErrorCode.OPERATION_NOT_ALLOWED, "已归属的小鹅不能删除");
        }

        gooseMapper.deleteById(gooseId);
        log.info("成功删除小鹅: ID={}", gooseId);
    }

    @Override
    @Transactional
    public void batchDeleteGeese(List<Long> gooseIds) {
        log.debug("批量删除小鹅: 数量={}", gooseIds.size());

        List<String> errors = new ArrayList<>();
        int successCount = 0;

        for (Long gooseId : gooseIds) {
            try {
                deleteGoose(gooseId);
                successCount++;
            } catch (Exception e) {
                errors.add(String.format("删除小鹅%d失败: %s", gooseId, e.getMessage()));
                log.warn("删除小鹅{}失败", gooseId, e);
            }
        }

        log.info("批量删除小鹅完成: 成功{}只, 失败{}只", successCount, errors.size());

        if (!errors.isEmpty()) {
            throw new BusinessException(ErrorCode.BUSINESS_RULE_VIOLATION,
                                      "部分删除失败: " + String.join("; ", errors));
        }
    }

    @Override
    public AdminGooseStatistics getGooseStatistics() {
        QueryWrapper<Goose> totalWrapper = new QueryWrapper<>();
        Long totalGeese = gooseMapper.selectCount(totalWrapper);
        
        QueryWrapper<Goose> availableWrapper = new QueryWrapper<>();
        availableWrapper.eq("ownership_status", 0);
        Long availableGeese = gooseMapper.selectCount(availableWrapper);
        
        QueryWrapper<Goose> ownedWrapper = new QueryWrapper<>();
        ownedWrapper.eq("ownership_status", 1);
        Long ownedGeese = gooseMapper.selectCount(ownedWrapper);
        
        QueryWrapper<Goose> sickWrapper = new QueryWrapper<>();
        sickWrapper.eq("status", 2);
        Long sickGeese = gooseMapper.selectCount(sickWrapper);
        
        QueryWrapper<Goose> giftedWrapper = new QueryWrapper<>();
        giftedWrapper.eq("status", 0);
        Long giftedGeese = gooseMapper.selectCount(giftedWrapper);

        return new AdminGooseStatistics(totalGeese, availableGeese, ownedGeese, sickGeese, giftedGeese);
    }

    /**
     * 转换实体为DTO
     */
    private GooseDTO convertToDTO(Goose goose) {
        if (goose == null) {
            return null;
        }

        GooseDTO dto = new GooseDTO();
        dto.setId(goose.getId());
        dto.setUserId(goose.getUserId());
        dto.setName(goose.getName());
        dto.setBreed(goose.getBreed());
        dto.setGender(goose.getGender());
        dto.setAge(goose.getAge());
        dto.setHealth(goose.getHealth());
        dto.setHunger(goose.getHunger());
        dto.setThirst(goose.getThirst());
        dto.setCleanliness(goose.getCleanliness());
        dto.setHappiness(goose.getHappiness());
        dto.setLevel(goose.getLevel());
        dto.setExperience(goose.getExperience());
        dto.setImageUrl(goose.getImageUrl());
        dto.setVideoUrl(goose.getVideoUrl());
        dto.setOwnershipStatus(goose.getOwnershipStatus());
        dto.setPurchaseTime(goose.getPurchaseTime());
        dto.setStatus(goose.getStatus());
        dto.setCreatedAt(goose.getCreatedAt());
        dto.setUpdatedAt(goose.getUpdatedAt());

        return dto;
    }
}
