package com.gooseapp.admin.controller;

import com.gooseapp.admin.dto.AdminGooseCreateRequest;
import com.gooseapp.admin.dto.BatchCreateGooseRequest;
import com.gooseapp.admin.service.AdminGooseService;
import com.gooseapp.common.annotation.AdminRequired;
import com.gooseapp.common.response.ApiResponse;
import com.gooseapp.goose.dto.GooseDTO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 管理员小鹅管理控制器
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-12-19
 */
@Slf4j
@RestController
@RequestMapping("/api/v1/admin/geese")
@RequiredArgsConstructor
@AdminRequired
@Tag(name = "管理员小鹅管理", description = "管理员专用的小鹅管理接口")
public class AdminGooseController {

    private final AdminGooseService adminGooseService;

    @PostMapping("/create")
    @Operation(summary = "创建小鹅到商品池", description = "管理员创建小鹅，小鹅将进入商品池等待用户购买")
    public ApiResponse<GooseDTO> createGooseForSale(
            @Valid @RequestBody AdminGooseCreateRequest request) {
        log.info("管理员创建小鹅到商品池: {}", request.getName());

        GooseDTO goose = adminGooseService.createGooseForSale(request);

        return ApiResponse.success("小鹅创建成功", goose);
    }

    @PostMapping("/batch-create")
    @Operation(summary = "批量创建小鹅", description = "管理员批量创建多只小鹅到商品池")
    public ApiResponse<List<GooseDTO>> batchCreateGeese(
            @Valid @RequestBody BatchCreateGooseRequest request) {
        log.info("管理员批量创建小鹅: 数量={}", request.getBatchSize());

        List<GooseDTO> geese = adminGooseService.batchCreateGeese(request);

        return ApiResponse.success(
                String.format("批量创建完成，成功创建%d只小鹅", geese.size()), geese);
    }

    @GetMapping("/all")
    @Operation(summary = "查看所有小鹅", description = "管理员查看所有小鹅，包括待售和已归属的")
    public ApiResponse<Page<GooseDTO>> getAllGeese(
            @Parameter(description = "页码，从0开始") @RequestParam(defaultValue = "0") int page,
            @Parameter(description = "每页大小") @RequestParam(defaultValue = "20") int size) {
        log.debug("管理员查看所有小鹅: page={}, size={}", page, size);

        Pageable pageable = PageRequest.of(page, size);
        Page<GooseDTO> geesePage = adminGooseService.getAllGeese(pageable);

        return ApiResponse.success("查询成功", geesePage);
    }

    @GetMapping("/available")
    @Operation(summary = "查看待售小鹅", description = "管理员查看商品池中待售的小鹅")
    public ApiResponse<Page<GooseDTO>> getAvailableGeese(
            @Parameter(description = "页码，从0开始") @RequestParam(defaultValue = "0") int page,
            @Parameter(description = "每页大小") @RequestParam(defaultValue = "20") int size) {
        log.debug("管理员查看待售小鹅: page={}, size={}", page, size);

        Pageable pageable = PageRequest.of(page, size);
        Page<GooseDTO> geesePage = adminGooseService.getAvailableGeese(pageable);

        return ApiResponse.success("查询成功", geesePage);
    }

    @GetMapping("/owned")
    @Operation(summary = "查看已归属小鹅", description = "管理员查看已被用户购买的小鹅")
    public ApiResponse<Page<GooseDTO>> getOwnedGeese(
            @Parameter(description = "页码，从0开始") @RequestParam(defaultValue = "0") int page,
            @Parameter(description = "每页大小") @RequestParam(defaultValue = "20") int size) {
        log.debug("管理员查看已归属小鹅: page={}, size={}", page, size);

        Pageable pageable = PageRequest.of(page, size);
        Page<GooseDTO> geesePage = adminGooseService.getOwnedGeese(pageable);

        return ApiResponse.success("查询成功", geesePage);
    }

    @GetMapping("/user/{userId}")
    @Operation(summary = "查看用户的小鹅", description = "管理员查看指定用户的所有小鹅")
    public ApiResponse<Page<GooseDTO>> getGeeseByUserId(
            @Parameter(description = "用户ID") @PathVariable Long userId,
            @Parameter(description = "页码，从0开始") @RequestParam(defaultValue = "0") int page,
            @Parameter(description = "每页大小") @RequestParam(defaultValue = "20") int size) {
        log.debug("管理员查看用户{}的小鹅: page={}, size={}", userId, page, size);

        Pageable pageable = PageRequest.of(page, size);
        Page<GooseDTO> geesePage = adminGooseService.getGeeseByUserId(userId, pageable);

        return ApiResponse.success("查询成功", geesePage);
    }

    @GetMapping("/breed/{breed}")
    @Operation(summary = "按品种查看小鹅", description = "管理员按品种查看小鹅")
    public ApiResponse<Page<GooseDTO>> getGeeseByBreed(
            @Parameter(description = "品种") @PathVariable String breed,
            @Parameter(description = "页码，从0开始") @RequestParam(defaultValue = "0") int page,
            @Parameter(description = "每页大小") @RequestParam(defaultValue = "20") int size) {
        log.debug("管理员按品种查看小鹅: breed={}, page={}, size={}", breed, page, size);

        Pageable pageable = PageRequest.of(page, size);
        Page<GooseDTO> geesePage = adminGooseService.getGeeseByBreed(breed, pageable);

        return ApiResponse.success("查询成功", geesePage);
    }

    @GetMapping("/search")
    @Operation(summary = "搜索小鹅", description = "管理员根据关键词搜索小鹅")
    public ApiResponse<Page<GooseDTO>> searchGeese(
            @Parameter(description = "搜索关键词") @RequestParam String keyword,
            @Parameter(description = "页码，从0开始") @RequestParam(defaultValue = "0") int page,
            @Parameter(description = "每页大小") @RequestParam(defaultValue = "20") int size) {
        log.debug("管理员搜索小鹅: keyword={}, page={}, size={}", keyword, page, size);

        Pageable pageable = PageRequest.of(page, size);
        Page<GooseDTO> geesePage = adminGooseService.searchGeese(keyword, pageable);

        return ApiResponse.success("搜索成功", geesePage);
    }

    @PutMapping("/{gooseId}/status")
    @Operation(summary = "更新小鹅状态", description = "管理员更新小鹅的状态")
    public ApiResponse<GooseDTO> updateGooseStatus(
            @Parameter(description = "小鹅ID") @PathVariable Long gooseId,
            @Parameter(description = "新状态") @RequestParam Integer status) {
        log.info("管理员更新小鹅状态: gooseId={}, status={}", gooseId, status);

        GooseDTO goose = adminGooseService.updateGooseStatus(gooseId, status);

        return ApiResponse.success("状态更新成功", goose);
    }

    @PutMapping("/{gooseId}/ownership-status")
    @Operation(summary = "更新小鹅归属状态", description = "管理员更新小鹅的归属状态")
    public ApiResponse<GooseDTO> updateGooseOwnershipStatus(
            @Parameter(description = "小鹅ID") @PathVariable Long gooseId,
            @Parameter(description = "归属状态：0-待售，1-已归属") @RequestParam Integer ownershipStatus) {
        log.info("管理员更新小鹅归属状态: gooseId={}, ownershipStatus={}", gooseId, ownershipStatus);

        GooseDTO goose = adminGooseService.updateGooseOwnershipStatus(gooseId, ownershipStatus);

        return ApiResponse.success("归属状态更新成功", goose);
    }

    @PutMapping("/{gooseId}/ownership")
    @Operation(summary = "设置小鹅归属", description = "管理员强制设置小鹅的归属关系")
    public ApiResponse<GooseDTO> setGooseOwnership(
            @Parameter(description = "小鹅ID") @PathVariable Long gooseId,
            @Parameter(description = "用户ID，null表示设为待售") @RequestParam(required = false) Long userId) {
        log.info("管理员设置小鹅归属: gooseId={}, userId={}", gooseId, userId);

        GooseDTO goose = adminGooseService.setGooseOwnership(gooseId, userId);

        return ApiResponse.success("归属设置成功", goose);
    }

    @DeleteMapping("/{gooseId}")
    @Operation(summary = "删除小鹅", description = "管理员删除小鹅（仅限待售状态）")
    public ApiResponse<Void> deleteGoose(
            @Parameter(description = "小鹅ID") @PathVariable Long gooseId) {
        log.info("管理员删除小鹅: gooseId={}", gooseId);

        adminGooseService.deleteGoose(gooseId);

        return ApiResponse.success("小鹅删除成功");
    }

    @DeleteMapping("/batch")
    @Operation(summary = "批量删除小鹅", description = "管理员批量删除小鹅")
    public ApiResponse<Void> batchDeleteGeese(
            @Parameter(description = "小鹅ID列表") @RequestBody List<Long> gooseIds) {
        log.info("管理员批量删除小鹅: 数量={}", gooseIds.size());

        adminGooseService.batchDeleteGeese(gooseIds);

        return ApiResponse.success("批量删除成功");
    }

    @GetMapping("/statistics")
    @Operation(summary = "获取小鹅统计信息", description = "管理员查看小鹅统计数据")
    public ApiResponse<AdminGooseService.AdminGooseStatistics> getGooseStatistics() {
        log.debug("管理员查看小鹅统计信息");

        AdminGooseService.AdminGooseStatistics statistics = adminGooseService.getGooseStatistics();

        return ApiResponse.success("统计信息获取成功", statistics);
    }
}
