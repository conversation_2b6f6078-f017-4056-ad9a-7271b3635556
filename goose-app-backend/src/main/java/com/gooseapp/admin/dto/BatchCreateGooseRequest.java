package com.gooseapp.admin.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;
import jakarta.validation.constraints.*;
import lombok.Data;

import java.util.List;

/**
 * 批量创建小鹅请求DTO
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-12-19
 */
@Data
@Schema(description = "批量创建小鹅请求")
public class BatchCreateGooseRequest {

    /**
     * 批量创建的小鹅列表
     */
    @NotNull(message = "小鹅列表不能为空")
    @NotEmpty(message = "小鹅列表不能为空")
    @Size(min = 1, max = 50, message = "批量创建数量必须在1-50之间")
    @Valid
    @Schema(description = "小鹅列表", required = true)
    private List<AdminGooseCreateRequest> geese;

    /**
     * 批量操作备注
     */
    @Size(max = 500, message = "备注信息长度不能超过500个字符")
    @Schema(description = "批量操作备注", example = "批量创建白鹅用于新年活动")
    private String batchRemark;

    /**
     * 是否忽略错误继续创建
     */
    @Schema(description = "是否忽略错误继续创建", example = "true")
    private Boolean ignoreErrors = true;

    /**
     * 获取批量创建数量
     */
    public int getBatchSize() {
        return geese != null ? geese.size() : 0;
    }

    /**
     * 验证请求参数
     */
    public void validate() {
        if (batchRemark != null) {
            batchRemark = batchRemark.trim();
        }
        
        if (geese != null) {
            for (AdminGooseCreateRequest goose : geese) {
                if (goose != null) {
                    goose.validate();
                }
            }
        }
    }

    @Override
    public String toString() {
        return String.format("BatchCreateGooseRequest{batchSize=%d, ignoreErrors=%s, remark='%s'}", 
                           getBatchSize(), ignoreErrors, batchRemark);
    }
}
