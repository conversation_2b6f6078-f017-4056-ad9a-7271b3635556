package com.gooseapp.admin.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.*;
import lombok.Data;

/**
 * 管理员创建小鹅请求DTO
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-12-19
 */
@Data
@Schema(description = "管理员创建小鹅请求")
public class AdminGooseCreateRequest {

    /**
     * 小鹅名称
     */
    @NotBlank(message = "小鹅名称不能为空")
    @Size(min = 1, max = 50, message = "小鹅名称长度必须在1-50个字符之间")
    @Schema(description = "小鹅名称", example = "小白", required = true)
    private String name;

    /**
     * 品种
     */
    @NotBlank(message = "品种不能为空")
    @Size(max = 30, message = "品种名称长度不能超过30个字符")
    @Schema(description = "品种", example = "白鹅", required = true)
    private String breed;

    /**
     * 性别：1-雄性，2-雌性
     */
    @NotNull(message = "性别不能为空")
    @Min(value = 1, message = "性别值必须为1或2")
    @Max(value = 2, message = "性别值必须为1或2")
    @Schema(description = "性别：1-雄性，2-雌性", example = "1", required = true)
    private Integer gender;

    /**
     * 初始健康度（0-100）
     */
    @Min(value = 0, message = "健康度不能小于0")
    @Max(value = 100, message = "健康度不能大于100")
    @Schema(description = "初始健康度（0-100）", example = "100")
    private Integer initialHealth = 100;

    /**
     * 初始饥饿度（0-100）
     */
    @Min(value = 0, message = "饥饿度不能小于0")
    @Max(value = 100, message = "饥饿度不能大于100")
    @Schema(description = "初始饥饿度（0-100）", example = "30")
    private Integer initialHunger = 30;

    /**
     * 初始口渴度（0-100）
     */
    @Min(value = 0, message = "口渴度不能小于0")
    @Max(value = 100, message = "口渴度不能大于100")
    @Schema(description = "初始口渴度（0-100）", example = "30")
    private Integer initialThirst = 30;

    /**
     * 初始清洁度（0-100）
     */
    @Min(value = 0, message = "清洁度不能小于0")
    @Max(value = 100, message = "清洁度不能大于100")
    @Schema(description = "初始清洁度（0-100）", example = "100")
    private Integer initialCleanliness = 100;

    /**
     * 初始快乐度（0-100）
     */
    @Min(value = 0, message = "快乐度不能小于0")
    @Max(value = 100, message = "快乐度不能大于100")
    @Schema(description = "初始快乐度（0-100）", example = "80")
    private Integer initialHappiness = 80;

    /**
     * 初始等级
     */
    @Min(value = 1, message = "等级不能小于1")
    @Max(value = 100, message = "等级不能大于100")
    @Schema(description = "初始等级", example = "1")
    private Integer initialLevel = 1;

    /**
     * 初始经验值
     */
    @Min(value = 0, message = "经验值不能小于0")
    @Schema(description = "初始经验值", example = "0")
    private Integer initialExperience = 0;

    /**
     * 小鹅图片URL
     */
    @Size(max = 255, message = "图片URL长度不能超过255个字符")
    @Schema(description = "小鹅图片URL", example = "https://example.com/goose.jpg")
    private String imageUrl;

    /**
     * 直播视频URL
     */
    @Size(max = 255, message = "视频URL长度不能超过255个字符")
    @Schema(description = "直播视频URL", example = "https://example.com/live.m3u8")
    private String videoUrl;

    /**
     * 备注信息
     */
    @Size(max = 500, message = "备注信息长度不能超过500个字符")
    @Schema(description = "备注信息", example = "这是一只特别可爱的小鹅")
    private String remark;

    /**
     * 验证请求参数
     */
    public void validate() {
        if (name != null) {
            name = name.trim();
        }
        if (breed != null) {
            breed = breed.trim();
        }
        if (remark != null) {
            remark = remark.trim();
        }
    }

    @Override
    public String toString() {
        return String.format("AdminGooseCreateRequest{name='%s', breed='%s', gender=%d, health=%d}", 
                           name, breed, gender, initialHealth);
    }
}
