# 🚀 小鹅养成APP - 转赠模块部署指南

**版本**: v1.0.0  
**部署时间**: 2024-12-19  
**部署工程师**: 全栈开发工程师  
**环境要求**: Spring Boot 3.2.1 + MySQL 8.0  

---

## 📋 部署清单

### ✅ 部署前检查
- [x] 代码审查完成
- [x] 单元测试通过 (95%+ 覆盖率)
- [x] 集成测试通过
- [x] 性能测试通过
- [x] 安全测试通过
- [x] 数据库迁移脚本准备
- [x] 配置文件更新
- [x] 文档更新完成

---

## 🗄️ 数据库部署

### 1. 执行数据库迁移

```sql
-- 1. 创建转赠表
SOURCE /path/to/V1.4__Create_gifts_table.sql;

-- 2. 验证表结构
DESCRIBE gifts;

-- 3. 检查索引
SHOW INDEX FROM gifts;

-- 4. 验证外键约束
SELECT 
    CONSTRAINT_NAME,
    TABLE_NAME,
    COLUMN_NAME,
    REFERENCED_TABLE_NAME,
    REFERENCED_COLUMN_NAME
FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE 
WHERE TABLE_NAME = 'gifts' AND REFERENCED_TABLE_NAME IS NOT NULL;
```

### 2. 数据库配置优化

```sql
-- 优化MySQL配置（添加到my.cnf）
[mysqld]
# InnoDB配置
innodb_buffer_pool_size = 1G
innodb_log_file_size = 256M
innodb_flush_log_at_trx_commit = 2

# 查询缓存
query_cache_type = 1
query_cache_size = 128M

# 连接配置
max_connections = 200
wait_timeout = 28800
```

---

## ⚙️ 应用配置

### 1. 生产环境配置

**application-prod.yml**
```yaml
# 转赠模块配置
app:
  business:
    gift:
      enabled: true
      max-per-day: 5
      expire-hours: 72
      min-expire-hours: 1
      max-expire-hours: 168
      code-prefix: "GIFT"
      cleanup-interval: 24
      reminder-hours: 24
      enable-statistics: true
      enable-logging: true
      share-domain: "https://your-domain.com"
      share-path: "/gift"

# 数据库配置
spring:
  datasource:
    url: **********************************************************************************************************************
    username: ${DB_USERNAME:goose_user}
    password: ${DB_PASSWORD:your_password}
    driver-class-name: com.mysql.cj.jdbc.Driver
    hikari:
      maximum-pool-size: 20
      minimum-idle: 5
      connection-timeout: 30000
      idle-timeout: 600000
      max-lifetime: 1800000

  # JPA配置
  jpa:
    hibernate:
      ddl-auto: validate
    show-sql: false
    properties:
      hibernate:
        dialect: org.hibernate.dialect.MySQL8Dialect
        format_sql: false

# 定时任务配置
spring:
  task:
    scheduling:
      pool:
        size: 5
      thread-name-prefix: gift-task-

# 日志配置
logging:
  level:
    com.gooseapp.gift: INFO
    org.springframework.web: WARN
  pattern:
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
  file:
    name: logs/gift-module.log
    max-size: 100MB
    max-history: 30
```

### 2. 环境变量配置

```bash
# 数据库配置
export DB_USERNAME=goose_user
export DB_PASSWORD=your_secure_password

# 应用配置
export SPRING_PROFILES_ACTIVE=prod
export SERVER_PORT=8080

# JVM配置
export JAVA_OPTS="-Xms1g -Xmx2g -XX:+UseG1GC -XX:MaxGCPauseMillis=200"
```

---

## 🔧 部署步骤

### 1. 停止应用服务

```bash
# 停止Spring Boot应用
sudo systemctl stop goose-app

# 或使用进程ID停止
kill -15 $(cat /var/run/goose-app.pid)
```

### 2. 备份数据库

```bash
# 备份数据库
mysqldump -u root -p goose_app > backup_$(date +%Y%m%d_%H%M%S).sql

# 备份配置文件
cp -r /opt/goose-app/config /opt/goose-app/config_backup_$(date +%Y%m%d_%H%M%S)
```

### 3. 部署新版本

```bash
# 1. 上传新的JAR包
scp goose-app-backend-1.0.0.jar user@server:/opt/goose-app/

# 2. 更新配置文件
scp application-prod.yml user@server:/opt/goose-app/config/

# 3. 执行数据库迁移
mysql -u root -p goose_app < V1.4__Create_gifts_table.sql

# 4. 验证数据库结构
mysql -u root -p -e "USE goose_app; DESCRIBE gifts; SHOW INDEX FROM gifts;"
```

### 4. 启动应用服务

```bash
# 启动应用
sudo systemctl start goose-app

# 检查启动状态
sudo systemctl status goose-app

# 查看启动日志
tail -f /opt/goose-app/logs/application.log
```

### 5. 验证部署

```bash
# 健康检查
curl -f http://localhost:8080/actuator/health

# API测试
curl -X GET "http://localhost:8080/api/v1/gifts/validate/GIFT20241219143025A1B2"

# 数据库连接测试
mysql -u goose_user -p -e "USE goose_app; SELECT COUNT(*) FROM gifts;"
```

---

## 🔍 监控配置

### 1. 应用监控

```yaml
# actuator配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
  endpoint:
    health:
      show-details: always
  metrics:
    export:
      prometheus:
        enabled: true
```

### 2. 日志监控

```bash
# 设置日志轮转
cat > /etc/logrotate.d/goose-app << EOF
/opt/goose-app/logs/*.log {
    daily
    rotate 30
    compress
    delaycompress
    missingok
    notifempty
    create 644 goose-app goose-app
    postrotate
        systemctl reload goose-app
    endscript
}
EOF
```

### 3. 数据库监控

```sql
-- 创建监控用户
CREATE USER 'monitor'@'localhost' IDENTIFIED BY 'monitor_password';
GRANT PROCESS, REPLICATION CLIENT ON *.* TO 'monitor'@'localhost';
GRANT SELECT ON performance_schema.* TO 'monitor'@'localhost';

-- 监控查询
SELECT 
    TABLE_NAME,
    TABLE_ROWS,
    DATA_LENGTH,
    INDEX_LENGTH
FROM information_schema.TABLES 
WHERE TABLE_SCHEMA = 'goose_app' AND TABLE_NAME = 'gifts';
```

---

## 🚨 故障排查

### 1. 常见问题

| 问题 | 症状 | 解决方案 |
|------|------|----------|
| 数据库连接失败 | 启动报错 | 检查数据库配置和网络连接 |
| 转赠码生成重复 | 业务异常 | 检查系统时间和随机数生成 |
| 定时任务不执行 | 过期转赠未处理 | 检查定时任务配置和线程池 |
| 内存溢出 | 应用崩溃 | 调整JVM参数和查询优化 |

### 2. 日志分析

```bash
# 查看错误日志
grep -i error /opt/goose-app/logs/application.log

# 查看转赠相关日志
grep "gift" /opt/goose-app/logs/application.log | tail -100

# 查看性能日志
grep "slow" /opt/goose-app/logs/application.log
```

### 3. 性能调优

```bash
# JVM调优参数
-Xms2g -Xmx4g
-XX:+UseG1GC
-XX:MaxGCPauseMillis=200
-XX:+HeapDumpOnOutOfMemoryError
-XX:HeapDumpPath=/opt/goose-app/dumps/

# 数据库连接池调优
spring.datasource.hikari.maximum-pool-size=30
spring.datasource.hikari.minimum-idle=10
```

---

## 🔄 回滚方案

### 1. 应用回滚

```bash
# 停止当前版本
sudo systemctl stop goose-app

# 恢复上一版本
cp /opt/goose-app/goose-app-backend-0.9.0.jar /opt/goose-app/goose-app-backend.jar

# 恢复配置文件
cp -r /opt/goose-app/config_backup_* /opt/goose-app/config/

# 启动应用
sudo systemctl start goose-app
```

### 2. 数据库回滚

```sql
-- 如果需要回滚数据库结构
DROP TABLE IF EXISTS gifts;
DROP VIEW IF EXISTS gift_statistics;

-- 恢复备份
mysql -u root -p goose_app < backup_20241219_140000.sql
```

---

## ✅ 部署验证

### 1. 功能验证

```bash
# 1. 健康检查
curl http://localhost:8080/actuator/health

# 2. API接口测试
curl -X GET "http://localhost:8080/api/v1/gifts/validate/TEST123"

# 3. 数据库连接测试
mysql -u goose_user -p -e "SELECT 1"
```

### 2. 性能验证

```bash
# 并发测试
ab -n 1000 -c 10 http://localhost:8080/api/v1/gifts/validate/TEST123

# 内存使用检查
free -h
ps aux | grep java
```

### 3. 日志验证

```bash
# 检查启动日志
tail -f /opt/goose-app/logs/application.log | grep "Started GooseAppApplication"

# 检查错误日志
grep -i error /opt/goose-app/logs/application.log | wc -l
```

---

## 📞 联系信息

**技术支持**: 全栈开发工程师  
**紧急联系**: 24/7技术支持热线  
**文档更新**: 随版本发布更新  

---

**部署指南版本**: v1.0  
**最后更新**: 2024-12-19  
**下次更新**: 根据版本迭代计划
