<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>登录/注册 - 小鹅养成APP</title>
    <link rel="stylesheet" href="../assets/style.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
    <style>
        .page-content {
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .auth-container {
            width: 100%;
            max-width: 340px;
            text-align: center;
        }
        
        .mascot {
            font-size: 100px;
            line-height: 1;
            margin-bottom: var(--gg-spacing-lg);
            animation: float 3s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0); }
            50% { transform: translateY(-15px); }
        }

        .auth-panel {
            background: rgba(255, 255, 255, 0.6);
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            border-radius: var(--gg-radius-blob);
            border: 1px solid rgba(255, 255, 255, 0.2);
            padding: var(--gg-spacing-xl);
            box-shadow: var(--gg-shadow-gummy-idle);
            display: flex;
            flex-direction: column;
            gap: var(--gg-spacing-lg);
            margin-bottom: var(--gg-spacing-xl);
        }

        .input-group {
            display: flex;
            gap: var(--gg-spacing-sm);
        }

        .input-field {
            flex-grow: 1;
            padding: 16px;
            border: 1px solid transparent;
            border-radius: var(--gg-radius-gummy);
            background: rgba(255, 255, 255, 0.5);
            font-size: var(--gg-font-size-body-large);
            font-family: var(--gg-font-family-body);
            color: var(--gg-color-text-primary);
            box-shadow: inset 0 2px 4px rgba(0,0,0,0.05);
            transition: all 0.2s ease-in-out;
            min-width: 0;
        }

        .input-field::placeholder {
            color: var(--gg-color-text-tertiary);
        }

        .input-field:focus {
            outline: none;
            border-color: rgba(255, 215, 0, 0.5);
            background: rgba(255, 255, 255, 0.8);
            box-shadow: inset 0 2px 4px rgba(0,0,0,0.05), 0 0 0 3px rgba(255, 215, 0, 0.3);
        }
        
        .gummy-button.get-code {
            flex-shrink: 0;
            padding: 0 20px;
            background: var(--gg-color-secondary-sky);
        }
        
        .main-auth-button {
             padding-top: 16px;
             padding-bottom: 16px;
        }

        .divider {
            display: flex;
            align-items: center;
            text-align: center;
            color: var(--gg-color-text-tertiary);
            font-size: var(--gg-font-size-caption);
            margin: var(--gg-spacing-xl) 0;
        }
        .divider::before, .divider::after {
            content: '';
            flex: 1;
            border-bottom: 1px solid rgba(0,0,0,0.1);
        }
        .divider:not(:empty)::before {
            margin-right: .5em;
        }
        .divider:not(:empty)::after {
            margin-left: .5em;
        }
        
        .social-login-options {
            display: flex;
            flex-direction: column;
            gap: var(--gg-spacing-md);
        }
        
        .gummy-button.social-login {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 12px;
            background: var(--gg-color-secondary-breath);
            color: var(--gg-color-text-primary);
            padding-top: 14px;
            padding-bottom: 14px;
        }
        
        .gummy-button.social-login .fab {
            font-size: 20px;
        }

    </style>
</head>
<body>
    <div class="mobile-frame">
        <div class="page-content">
            <div class="auth-container">
                <div class="mascot">🦆</div>
                
                <div class="auth-panel">
                    <div class="input-group">
                        <input type="tel" class="input-field" placeholder="手机号">
                        <button class="gummy-button get-code">获取验证码</button>
                    </div>
                    <input type="text" class="input-field" placeholder="6位验证码">
                    <button class="gummy-button main-auth-button">登录 / 注册</button>
                </div>
        
                <div class="divider">或</div>
                
                <div class="social-login-options">
                    <button class="gummy-button social-login">
                        <i class="fas fa-mobile-alt"></i> 一键手机号登录
                    </button>
                    <button class="gummy-button social-login">
                        <i class="fab fa-weixin" style="color: #07C160;"></i> 微信登录
                    </button>
                </div>
            </div>
        </div>
        <nav class="global-nav">
            <a href="./discover.html" class="nav-item">
                <i class="fas fa-compass icon"></i>
                <span class="label">发现</span>
            </a>
            <a href="./shop.html" class="nav-item">
                <i class="fas fa-store icon"></i>
                <span class="label">商城</span>
            </a>
            <a href="./my-geese.html" class="nav-item">
                <i class="fas fa-egg icon"></i>
                <span class="label">我的鹅</span>
            </a>
            <a href="./social-feed.html" class="nav-item">
                <i class="fas fa-users icon"></i>
                <span class="label">动态</span>
            </a>
            <a href="./profile.html" class="nav-item">
                <i class="fas fa-user-circle icon"></i>
                <span class="label">我的</span>
            </a>
        </nav>
    </div>
</body>
</html>