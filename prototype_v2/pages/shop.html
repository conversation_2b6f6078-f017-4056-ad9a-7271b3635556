<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>商城</title>
    <link rel="stylesheet" href="../assets/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    <style>
        .page-content {
            display: flex;
            flex-direction: column;
        }
        .header {
            display: flex;
            align-items: center;
            padding: var(--gg-spacing-md) var(--gg-page-padding);
            gap: var(--gg-spacing-md);
        }
        .search-bar {
            flex-grow: 1;
            background: rgba(255, 255, 255, 0.5);
            border-radius: var(--gg-radius-full);
            padding: var(--gg-spacing-sm) var(--gg-spacing-md);
            display: flex;
            align-items: center;
            gap: var(--gg-spacing-sm);
            color: var(--gg-color-text-tertiary);
            box-shadow: var(--gg-shadow-glass);
        }
        .icon-cart {
            font-size: 24px;
            color: var(--gg-color-text-primary);
            position: relative;
        }
        .cart-badge {
            position: absolute;
            top: -5px;
            right: -8px;
            background-color: var(--gg-color-accent-coral);
            color: white;
            font-size: 10px;
            font-weight: bold;
            border-radius: 50%;
            width: 16px;
            height: 16px;
            display: flex;
            justify-content: center;
            align-items: center;
        }
        .filters {
            display: flex;
            gap: var(--gg-spacing-sm);
            padding: 0 var(--gg-page-padding);
            overflow-x: auto;
            white-space: nowrap;
            -ms-overflow-style: none;  /* IE and Edge */
            scrollbar-width: none;  /* Firefox */
        }
        .filters::-webkit-scrollbar {
            display: none; /* Chrome, Safari, Opera */
        }
        .filter-chip {
            background: rgba(255, 255, 255, 0.5);
            padding: var(--gg-spacing-sm) var(--gg-spacing-md);
            border-radius: var(--gg-radius-full);
            font-size: var(--gg-font-size-caption);
            font-weight: var(--gg-font-weight-medium);
            cursor: pointer;
            transition: all 0.2s ease;
        }
        .filter-chip.active {
            background: var(--gg-color-primary-gold);
            color: var(--gg-color-text-primary);
            box-shadow: var(--gg-shadow-gummy-idle);
        }
        .shop-grid {
            flex-grow: 1;
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: var(--gg-spacing-md);
            padding: var(--gg-spacing-md) var(--gg-page-padding);
            overflow-y: auto;
        }
        .product-card {
            background: rgba(255, 255, 255, 0.4);
            backdrop-filter: blur(15px);
            border-radius: var(--gg-radius-blob);
            padding: var(--gg-spacing-sm);
            display: flex;
            flex-direction: column;
            box-shadow: var(--gg-shadow-gummy-idle);
            transition: all 0.2s ease;
        }
        .product-card:hover {
            transform: translateY(-4px);
        }
        .product-image {
            width: 100%;
            height: 100px;
            border-radius: var(--gg-radius-gummy);
            background: #eee url('https://i.imgur.com/kMv342I.png') center center/contain no-repeat;
        }
        .product-info {
            padding: var(--gg-spacing-sm) var(--gg-spacing-xs);
        }
        .product-name {
            font-family: var(--gg-font-family-heading);
            font-weight: var(--gg-font-weight-bold);
            font-size: var(--gg-font-size-body);
            margin-bottom: var(--gg-spacing-xs);
        }
        .product-price {
            font-size: var(--gg-font-size-body-large);
            font-weight: var(--gg-font-weight-bold);
            color: var(--gg-color-accent-coral);
        }
        .product-tag {
            font-size: 10px;
            background: var(--gg-color-accent-heal);
            color: var(--gg-color-text-primary);
            padding: 2px 6px;
            border-radius: var(--gg-radius-soft);
            display: inline-block;
            margin-top: var(--gg-spacing-sm);
        }

    </style>
</head>
<body>
    <div class="mobile-frame">
        <div class="page-content">
            <header class="header">
                <div class="search-bar">
                    <i class="fas fa-search"></i>
                    <span>搜索小鹅...</span>
                </div>
                <div class="icon-cart">
                    <i class="fas fa-shopping-bag"></i>
                    <div class="cart-badge">3</div>
                </div>
            </header>
    
            <div class="filters">
                <div class="filter-chip active">全部</div>
                <div class="filter-chip">小白鹅</div>
                <div class="filter-chip">小灰鹅</div>
                <div class="filter-chip">限量版</div>
                <div class="filter-chip">特惠</div>
            </div>
    
            <div class="shop-grid">
                <div class="product-card">
                    <div class="product-image"></div>
                    <div class="product-info">
                        <div class="product-name">小白鹅2号</div>
                        <div class="product-price">¥199</div>
                        <div class="product-tag">🔥热门</div>
                    </div>
                </div>
                 <div class="product-card">
                    <div class="product-image" style="background-image: url('https://i.imgur.com/L7p8U5A.png');"></div>
                    <div class="product-info">
                        <div class="product-name">小灰鹅5号</div>
                        <div class="product-price">¥299</div>
                        <div class="product-tag" style="background-color: var(--gg-color-secondary-sky);">✨限量</div>
                    </div>
                </div>
                 <div class="product-card">
                    <div class="product-image" style="background-image: url('https://i.imgur.com/kMv342I.png');"></div>
                    <div class="product-info">
                        <div class="product-name">小花鹅1号</div>
                        <div class="product-price">¥399</div>
                         <div class="product-tag" style="background-color: var(--gg-color-accent-coral);">🎁礼品装</div>
                    </div>
                </div>
                 <div class="product-card">
                    <div class="product-image" style="background-image: url('https://i.imgur.com/L7p8U5A.png');"></div>
                    <div class="product-info">
                        <div class="product-name">小黑鹅3号</div>
                        <div class="product-price">¥159</div>
                        <div class="product-tag" style="background-color: var(--gg-color-primary-gummy);">👶新生</div>
                    </div>
                </div>
                 <div class="product-card">
                    <div class="product-image"></div>
                    <div class="product-info">
                        <div class="product-name">小白鹅3号</div>
                        <div class="product-price">¥199</div>
                    </div>
                </div>
                 <div class="product-card">
                    <div class="product-image" style="background-image: url('https://i.imgur.com/L7p8U5A.png');"></div>
                    <div class="product-info">
                        <div class="product-name">小灰鹅8号</div>
                        <div class="product-price">¥299</div>
                    </div>
                </div>
            </div>
        </div>
        <nav class="global-nav">
            <a href="./discover.html" class="nav-item">
                <i class="fas fa-compass icon"></i>
                <span class="label">发现</span>
            </a>
            <a href="./shop.html" class="nav-item active">
                <i class="fas fa-store icon"></i>
                <span class="label">商城</span>
            </a>
            <a href="./my-geese.html" class="nav-item">
                <i class="fas fa-egg icon"></i>
                <span class="label">我的鹅</span>
            </a>
            <a href="./social-feed.html" class="nav-item">
                <i class="fas fa-users icon"></i>
                <span class="label">动态</span>
            </a>
            <a href="./profile.html" class="nav-item">
                <i class="fas fa-user-circle icon"></i>
                <span class="label">我的</span>
            </a>
        </nav>
    </div>
</body>
</html>