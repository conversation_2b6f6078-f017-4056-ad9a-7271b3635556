<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>转赠流程</title>
    <link rel="stylesheet" href="../assets/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    <style>
        .mobile-frame {
            display: flex;
            flex-direction: column;
            padding: var(--gg-page-padding);
            box-sizing: border-box;
            gap: var(--gg-spacing-lg);
        }
        .header {
            display: flex;
            align-items: center;
            gap: var(--gg-spacing-md);
        }
        .header .icon {
            font-size: 20px;
        }
        .header-title {
            font-size: var(--gg-font-size-title);
            font-weight: var(--gg-font-weight-bold);
        }
        .goose-info-card {
            display: flex;
            align-items: center;
            gap: var(--gg-spacing-md);
            padding: var(--gg-spacing-md);
        }
        .goose-avatar {
            width: 60px;
            height: 60px;
            border-radius: var(--gg-radius-soft);
            background: #fff url('https://i.imgur.com/kMv342I.png') center/contain no-repeat;
        }
        .goose-details .name {
            font-weight: bold;
            font-size: var(--gg-font-size-body-large);
        }
        .goose-details .id {
            font-size: var(--gg-font-size-caption);
            color: var(--gg-color-text-tertiary);
        }
        .step-title {
            font-size: var(--gg-font-size-body-large);
            font-weight: bold;
            color: var(--gg-color-text-secondary);
            text-align: center;
            margin: var(--gg-spacing-lg) 0;
        }
        .options-list {
            display: flex;
            flex-direction: column;
            gap: var(--gg-spacing-md);
        }
        .option-card {
            display: flex;
            align-items: center;
            padding: var(--gg-spacing-lg);
            gap: var(--gg-spacing-lg);
            transition: all 0.2s ease;
        }
        .option-card:hover {
            transform: scale(1.02);
        }
        .option-card .icon {
            font-size: 24px;
            width: 30px;
            text-align: center;
        }
        .option-details .title {
            font-weight: bold;
        }
        .option-details .subtitle {
            font-size: var(--gg-font-size-caption);
            color: var(--gg-color-text-secondary);
        }
        .message-box {
            width: 100%;
            height: 150px;
            border: none;
            border-radius: var(--gg-radius-gummy);
            padding: var(--gg-spacing-md);
            box-sizing: border-box;
            background-color: rgba(255,255,255,0.7);
            font-family: var(--gg-font-family-body);
            font-size: var(--gg-font-size-body);
        }
        .message-box:focus {
            outline: none;
            box-shadow: var(--gg-glow-primary);
        }
    </style>
</head>
<body>
    <div class="mobile-frame">
        <header class="header">
            <i class="fas fa-arrow-left icon"></i>
            <div class="header-title">转赠小鹅</div>
        </header>

        <div class="glass-card goose-info-card">
            <div class="goose-avatar"></div>
            <div class="goose-details">
                <div class="name">豆豆</div>
                <div class="id">ID: XE240520001</div>
            </div>
        </div>

        <div class="step-title">✨ 让这份礼物更有温度 ✨</div>
        
        <textarea class="message-box" placeholder="希望这只小鹅能给你带来快乐！记得每天来看看它哦~ 🥰"></textarea>

        <div class="options-list">
            <div class="glass-card option-card">
                <i class="fab fa-weixin icon" style="color: #2DC100;"></i>
                <div class="option-details">
                    <div class="title">发送给微信好友</div>
                    <div class="subtitle">一对一的贴心礼物</div>
                </div>
            </div>
             <div class="glass-card option-card">
                <i class="fas fa-link icon"></i>
                <div class="option-details">
                    <div class="title">复制链接</div>
                    <div class="subtitle">分享到其他平台</div>
                </div>
            </div>
        </div>

        <button class="gummy-button accent-glow" style="margin-top: auto;">
            <i class="fas fa-gift"></i> 确认转赠
        </button>

    </div>
</body>
</html>