<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>我的</title>
    <link rel="stylesheet" href="../assets/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    <style>
        /* .mobile-frame styling is now handled by .page-content in the global style.css */
        .page-content {
             display: flex;
             flex-direction: column;
             gap: var(--gg-spacing-xl);
        }
        .profile-header {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: var(--gg-spacing-md);
            margin-top: var(--gg-spacing-xl);
        }
        .avatar-container {
            width: 100px;
            height: 100px;
            border-radius: 50%;
            background: rgba(255,255,255,0.6);
            padding: var(--gg-spacing-sm);
            box-shadow: var(--gg-shadow-gummy-idle);
        }
        .avatar-container img {
            width: 100%;
            height: 100%;
            border-radius: 50%;
            object-fit: cover;
        }
        .username {
            font-size: var(--gg-font-size-title);
            font-weight: var(--gg-font-weight-bold);
        }
        .user-id {
            font-size: var(--gg-font-size-caption);
            color: var(--gg-color-text-tertiary);
            background-color: rgba(255,255,255,0.3);
            padding: 2px 8px;
            border-radius: var(--gg-radius-full);
        }
        .stats-bar {
            display: flex;
            justify-content: space-around;
            text-align: center;
            padding: var(--gg-spacing-md) 0;
        }
        .stat-item .value {
            font-size: var(--gg-font-size-title);
            font-weight: var(--gg-font-weight-bold);
        }
        .stat-item .label {
            font-size: var(--gg-font-size-caption);
            color: var(--gg-color-text-secondary);
        }
        .menu-list {
            display: flex;
            flex-direction: column;
            gap: var(--gg-spacing-md);
        }
        .menu-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: var(--gg-spacing-md) var(--gg-spacing-lg);
        }
        .menu-item .icon {
            font-size: 20px;
            margin-right: var(--gg-spacing-lg);
            width: 24px;
            text-align: center;
            color: var(--gg-color-text-secondary);
        }
        .menu-item .label {
            flex-grow: 1;
            font-size: var(--gg-font-size-body-large);
            font-weight: 500;
        }
        .menu-item .chevron {
            color: var(--gg-color-text-tertiary);
        }
    </style>
</head>
<body>
    <div class="mobile-frame">
        <div class="page-content">
            <div class="profile-header">
                <div class="avatar-container">
                    <img src="https://i.pravatar.cc/150?u=a042581f4e29026704d" alt="User Avatar">
                </div>
                <div class="username">鹅先森</div>
                <div class="user-id">ID: 12345678</div>
            </div>
    
            <div class="glass-card stats-bar">
                <div class="stat-item">
                    <div class="value">5</div>
                    <div class="label">拥有小鹅</div>
                </div>
                <div class="stat-item">
                    <div class="value">128</div>
                    <div class="label">好友</div>
                </div>
                <div class="stat-item">
                    <div class="value">36</div>
                    <div class="label">动态</div>
                </div>
            </div>
    
            <div class="glass-card menu-list">
                <div class="menu-item">
                    <i class="fas fa-box-open icon"></i>
                    <span class="label">我的订单</span>
                    <i class="fas fa-chevron-right chevron"></i>
                </div>
                <div class="menu-item">
                    <i class="fas fa-gift icon"></i>
                    <span class="label">我的转赠</span>
                    <i class="fas fa-chevron-right chevron"></i>
                </div>
                <div class="menu-item">
                    <i class="fas fa-cog icon"></i>
                    <span class="label">设置</span>
                    <i class="fas fa-chevron-right chevron"></i>
                </div>
                <div class="menu-item">
                    <i class="fas fa-question-circle icon"></i>
                    <span class="label">帮助与反馈</span>
                    <i class="fas fa-chevron-right chevron"></i>
                </div>
            </div>
        </div>
        <nav class="global-nav">
            <a href="./discover.html" class="nav-item">
                <i class="fas fa-compass icon"></i>
                <span class="label">发现</span>
            </a>
            <a href="./shop.html" class="nav-item">
                <i class="fas fa-store icon"></i>
                <span class="label">商城</span>
            </a>
            <a href="./my-geese.html" class="nav-item">
                <i class="fas fa-egg icon"></i>
                <span class="label">我的鹅</span>
            </a>
            <a href="./social-feed.html" class="nav-item">
                <i class="fas fa-users icon"></i>
                <span class="label">动态</span>
            </a>
            <a href="./profile.html" class="nav-item active">
                <i class="fas fa-user-circle icon"></i>
                <span class="label">我的</span>
            </a>
        </nav>
    </div>
</body>
</html>