<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>动态</title>
    <link rel="stylesheet" href="../assets/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    <style>
        .page-content {
            display: flex;
            flex-direction: column;
            padding: 0;
            position: relative;
        }
        .header {
            padding: var(--gg-spacing-md) var(--gg-page-padding);
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-bottom: 1px solid rgba(0,0,0,0.05);
            background: rgba(255,255,255,0.2);
            backdrop-filter: blur(10px);
        }
        .header-title {
            font-size: var(--gg-font-size-title);
            font-weight: var(--gg-font-weight-bold);
        }
        .header-icon {
            font-size: 22px;
        }
        .feed-container {
            flex-grow: 1;
            overflow-y: auto;
            padding: var(--gg-page-padding);
            display: flex;
            flex-direction: column;
            gap: var(--gg-spacing-lg);
        }
        .feed-post {
            padding: var(--gg-spacing-md);
        }
        .post-header {
            display: flex;
            align-items: center;
            gap: var(--gg-spacing-md);
            margin-bottom: var(--gg-spacing-md);
        }
        .avatar {
            width: 44px;
            height: 44px;
            border-radius: 50%;
            background-color: var(--gg-color-secondary-sky);
        }
        .post-author .name {
            font-weight: bold;
        }
        .post-author .time {
            font-size: var(--gg-font-size-caption);
            color: var(--gg-color-text-tertiary);
        }
        .post-content {
            margin-bottom: var(--gg-spacing-md);
            line-height: 1.6;
        }
        .post-image {
            width: 100%;
            height: 180px;
            border-radius: var(--gg-radius-gummy);
            background: var(--gg-color-secondary-breath) url('https://i.imgur.com/xCE2N6y.gif') center/cover;
            margin-bottom: var(--gg-spacing-md);
        }
        .post-actions {
            display: flex;
            justify-content: space-around;
            color: var(--gg-color-text-secondary);
        }
        .action {
            display: flex;
            align-items: center;
            gap: var(--gg-spacing-sm);
            font-size: var(--gg-font-size-body);
        }
        .fab {
            position: absolute;
            bottom: var(--gg-spacing-xl);
            right: var(--gg-page-padding);
            width: 60px;
            height: 60px;
            background: var(--gg-color-primary-gold);
            border-radius: 50%;
            display: flex;
            justify-content: center;
            align-items: center;
            font-size: 28px;
            color: var(--gg-color-text-primary);
            box-shadow: var(--gg-shadow-gummy-idle);
            cursor: pointer;
        }
    </style>
</head>
<body>
    <div class="mobile-frame">
        <div class="page-content">
            <header class="header">
                <h1 class="header-title">动态</h1>
                <i class="fas fa-paper-plane header-icon"></i>
            </header>
    
            <div class="feed-container">
                <div class="glass-card feed-post">
                    <div class="post-header">
                        <div class="avatar"></div>
                        <div class="post-author">
                            <div class="name">小美</div>
                            <div class="time">15分钟前</div>
                        </div>
                    </div>
                    <p class="post-content">今天天气真好，带我的小鹅晒太阳～☀️</p>
                    <div class="post-image" style="background-image: url('https://i.imgur.com/kMv342I.png');"></div>
                    <div class="post-actions">
                        <div class="action"><i class="far fa-heart"></i> 23</div>
                        <div class="action"><i class="far fa-comment"></i> 5</div>
                        <div class="action"><i class="fas fa-share"></i> 2</div>
                    </div>
                </div>
    
                <div class="glass-card feed-post">
                    <div class="post-header">
                        <div class="avatar" style="background-color: var(--gg-color-accent-coral);"></div>
                        <div class="post-author">
                            <div class="name">阿强</div>
                            <div class="time">1小时前</div>
                        </div>
                    </div>
                    <p class="post-content">我的小灰鹅终于长大了！</p>
                    <div class="post-actions">
                        <div class="action"><i class="far fa-heart"></i> 12</div>
                        <div class="action"><i class="far fa-comment"></i> 3</div>
                        <div class="action"><i class="fas fa-share"></i> 1</div>
                    </div>
                </div>
            </div>
            
            <div class="fab">
                <i class="fas fa-pen"></i>
            </div>
        </div>
        <nav class="global-nav">
            <a href="./discover.html" class="nav-item">
                <i class="fas fa-compass icon"></i>
                <span class="label">发现</span>
            </a>
            <a href="./shop.html" class="nav-item">
                <i class="fas fa-store icon"></i>
                <span class="label">商城</span>
            </a>
            <a href="./my-geese.html" class="nav-item">
                <i class="fas fa-egg icon"></i>
                <span class="label">我的鹅</span>
            </a>
            <a href="./social-feed.html" class="nav-item active">
                <i class="fas fa-users icon"></i>
                <span class="label">动态</span>
            </a>
            <a href="./profile.html" class="nav-item">
                <i class="fas fa-user-circle icon"></i>
                <span class="label">我的</span>
            </a>
        </nav>
    </div>
</body>
</html>