<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>小鹅详情页</title>
    <link rel="stylesheet" href="../assets/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    <style>
        .page-content {
             display: flex;
             flex-direction: column;
             gap: var(--gg-spacing-lg);
        }
        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 var(--gg-spacing-sm);
            color: var(--gg-color-text-primary);
        }
        .header-title {
            font-family: var(--gg-font-family-heading);
            font-size: var(--gg-font-size-title);
            font-weight: var(--gg-font-weight-bold);
        }
        .icon {
            font-size: 20px;
        }
        .video-container {
            width: 100%;
            height: 250px;
            border-radius: var(--gg-radius-blob);
            background: #333 url('https://i.imgur.com/xCE2N6y.gif') center center/cover;
            display: flex;
            align-items: flex-end;
            padding: var(--gg-spacing-md);
            box-sizing: border-box;
            color: white;
            box-shadow: var(--gg-shadow-gummy-idle);
        }
        .video-controls {
            width: 100%;
            display: flex;
            justify-content: space-between;
            align-items: center;
            background: rgba(0,0,0,0.3);
            backdrop-filter: blur(5px);
            padding: var(--gg-spacing-sm) var(--gg-spacing-md);
            border-radius: var(--gg-radius-gummy);
        }
        .stats-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: var(--gg-spacing-md);
        }
        .stat-card {
            padding: var(--gg-spacing-md);
            text-align: center;
        }
        .stat-card .icon {
            font-size: 24px;
            margin-bottom: var(--gg-spacing-sm);
        }
        .stat-card .label {
            font-size: var(--gg-font-size-caption);
            font-weight: var(--gg-font-weight-medium);
            margin-bottom: var(--gg-spacing-sm);
        }
        .progress-bar {
            width: 100%;
            height: 10px;
            background-color: rgba(0,0,0,0.1);
            border-radius: var(--gg-radius-full);
            overflow: hidden;
        }
        .progress-fill {
            height: 100%;
            border-radius: var(--gg-radius-full);
        }
        .health .progress-fill { background: var(--gg-gradient-health); }
        .hunger .progress-fill { background: var(--gg-gradient-hunger); }
        .cleanliness .progress-fill { background: var(--gg-gradient-clean); }
        .happiness .progress-fill { background: var(--gg-color-primary-gold); }

        .actions-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: var(--gg-spacing-md);
            margin-top: auto; /* Push to bottom */
            padding-bottom: var(--gg-spacing-lg);
        }
        .action-button {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: var(--gg-spacing-sm);
            padding: var(--gg-spacing-md) 0;
            background: rgba(255, 255, 255, 0.6);
            border-radius: var(--gg-radius-gummy);
            box-shadow: var(--gg-shadow-gummy-idle);
            transition: all 0.2s ease-in-out;
            cursor: pointer;
        }
        .action-button:hover {
            transform: scale(1.05);
        }
        .action-button .icon {
            font-size: 28px;
        }
    </style>
</head>
<body>
    <div class="mobile-frame">
        <div class="page-content">
            <header class="header">
                <i class="fas fa-arrow-left icon"></i>
                <div class="header-title">豆豆</div>
                <i class="fas fa-ellipsis-h icon"></i>
            </header>
    
            <div class="video-container">
                <div class="video-controls">
                    <span><i class="fas fa-video icon"></i> 直播中</span>
                    <span><i class="fas fa-eye icon"></i> 128</span>
                </div>
            </div>
    
            <div class="stats-grid">
                <div class="glass-card stat-card health">
                    <i class="fas fa-heartbeat icon" style="color: #32CD32;"></i>
                    <div class="label">健康</div>
                    <div class="progress-bar"><div class="progress-fill" style="width: 85%;"></div></div>
                </div>
                <div class="glass-card stat-card hunger">
                    <i class="fas fa-utensils icon" style="color: #FF7F50;"></i>
                    <div class="label">饥饿</div>
                    <div class="progress-bar"><div class="progress-fill" style="width: 60%;"></div></div>
                </div>
                <div class="glass-card stat-card cleanliness">
                    <i class="fas fa-soap icon" style="color: #4682B4;"></i>
                    <div class="label">清洁</div>
                    <div class="progress-bar"><div class="progress-fill" style="width: 75%;"></div></div>
                </div>
                <div class="glass-card stat-card happiness">
                    <i class="fas fa-smile-beam icon" style="color: var(--gg-color-primary-gold);"></i>
                    <div class="label">心情</div>
                    <div class="progress-bar"><div class="progress-fill" style="width: 90%;"></div></div>
                </div>
            </div>
    
            <div class="actions-grid">
                <div class="action-button">
                    <i class="fas fa-bone icon"></i>
                    <span>喂食</span>
                </div>
                <div class="action-button">
                    <i class="fas fa-tint icon"></i>
                    <span>喂水</span>
                </div>
                 <div class="action-button">
                    <i class="fas fa-shower icon"></i>
                    <span>清洁</span>
                </div>
                 <div class="action-button">
                    <i class="fas fa-gamepad icon"></i>
                    <span>玩耍</span>
                </div>
            </div>
    
            <button class="gummy-button accent-glow" style="width: 100%; margin-top: var(--gg-spacing-sm);">
                <i class="fas fa-gift"></i> 转赠给朋友
            </button>
        </div>
        <nav class="global-nav">
            <a href="./discover.html" class="nav-item">
                <i class="fas fa-compass icon"></i>
                <span class="label">发现</span>
            </a>
            <a href="./shop.html" class="nav-item">
                <i class="fas fa-store icon"></i>
                <span class="label">商城</span>
            </a>
            <a href="./my-geese.html" class="nav-item active">
                <i class="fas fa-egg icon"></i>
                <span class="label">我的鹅</span>
            </a>
            <a href="./social-feed.html" class="nav-item">
                <i class="fas fa-users icon"></i>
                <span class="label">动态</span>
            </a>
            <a href="./profile.html" class="nav-item">
                <i class="fas fa-user-circle icon"></i>
                <span class="label">我的</span>
            </a>
        </nav>
    </div>
</body>
</html>