<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>购买流程 - 小鹅养成APP</title>
    <link rel="stylesheet" href="../assets/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    <style>
        .mobile-frame {
            display: flex;
            justify-content: center;
            align-items: center;
            background: var(--gg-background-day);
            padding: var(--gg-page-padding);
            box-sizing: border-box;
        }

        .purchase-modal {
            background: rgba(255, 255, 255, 0.6); /* Frosted glass effect */
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            border-radius: var(--gg-radius-blob);
            border: 1px solid rgba(255, 255, 255, 0.2);
            padding: var(--gg-spacing-xl);
            width: 100%;
            max-width: 340px;
            box-shadow: var(--gg-shadow-gummy-idle);
            display: flex;
            flex-direction: column;
            gap: var(--gg-spacing-lg);
        }

        .goose-preview {
            text-align: center;
        }

        .goose-preview-model {
            width: 120px;
            height: 120px;
            background: var(--gg-color-secondary-breath);
            border-radius: 50%;
            display: flex;
            justify-content: center;
            align-items: center;
            margin: 0 auto;
            font-size: 60px;
            color: var(--gg-color-primary-gold);
            box-shadow: inset 0 2px 4px rgba(0,0,0,0.05);
        }
        
        .goose-name {
            font-family: var(--gg-font-family-heading);
            font-weight: var(--gg-font-weight-bold);
            font-size: var(--gg-font-size-title);
            margin-top: var(--gg-spacing-md);
            color: var(--gg-color-text-primary);
        }

        .summary-section, .payment-section {
            background: rgba(255, 255, 255, 0.5);
            padding: var(--gg-spacing-md);
            border-radius: var(--gg-radius-gummy);
        }

        .section-title {
            font-family: var(--gg-font-family-heading);
            font-weight: var(--gg-font-weight-medium);
            font-size: var(--gg-font-size-body-large);
            color: var(--gg-color-text-secondary);
            margin-bottom: var(--gg-spacing-md);
        }

        .summary-item {
            display: flex;
            justify-content: space-between;
            font-size: var(--gg-font-size-body);
            color: var(--gg-color-text-primary);
            margin-bottom: var(--gg-spacing-sm);
        }
        
        .summary-item.total {
            font-weight: var(--gg-font-weight-bold);
            margin-top: var(--gg-spacing-sm);
            padding-top: var(--gg-spacing-sm);
            border-top: 1px solid rgba(0,0,0,0.05);
        }

        .payment-method {
            display: flex;
            align-items: center;
            justify-content: space-between;
            font-size: var(--gg-font-size-body);
        }
        
        .payment-method span:first-child{
            font-weight: var(--gg-font-weight-medium);
        }

        .confirm-button {
            margin-top: var(--gg-spacing-md);
        }

    </style>
</head>
<body>
    <div class="mobile-frame">
        <div class="purchase-modal">
            <div class="goose-preview">
                <div class="goose-preview-model">🦆</div>
                <h2 class="goose-name">阳光小鹅</h2>
            </div>
    
            <div class="summary-section">
                <h3 class="section-title">订单摘要</h3>
                <div class="summary-item">
                    <span>商品价格</span>
                    <span>¥ 99.00</span>
                </div>
                <div class="summary-item">
                    <span>优惠券</span>
                    <span>- ¥ 10.00</span>
                </div>
                <div class="summary-item total">
                    <span>总计</span>
                    <span>¥ 89.00</span>
                </div>
            </div>
    
            <div class="payment-section">
                <h3 class="section-title">支付方式</h3>
                <div class="payment-method">
                    <span>微信支付</span>
                    <span>></span>
                </div>
            </div>
            
            <button class="gummy-button confirm-button">确认支付 ¥89.00</button>
        </div>
    </div>
    <nav class="global-nav">
        <a href="./discover.html" class="nav-item">
            <i class="fas fa-compass icon"></i>
            <span class="label">发现</span>
        </a>
        <a href="./shop.html" class="nav-item">
            <i class="fas fa-store icon"></i>
            <span class="label">商城</span>
        </a>
        <a href="./my-geese.html" class="nav-item">
            <i class="fas fa-egg icon"></i>
            <span class="label">我的鹅</span>
        </a>
        <a href="./social-feed.html" class="nav-item">
            <i class="fas fa-users icon"></i>
            <span class="label">动态</span>
        </a>
        <a href="./profile.html" class="nav-item">
            <i class="fas fa-user-circle icon"></i>
            <span class="label">我的</span>
        </a>
    </nav>
</body>
</html>