<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>设置</title>
    <link rel="stylesheet" href="../assets/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    <style>
        .mobile-frame {
            display: flex;
            flex-direction: column;
            padding: 0;
            box-sizing: border-box;
            gap: var(--gg-spacing-lg);
        }
        .header {
            display: flex;
            align-items: center;
            gap: var(--gg-spacing-md);
            padding: var(--gg-spacing-md) var(--gg-page-padding);
            border-bottom: 1px solid rgba(0,0,0,0.05);
        }
        .header .icon {
            font-size: 20px;
        }
        .header-title {
            font-size: var(--gg-font-size-title);
            font-weight: var(--gg-font-weight-bold);
        }
        .settings-container {
            padding: 0 var(--gg-page-padding);
            display: flex;
            flex-direction: column;
            gap: var(--gg-spacing-xl);
        }
        .settings-section {
            display: flex;
            flex-direction: column;
            gap: var(--gg-spacing-md);
        }
        .section-title {
            font-size: var(--gg-font-size-body);
            font-weight: bold;
            color: var(--gg-color-text-tertiary);
            padding-left: var(--gg-spacing-sm);
        }
        .menu-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: var(--gg-spacing-lg);
        }
        .menu-item .label {
            font-size: var(--gg-font-size-body-large);
            font-weight: 500;
        }
        .menu-item .chevron {
            color: var(--gg-color-text-tertiary);
        }
        .switch {
            width: 52px;
            height: 32px;
            background-color: rgba(0,0,0,0.1);
            border-radius: var(--gg-radius-full);
            position: relative;
            cursor: pointer;
            transition: background-color 0.3s ease;
        }
        .switch.active {
            background-color: var(--gg-color-accent-heal);
        }
        .switch-handle {
            width: 28px;
            height: 28px;
            background-color: white;
            border-radius: 50%;
            position: absolute;
            top: 2px;
            left: 2px;
            transition: transform 0.3s ease;
            box-shadow: 0 2px 4px rgba(0,0,0,0.2);
        }
        .switch.active .switch-handle {
            transform: translateX(20px);
        }
        .logout-button {
            margin-top: var(--gg-spacing-xl);
            background-color: var(--gg-color-accent-coral);
            color: white;
        }
    </style>
</head>
<body>
    <div class="mobile-frame">
        <header class="header">
            <i class="fas fa-arrow-left icon"></i>
            <h1 class="header-title">设置</h1>
        </header>

        <div class="settings-container">
            <div class="settings-section">
                <div class="section-title">账户</div>
                <div class="glass-card">
                    <div class="menu-item">
                        <span class="label">编辑个人资料</span>
                        <i class="fas fa-chevron-right chevron"></i>
                    </div>
                    <div class="menu-item">
                        <span class="label">账号与安全</span>
                        <i class="fas fa-chevron-right chevron"></i>
                    </div>
                </div>
            </div>

            <div class="settings-section">
                <div class="section-title">通知</div>
                <div class="glass-card">
                    <div class="menu-item">
                        <span class="label">好友动态提醒</span>
                        <div class="switch active"><div class="switch-handle"></div></div>
                    </div>
                    <div class="menu-item">
                        <span class="label">小鹅状态提醒</span>
                        <div class="switch active"><div class="switch-handle"></div></div>
                    </div>
                     <div class="menu-item">
                        <span class="label">夜间免打扰</span>
                        <div class="switch"><div class="switch-handle"></div></div>
                    </div>
                </div>
            </div>
            
            <div class="settings-section">
                 <div class="section-title">通用</div>
                <div class="glass-card">
                    <div class="menu-item">
                        <span class="label">关于我们</span>
                        <i class="fas fa-chevron-right chevron"></i>
                    </div>
                </div>
            </div>

            <button class="gummy-button logout-button">退出登录</button>
        </div>
    </div>
    <script>
        document.querySelectorAll('.switch').forEach(item => {
            item.addEventListener('click', event => {
                item.classList.toggle('active');
            });
        });
    </script>
</body>
</html>