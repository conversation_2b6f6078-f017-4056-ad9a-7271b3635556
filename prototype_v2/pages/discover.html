<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>发现</title>
    <link rel="stylesheet" href="../assets/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    <style>
        .mobile-frame {
            padding: 0;
        }
        .page-content {
            display: flex;
            flex-direction: column;
            gap: var(--gg-spacing-xl);
            padding-bottom: 100px; /* Ensure content doesn't hide behind nav */
        }
        .discover-header {
            padding: var(--gg-spacing-md) var(--gg-page-padding);
            padding-bottom: 0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .discover-title {
            font-size: var(--gg-font-size-display);
            font-weight: var(--gg-font-weight-bold);
        }
        .discover-icon {
            font-size: 24px;
        }
        .banner-card {
            height: 180px;
            border-radius: var(--gg-radius-blob);
            background: linear-gradient(45deg, var(--gg-color-primary-gold), var(--gg-color-accent-coral));
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            color: white;
            padding: var(--gg-spacing-lg);
            text-align: center;
            box-shadow: var(--gg-shadow-gummy-idle);
            margin: 0 var(--gg-page-padding);
        }
        .banner-title {
            font-size: var(--gg-font-size-title);
            font-weight: bold;
        }
        .banner-subtitle {
            font-size: var(--gg-font-size-body);
            opacity: 0.8;
        }
        .section-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 var(--gg-page-padding);
        }
        .section-title {
            font-size: var(--gg-font-size-title);
            font-weight: bold;
        }
        .section-more {
            font-size: var(--gg-font-size-caption);
            color: var(--gg-color-text-tertiary);
        }
        .horizontal-scroll {
            display: flex;
            gap: var(--gg-spacing-md);
            overflow-x: auto;
            padding: 0 var(--gg-page-padding);
            padding-bottom: var(--gg-spacing-md); /* For shadow visibility */
            -ms-overflow-style: none;
            scrollbar-width: none;
        }
        .horizontal-scroll::-webkit-scrollbar {
            display: none;
        }
        .goose-card {
            width: 140px;
            flex-shrink: 0;
        }
        .goose-image {
            height: 100px;
            width: 100%;
            background-color: #fff;
            border-radius: var(--gg-radius-gummy);
            background-image: url('https://i.imgur.com/kMv342I.png');
            background-size: contain;
            background-position: center;
            background-repeat: no-repeat;
            margin-bottom: var(--gg-spacing-sm);
        }
        .goose-name {
            font-weight: bold;
        }
        .goose-price {
            color: var(--gg-color-text-secondary);
            font-size: var(--gg-font-size-caption);
        }
        .feed-card {
            padding: var(--gg-spacing-md);
            margin: 0 var(--gg-page-padding);
        }
        .feed-header {
            display: flex;
            align-items: center;
            gap: var(--gg-spacing-sm);
            margin-bottom: var(--gg-spacing-md);
        }
        .avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: var(--gg-color-secondary-sky);
        }
        .user-info .name {
            font-weight: bold;
        }
        .user-info .time {
            font-size: var(--gg-font-size-caption);
            color: var(--gg-color-text-tertiary);
        }
        .feed-content {
            margin-bottom: var(--gg-spacing-md);
        }
        .feed-image {
            width: 100%;
            height: 150px;
            border-radius: var(--gg-radius-gummy);
            background: var(--gg-color-secondary-breath) url('https://i.imgur.com/xCE2N6y.gif') center/cover;
        }
    </style>
</head>
<body>
    <div class="mobile-frame">
        <div class="page-content">
            <header class="discover-header">
                <div class="discover-title">发现</div>
                <i class="fas fa-search discover-icon"></i>
            </header>
            
            <div class="banner-card">
                <h2 class="banner-title">夏日特别活动</h2>
                <p class="banner-subtitle">领养你的夏日专属小鹅伙伴！</p>
            </div>

            <section>
                <div class="section-header">
                    <h3 class="section-title">今日推荐</h3>
                    <span class="section-more">更多 ></span>
                </div>
                <div class="horizontal-scroll">
                    <div class="glass-card goose-card">
                        <div class="goose-image"></div>
                        <div class="goose-name">小白鹅</div>
                        <div class="goose-price">¥199</div>
                    </div>
                     <div class="glass-card goose-card">
                        <div class="goose-image" style="background-image: url('https://i.imgur.com/L7p8U5A.png');"></div>
                        <div class="goose-name">小灰鹅</div>
                        <div class="goose-price">¥299</div>
                    </div>
                     <div class="glass-card goose-card">
                        <div class="goose-image" style="background-image: url('https://i.imgur.com/kMv342I.png');"></div>
                        <div class="goose-name">小花鹅</div>
                        <div class="goose-price">¥399</div>
                    </div>
                </div>
            </section>
            
            <section>
                <div class="section-header">
                    <h3 class="section-title">鹅友动态</h3>
                    <span class="section-more">更多 ></span>
                </div>
                <div class="glass-card feed-card">
                    <div class="feed-header">
                        <div class="avatar"></div>
                        <div class="user-info">
                            <div class="name">小明</div>
                            <div class="time">2小时前</div>
                        </div>
                    </div>
                    <p class="feed-content">我的小鹅今天学会游泳了！开心！</p>
                    <div class="feed-image"></div>
                </div>
            </section>

        </div>
        <nav class="global-nav">
            <a href="./discover.html" class="nav-item active">
                <i class="fas fa-compass icon"></i>
                <span class="label">发现</span>
            </a>
            <a href="./shop.html" class="nav-item">
                <i class="fas fa-store icon"></i>
                <span class="label">商城</span>
            </a>
            <a href="./my-geese.html" class="nav-item">
                <i class="fas fa-egg icon"></i>
                <span class="label">我的鹅</span>
            </a>
            <a href="./social-feed.html" class="nav-item">
                <i class="fas fa-users icon"></i>
                <span class="label">动态</span>
            </a>
            <a href="./profile.html" class="nav-item">
                <i class="fas fa-user-circle icon"></i>
                <span class="label">我的</span>
            </a>
        </nav>
    </div>
</body>
</html>