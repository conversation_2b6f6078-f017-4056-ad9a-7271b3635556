<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>我的鹅</title>
    <link rel="stylesheet" href="../assets/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    <style>
        .page-content {
            padding: 0;
            overflow: hidden; /* Important for slider */
            display: flex; /* Make it a flex container */
            flex-direction: column; /* Stack children vertically */
        }
        .goose-slider {
            display: flex;
            overflow-x: auto;
            scroll-snap-type: x mandatory; /* The magic for snapping */
            -webkit-overflow-scrolling: touch; /* Smooth scrolling on iOS */
            height: 100%;
            scrollbar-width: none; /* Hide scrollbar for Firefox */
        }
        .goose-slider::-webkit-scrollbar {
            display: none; /* Hide scrollbar for Chrome, Safari, and Opera */
        }
        .goose-card {
            flex: 0 0 100%; /* Each card takes full width */
            width: 100%;
            height: 100%;
            scroll-snap-align: center; /* Snap to the center */
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: var(--gg-page-padding);
            box-sizing: border-box;
            gap: var(--gg-spacing-lg);
            position: relative;
        }

        .goose-avatar {
            width: 220px;
            height: 220px;
            animation: float 4s ease-in-out infinite;
        }
        
        .goose-card:nth-child(2) .goose-avatar { animation-delay: -1s; }
        .goose-card:nth-child(3) .goose-avatar { animation-delay: -2s; }

        @keyframes float {
            0%, 100% { transform: translateY(0); }
            50% { transform: translateY(-20px); }
        }

        .goose-avatar img {
            width: 100%;
            height: 100%;
            object-fit: contain;
            filter: drop-shadow(0 10px 15px rgba(0,0,0,0.15));
        }

        .goose-info {
            text-align: center;
        }

        .goose-name {
            font-size: var(--gg-font-size-display);
            font-weight: var(--gg-font-weight-bold);
            color: var(--gg-color-text-primary);
        }
        .goose-status {
            background-color: var(--gg-color-accent-heal);
            color: var(--gg-color-text-primary);
            font-size: var(--gg-font-size-caption);
            font-weight: var(--gg-font-weight-medium);
            padding: var(--gg-spacing-xs) var(--gg-spacing-md);
            border-radius: var(--gg-radius-full);
            display: inline-block;
            margin-top: var(--gg-spacing-sm);
        }

        .interaction-buttons {
            display: flex;
            gap: var(--gg-spacing-md);
        }
        
        .pagination {
            position: absolute;
            bottom: var(--gg-spacing-xl);
            left: 50%;
            transform: translateX(-50%);
            display: flex;
            gap: 8px;
        }
        .dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background-color: rgba(0,0,0,0.15);
            transition: all 0.3s ease;
        }
        .dot.active {
            background-color: var(--gg-color-primary-gold);
            transform: scale(1.2);
        }

    </style>
</head>
<body>
    <div class="mobile-frame">
        <div class="page-content">
            <div class="goose-slider" id="goose-slider">
                <!-- Goose Card 1 -->
                <div class="goose-card" data-index="0">
                    <div class="goose-avatar">
                        <img src="https://i.imgur.com/kMv342I.png" alt="豆豆">
                    </div>
                    <div class="goose-info">
                        <h1 class="goose-name">豆豆</h1>
                        <div class="goose-status">非常开心</div>
                    </div>
                    <div class="interaction-buttons">
                        <button class="gummy-button"><i class="fas fa-utensils"></i> 喂食</button>
                        <button class="gummy-button"><i class="fas fa-hand-sparkles"></i> 互动</button>
                    </div>
                </div>
    
                <!-- Goose Card 2 -->
                <div class="goose-card" data-index="1">
                    <div class="goose-avatar">
                        <img src="https://i.imgur.com/L7p8U5A.png" alt="灰灰">
                    </div>
                    <div class="goose-info">
                        <h1 class="goose-name">灰灰</h1>
                        <div class="goose-status">正在打盹</div>
                    </div>
                    <div class="interaction-buttons">
                         <button class="gummy-button"><i class="fas fa-utensils"></i> 喂食</button>
                        <button class="gummy-button"><i class="fas fa-hand-sparkles"></i> 互动</button>
                    </div>
                </div>
    
                <!-- Goose Card 3 -->
                <div class="goose-card" data-index="2">
                     <div class="goose-avatar">
                        <img src="https://i.imgur.com/kMv342I.png" alt="花花">
                    </div>
                    <div class="goose-info">
                        <h1 class="goose-name">花花</h1>
                        <div class="goose-status">有点饿了</div>
                    </div>
                    <div class="interaction-buttons">
                         <button class="gummy-button accent-glow"><i class="fas fa-utensils"></i> 喂食</button>
                        <button class="gummy-button"><i class="fas fa-hand-sparkles"></i> 互动</button>
                    </div>
                </div>
            </div>
            <div class="pagination" id="pagination"></div>
        </div>
        <nav class="global-nav">
            <a href="./discover.html" class="nav-item">
                <i class="fas fa-compass icon"></i>
                <span class="label">发现</span>
            </a>
            <a href="./shop.html" class="nav-item">
                <i class="fas fa-store icon"></i>
                <span class="label">商城</span>
            </a>
            <a href="./my-geese.html" class="nav-item active">
                <i class="fas fa-egg icon"></i>
                <span class="label">我的鹅</span>
            </a>
            <a href="./social-feed.html" class="nav-item">
                <i class="fas fa-users icon"></i>
                <span class="label">动态</span>
            </a>
            <a href="./profile.html" class="nav-item">
                <i class="fas fa-user-circle icon"></i>
                <span class="label">我的</span>
            </a>
        </nav>
    </div>
    <script>
        const slider = document.getElementById('goose-slider');
        const pagination = document.getElementById('pagination');
        const cards = slider.querySelectorAll('.goose-card');

        // Create pagination dots
        cards.forEach((card, index) => {
            const dot = document.createElement('div');
            dot.className = 'dot';
            dot.dataset.index = index;
            // Set initial active dot based on the first card in view
            if (index === 0) {
                 dot.classList.add('active');
            }
            pagination.appendChild(dot);
        });

        // Update active dot on scroll
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const index = entry.target.dataset.index;
                    document.querySelectorAll('.dot').forEach(d => d.classList.remove('active'));
                    const activeDot = document.querySelector(`.dot[data-index="${index}"]`);
                    if (activeDot) {
                        activeDot.classList.add('active');
                    }
                }
            });
        }, { root: slider, threshold: 0.5 });

        cards.forEach(card => {
            observer.observe(card);
        });
    </script>
</body>
</html>