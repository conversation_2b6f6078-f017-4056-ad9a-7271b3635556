<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>小鹅养成APP - 原型v2 - Gummy Glassmorphism</title>
    <link rel="stylesheet" href="assets/style.css">
    <style>
        html, body {
            margin: 0;
            padding: 0;
            width: 100%;
            height: 100%;
            overflow: hidden; /* Hide body scrollbars */
            background-color: #F0F2F5;
            font-family: sans-serif;
        }

        #prototype-grid {
            display: flex;
            flex-wrap: nowrap; /* Key change for horizontal layout */
            gap: var(--gg-spacing-xxl);
            padding: var(--gg-spacing-xxl);
            height: 100%;
            box-sizing: border-box;
            overflow-x: auto; /* Enable horizontal scrolling */
            overflow-y: hidden;
            align-items: center; /* Vertically center the prototypes */
        }

        .prototype-container {
            flex-shrink: 0; /* Prevent items from shrinking */
        }

        .prototype-title {
            font-family: var(--gg-font-family-heading);
            font-size: var(--gg-font-size-title);
            color: var(--gg-color-text-primary);
            text-align: center;
            margin-bottom: var(--gg-spacing-md);
            font-weight: 600;
        }

        .prototype-wrapper {
            transform: scale(0.8);
            transform-origin: center center;
            box-shadow: 0 10px 40px rgba(0,0,0,0.15);
            border-radius: 48px; /* a bit larger than iframe for shadow */
            background: #fff;
            padding: 8px;
        }
    </style>
</head>
<body>

    <div id="prototype-grid">
        <!-- 原型页面将通过JS动态加载到这里 -->
    </div>

    <script>
        const pages = [
            { title: "登录页 (Login)", file: "pages/login.html" },
            { title: "小鹅详情页 (Goose Detail)", file: "pages/goose-detail.html" },
            { title: "商城页 (Shop)", file: "pages/shop.html" },
            { title: "发现页 (Discover)", file: "pages/discover.html" },
            { title: "我的鹅页 (My Geese)", file: "pages/my-geese.html" },
            { title: "动态页 (Social Feed)", file: "pages/social-feed.html" },
            { title: "我的页 (Profile)", file: "pages/profile.html" },
            { title: "转赠流程页 (Gifting Flow)", file: "pages/gifting-flow.html" },
            { title: "设置页面 (Settings)", file: "pages/settings.html" },
            { title: "购买流程页 (Purchase Flow)", file: "pages/purchase-flow.html" },
        ];

        const grid = document.getElementById('prototype-grid');

        pages.forEach(page => {
            const container = document.createElement('div');
            container.className = 'prototype-container';

            const title = document.createElement('h2');
            title.className = 'prototype-title';
            title.textContent = page.title;

            const wrapper = document.createElement('div');
            wrapper.className = 'prototype-wrapper';

            const iframe = document.createElement('iframe');
            iframe.src = page.file;
            iframe.width = 375;
            iframe.height = 812;
            iframe.style.border = 'none';
            iframe.style.borderRadius = '40px';
            iframe.scrolling = 'no';

            wrapper.appendChild(iframe);
            container.appendChild(title);
            container.appendChild(wrapper);
            grid.appendChild(container);
        });

        // Removed dynamic scaling function as it's not suitable for horizontal layout.
    </script>

</body>
</html>