@import url('https://fonts.googleapis.com/css2?family=Varela+Round&family=Inter:wght@400;500;700&display=swap');

:root {
    /* --- v2.0 Design System: Gummy Glassmorphism --- */

    /* 1. Color System: Emotional & Healing */
    --gg-color-primary-gold: #FFD700;
    --gg-color-primary-gummy: #FFEB3B;
    --gg-color-secondary-sky: #87CEEB;
    --gg-color-secondary-breath: #E3F2FD;
    --gg-color-accent-coral: #FFA07A;
    --gg-color-accent-heal: #98FB98;

    --gg-color-text-primary: #1F2937;
    --gg-color-text-secondary: #6B7280;
    --gg-color-text-tertiary: #9CA3AF;

    --gg-background-day: linear-gradient(180deg, #E3F2FD 0%, #FFFDE7 100%);
    --gg-background-night: linear-gradient(180deg, #1F2937 0%, #37474F 100%);
    
    --gg-gradient-health: linear-gradient(90deg, #98FB98 0%, #32CD32 100%);
    --gg-gradient-hunger: linear-gradient(90deg, #FFA07A 0%, #FF7F50 100%);
    --gg-gradient-clean: linear-gradient(90deg, #87CEEB 0%, #4682B4 100%);

    /* 2. Typography: Friendly & Clear */
    --gg-font-family-body: 'Varela Round', 'PingFang SC', sans-serif;
    --gg-font-family-heading: 'Inter', 'Varela Round', sans-serif;
    --gg-font-size-display: 36px;
    --gg-font-size-title: 22px;
    --gg-font-size-body-large: 17px;
    --gg-font-size-body: 15px;
    --gg-font-size-caption: 13px;
    --gg-font-weight-regular: 400;
    --gg-font-weight-medium: 500;
    --gg-font-weight-bold: 700;

    /* 3. Layout: Breathable & Layered */
    --gg-radius-full: 9999px;
    --gg-radius-blob: 32px;
    --gg-radius-gummy: 20px;
    --gg-radius-soft: 12px;

    --gg-spacing-xs: 4px;
    --gg-spacing-sm: 8px;
    --gg-spacing-md: 16px;
    --gg-spacing-lg: 24px;
    --gg-spacing-xl: 32px;
    --gg-spacing-xxl: 48px;

    --gg-page-padding: 24px;
    
    /* 4. Shadow System: The Art of Light */
    --gg-shadow-glass: inset 0 1px 1px 0 rgba(255, 255, 255, 0.5);
    --gg-shadow-gummy-idle: 0 4px 12px -2px rgba(25, 40, 50, 0.15), 0 2px 8px -2px rgba(25, 40, 50, 0.1);
    --gg-shadow-gummy-press: 0 1px 4px -1px rgba(25, 40, 50, 0.15), 0 1px 4px -1px rgba(25, 40, 50, 0.1);
    --gg-glow-primary: 0 0 20px 0 rgba(255, 235, 59, 0.5);
    --gg-glow-accent: 0 0 20px 0 rgba(255, 160, 122, 0.5);
}

/* --- Global Styles --- */
body {
    margin: 0;
    font-family: var(--gg-font-family-body);
    font-size: var(--gg-font-size-body);
    color: var(--gg-color-text-primary);
    background: #F4F6F9; /* A neutral background for the container page */
}

h1, h2, h3 {
    font-family: var(--gg-font-family-heading);
    font-weight: var(--gg-font-weight-bold);
    color: var(--gg-color-text-primary);
}

/* --- Core Components --- */

.mobile-frame {
    width: 375px;
    height: 812px;
    border: 8px solid #1F2937;
    border-radius: 40px;
    background: var(--gg-background-day);
    box-shadow: 0 20px 50px -10px rgba(0,0,0,0.3);
    overflow: hidden;
    position: relative;
    display: flex;
    flex-direction: column;
    color: var(--gg-color-text-primary);
}
.page-content {
    flex: 1; /* Allow content to grow and fill available space */
    padding: var(--gg-page-padding);
    box-sizing: border-box;
    overflow-y: auto; /* Allow content to scroll if it overflows */
}

.glass-card {
    background: rgba(255, 255, 255, 0.4);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border-radius: var(--gg-radius-blob);
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: var(--gg-shadow-glass);
    padding: var(--gg-spacing-lg);
}

.gummy-button {
    background: var(--gg-color-primary-gold);
    color: var(--gg-color-text-primary);
    font-family: var(--gg-font-family-heading);
    font-weight: var(--gg-font-weight-bold);
    font-size: var(--gg-font-size-body-large);
    border: none;
    border-radius: var(--gg-radius-gummy);
    padding: var(--gg-spacing-md) var(--gg-spacing-xl);
    cursor: pointer;
    box-shadow: var(--gg-shadow-gummy-idle);
    transition: all 0.15s ease-out;
}

.gummy-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 16px -3px rgba(25, 40, 50, 0.2), 0 4px 8px -3px rgba(25, 40, 50, 0.12);
}

.gummy-button:active {
    transform: translateY(1px);
    box-shadow: var(--gg-shadow-gummy-press);
}

.accent-glow {
    box-shadow: var(--gg-glow-accent);
}
/* --- Global Navigation Bar --- */
.global-nav {
    position: absolute;
    bottom: 16px;
    left: 16px;
    right: 16px;
    height: 70px;
    background: rgba(255, 255, 255, 0.3); /*降低不透明度，增强通透感*/
    backdrop-filter: blur(25px); /*增加模糊，使其更柔和*/
    -webkit-backdrop-filter: blur(20px);
    border-radius: var(--gg-radius-blob);
    border-top: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: var(--gg-shadow-gummy-idle);
    display: flex;
    justify-content: space-around;
    align-items: center;
    padding-bottom: env(safe-area-inset-bottom); /* For iPhone X notch */
    z-index: 1000;
}

.nav-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: var(--gg-spacing-xs);
    color: var(--gg-color-text-secondary);
    text-decoration: none;
    transition: all 0.2s ease-in-out;
    position: relative;
    padding: var(--gg-spacing-sm);
}

.nav-item .icon {
    font-size: 22px;
}

.nav-item .label {
    font-size: 11px;
    font-weight: var(--gg-font-weight-medium);
}

.nav-item:hover {
    transform: scale(1.1);
    color: var(--gg-color-text-primary);
}

.nav-item.active {
    color: var(--gg-color-primary-gold);
}

.nav-item.active::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 50%;
    transform: translateX(-50%);
    width: 24px;
    height: 4px;
    background-color: var(--gg-color-primary-gold);
    border-radius: var(--gg-radius-full);
    box-shadow: var(--gg-glow-primary);
}