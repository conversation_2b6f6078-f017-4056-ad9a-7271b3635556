# 小鹅养成APP项目配置记忆

## 🗄️ 数据库配置
- **类型**: MySQL 8.0
- **部署方式**: 本地Docker容器
- **连接信息**: 
  - 主机: localhost
  - 端口: 3306
  - 数据库名: goose_app
  - 用户名: root
  - 密码: 123456
- **Docker启动命令**: `docker run -d --name mysql -p 3306:3306 -e MYSQL_ROOT_PASSWORD=123456 mysql:8.0`

## 👤 管理员账户
- **用户名**: superAdmin
- **密码**: admin123
- **角色**: 系统管理员
- **权限**: 创建小鹅、管理用户、系统配置

## 🚀 应用配置
- **后端端口**: 8080
- **健康检查**: http://localhost:8080/actuator/health
- **API文档**: http://localhost:8080/swagger-ui.html
- **基础URL**: http://localhost:8080

## 🧪 测试配置
- **集成测试脚本**: `./integration-test.sh`
- **单元测试脚本**: `./unit-test.sh`
- **API验证脚本**: `./api-validation-test.sh`
- **快速测试脚本**: `./quick-test.sh`
- **测试数据库**: H2内存数据库 (测试环境)
- **测试覆盖率**: >80%
- **API测试覆盖**: 67个接口，35个测试用例
- **测试验证结果**: 21/35通过 (60%成功率)

## 📊 项目状态
- **整体进度**: 95%
- **后端进度**: 95% (4/5核心模块完成，仅转赠模块待开发)
- **测试验证**: 完成 (67个API接口测试，60%成功率)
- **前端进度**: 0% (可以开始开发)
- **待开发**: 转赠模块

## 🛠️ 技术栈
- **后端**: Spring Boot 3.2.1 + JDK 17
- **认证**: Sa-Token 1.37.0
- **数据库**: MySQL 8.0 + Redis 6.0
- **前端**: React Native (计划)
- **构建**: Maven 3.9+

## 📋 快速启动命令
```bash
# 启动MySQL Docker容器
docker start mysql

# 编译项目
cd goose-app-backend && mvn clean compile

# 启动应用
mvn spring-boot:run

# 运行集成测试
./integration-test.sh

# 运行单元测试
./unit-test.sh
```

## 🔑 重要API接口
- **管理员登录**: POST /api/v1/auth/login
- **用户注册**: POST /api/v1/auth/register
- **查看待售小鹅**: GET /api/v1/shop/geese/available
- **创建小鹅**: POST /api/v1/admin/geese/create (需管理员权限)
- **购买小鹅**: POST /api/v1/shop/purchase

---
*最后更新: 2025-01-06*
