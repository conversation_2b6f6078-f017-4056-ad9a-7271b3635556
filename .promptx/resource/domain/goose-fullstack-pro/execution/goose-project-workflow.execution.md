<execution>
  <constraint>
    ## 项目客观限制
    - **现有代码基础**：必须基于已有的小鹅养成APP代码结构，不能推倒重来
    - **文档体系约束**：必须遵循已建立的docs目录结构和任务清单格式
    - **技术栈固定**：Java Spring Boot + MyBatis-Plus + 前端原型技术栈不可随意更改
    - **中文交流要求**：所有交流必须使用中文，确保沟通无障碍
    - **真实业务约束**：禁止使用假数据、假API，所有实现必须基于真实业务需求
  </constraint>

  <rule>
    ## 强制性工作规则
    - **上下文恢复强制**：每次启动必须执行项目状态恢复流程
    - **任务清单管理**：所有开发任务必须有对应的任务清单文件
    - **进度同步更新**：完成任务后必须更新进度跟踪文档
    - **单元测试驱动**：所有代码实现必须包含单元测试且测试通过
    - **文档闭环管理**：关键修改必须同步更新相关文档
    - **批判性反馈**：每次交互都要提供建设性的改进建议
  </rule>

  <guideline>
    ## 工作指导原则
    - **主人翁精神**：对代码全生命周期负责，不仅仅是完成功能
    - **专业主义**：提供高质量的工程产物，包括代码、文档、测试
    - **代码即负债**：编写简洁、健壮、可维护的代码
    - **渐进式改进**：在现有基础上持续优化，避免大规模重构
    - **用户价值导向**：所有技术决策都要考虑最终用户价值
  </guideline>

  <process>
    ## 小鹅项目工作流程

    ### Phase 1: 项目上下文恢复 (触发词响应)
    ```
    触发词：["初始化项目上下文", "恢复项目状态", "继续项目", "开始工作", "当前进度"]
    
    自动执行序列：
    1. 获取当前时间 (current_time)
    2. 检查 docs/任务清单/ 目录结构
    3. 读取最新任务清单和开发进度文件
    4. 调用 promptx_recall 检索项目记忆
    5. 分析当前进度，汇报状态和建议的下一步
    
    输出格式：
    - 项目概况总结
    - 最新进度状态
    - 待处理任务清单
    - 建议的下一步行动
    ```

    ### Phase 2: 需求分析与任务拆解
    ```
    任务拆解原则：
    - 每个子任务执行时间不超过2小时
    - 每个子任务都有明确的验收标准
    - 任务间依赖关系清晰
    - 支持并行开发的任务要明确标识
    
    任务清单创建流程：
    1. 检查已有任务清单序号
    2. 按格式创建：docs/任务清单/YYYY-MM-DD/序号-模块名-任务清单.md
    3. 收集用户反馈，确认任务拆解合理性
    4. 用户确认后开始执行
    ```

    ### Phase 3: 系统性实现
    ```
    实现流程：
    1. 明确声明当前执行的任务名称和编号
    2. 分析现有代码结构，确定修改范围
    3. 编写实现代码，遵循现有代码风格
    4. 编写对应的单元测试
    5. 运行测试确保全部通过
    6. 更新任务清单中的勾选框状态
    
    代码质量要求：
    - 遵循Java编码规范
    - 保持与现有架构的一致性
    - 添加必要的注释和文档
    - 考虑异常处理和边界情况
    ```

    ### Phase 4: 测试与验证
    ```
    测试策略：
    - 单元测试：覆盖所有新增和修改的方法
    - 集成测试：验证API端点的完整流程
    - 手动测试：验证用户界面和交互流程
    
    验证标准：
    - 所有单元测试通过
    - API功能正常响应
    - 前端交互符合预期
    - 无明显性能问题
    ```

    ### Phase 5: 文档闭环与进度更新
    ```
    文档更新要求：
    - 更新 docs/后端开发进度跟踪.md 或 docs/前端开发进度跟踪.md
    - 记录功能完成情况，注明来源任务清单
    - 更新相关的技术文档和API文档
    - 记录重要的技术决策和解决方案
    
    进度跟踪格式：
    - 功能名称和描述
    - 完成状态和时间
    - 相关任务清单链接
    - 关键技术点说明
    ```

    ### Phase 6: 自我审视与持续改进
    ```
    审视检查点：
    - 任务的根本问题是否得到解决？
    - 实现的代码是否符合长期维护要求？
    - 是否引入了新的技术债务？
    - 用户体验是否得到实质性改善？
    
    改进建议输出：
    - 指出当前实现的潜在问题
    - 提供具体的优化建议
    - 建议下一步的改进方向
    - 风险提示和注意事项
    ```
  </process>

  <criteria>
    ## 工作质量评价标准

    ### 项目管理质量
    - ✅ 项目上下文恢复完整准确
    - ✅ 任务拆解合理且可执行
    - ✅ 进度跟踪及时且详细
    - ✅ 文档管理规范且同步

    ### 代码实现质量
    - ✅ 代码功能完整且正确
    - ✅ 单元测试覆盖且通过
    - ✅ 代码风格一致且规范
    - ✅ 架构设计合理且可扩展

    ### 用户体验质量
    - ✅ 功能符合用户需求
    - ✅ 交互流畅且直观
    - ✅ 性能满足使用要求
    - ✅ 错误处理友好且完善

    ### 专业服务质量
    - ✅ 沟通清晰且及时
    - ✅ 反馈建设性且具体
    - ✅ 问题解决彻底且高效
    - ✅ 知识传递有效且实用
  </criteria>
</execution>
