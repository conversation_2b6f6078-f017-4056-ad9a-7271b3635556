<execution>
  <constraint>
    ## 质量标准客观限制
    - **技术栈兼容性**：质量标准必须适配Java Spring Boot + 前端原型的技术栈
    - **项目规模约束**：标准必须适合中小型项目，不能过度工程化
    - **团队能力边界**：标准要考虑实际的开发和维护能力
    - **业务发展阶段**：标准要适配MVP到成熟产品的不同阶段
  </constraint>

  <rule>
    ## 强制性质量规则
    - **代码审查强制**：所有代码变更必须经过自我审查
    - **测试覆盖强制**：新增功能必须有对应的单元测试
    - **文档同步强制**：API变更必须同步更新文档
    - **性能基线强制**：不能引入明显的性能退化
    - **安全基线强制**：必须遵循基本的安全编码规范
  </rule>

  <guideline>
    ## 质量指导原则
    - **渐进式质量提升**：在现有基础上持续改进，不追求一步到位
    - **实用性优先**：质量标准要有实际指导价值，不能流于形式
    - **可量化评估**：尽可能使用可量化的指标评估质量
    - **团队共识**：质量标准要得到团队认同和执行
  </guideline>

  <process>
    ## 全栈质量保证流程

    ### 后端代码质量标准
    ```
    代码结构质量：
    - ✅ 遵循Spring Boot项目标准结构
    - ✅ 包命名清晰，职责分离明确
    - ✅ 类和方法命名具有自解释性
    - ✅ 避免上帝类和上帝方法
    
    代码实现质量：
    - ✅ 单一职责原则，每个类职责明确
    - ✅ 依赖注入使用合理，避免循环依赖
    - ✅ 异常处理完善，错误信息有意义
    - ✅ 日志记录适当，便于问题排查
    
    数据访问质量：
    - ✅ SQL语句性能合理，避免N+1查询
    - ✅ 事务边界清晰，避免长事务
    - ✅ 数据验证完善，防止脏数据
    - ✅ 连接池配置合理，资源使用高效
    ```

    ### 前端代码质量标准
    ```
    用户界面质量：
    - ✅ 界面布局合理，符合用户习惯
    - ✅ 交互反馈及时，用户体验流畅
    - ✅ 响应式设计，适配不同屏幕尺寸
    - ✅ 加载状态明确，错误提示友好
    
    代码组织质量：
    - ✅ 组件拆分合理，复用性良好
    - ✅ 状态管理清晰，数据流向明确
    - ✅ 样式组织规范，避免样式冲突
    - ✅ 资源优化合理，加载性能良好
    
    交互体验质量：
    - ✅ 页面切换流畅，无明显卡顿
    - ✅ 表单验证及时，提示信息清晰
    - ✅ 数据展示直观，信息层次分明
    - ✅ 操作反馈明确，用户操作可预期
    ```

    ### API设计质量标准
    ```
    接口设计质量：
    - ✅ RESTful设计规范，资源定义清晰
    - ✅ HTTP状态码使用正确，语义明确
    - ✅ 请求参数验证完善，错误处理友好
    - ✅ 响应格式统一，数据结构合理
    
    性能质量：
    - ✅ 响应时间合理，一般接口<500ms
    - ✅ 并发处理能力满足业务需求
    - ✅ 数据分页合理，避免大数据量传输
    - ✅ 缓存策略适当，减少重复计算
    
    安全质量：
    - ✅ 身份认证机制完善，权限控制准确
    - ✅ 输入参数校验严格，防止注入攻击
    - ✅ 敏感数据保护，避免信息泄露
    - ✅ 日志记录安全，不记录敏感信息
    ```

    ### 测试质量标准
    ```
    单元测试质量：
    - ✅ 测试覆盖率 > 80%，核心业务逻辑 > 90%
    - ✅ 测试用例设计合理，覆盖正常和异常场景
    - ✅ 测试数据独立，不依赖外部环境
    - ✅ 测试执行快速，单个测试 < 100ms
    
    集成测试质量：
    - ✅ API端点测试完整，覆盖主要业务流程
    - ✅ 数据库操作测试，验证数据一致性
    - ✅ 外部依赖模拟，测试环境稳定
    - ✅ 错误场景测试，验证异常处理
    
    用户验收测试：
    - ✅ 核心功能流程验证，符合业务需求
    - ✅ 用户界面测试，交互体验良好
    - ✅ 兼容性测试，支持主流浏览器
    - ✅ 性能测试，满足用户使用要求
    ```

    ### 文档质量标准
    ```
    技术文档质量：
    - ✅ API文档完整，包含请求响应示例
    - ✅ 代码注释适当，关键逻辑有说明
    - ✅ 架构文档清晰，系统设计可理解
    - ✅ 部署文档详细，环境搭建可复现
    
    项目文档质量：
    - ✅ 需求文档明确，功能边界清晰
    - ✅ 设计文档完整，技术方案可执行
    - ✅ 测试文档详细，验证标准明确
    - ✅ 用户文档友好，操作指南易懂
    ```

    ### 部署运维质量标准
    ```
    部署质量：
    - ✅ 部署脚本自动化，减少人工错误
    - ✅ 环境配置标准化，开发生产一致
    - ✅ 版本管理规范，回滚机制完善
    - ✅ 健康检查完整，服务状态可监控
    
    监控质量：
    - ✅ 关键指标监控，性能问题可发现
    - ✅ 错误日志收集，问题排查有依据
    - ✅ 告警机制合理，重要问题及时通知
    - ✅ 数据备份完善，数据安全有保障
    ```
  </process>

  <criteria>
    ## 质量评价标准

    ### 代码质量评价
    - ✅ 功能完整性：实现的功能符合需求规格
    - ✅ 代码可读性：代码结构清晰，易于理解
    - ✅ 代码可维护性：修改和扩展成本合理
    - ✅ 代码可测试性：便于编写和执行测试

    ### 系统质量评价
    - ✅ 性能表现：响应时间和吞吐量满足要求
    - ✅ 稳定性：系统运行稳定，错误率低
    - ✅ 安全性：安全机制完善，风险可控
    - ✅ 可扩展性：架构支持业务发展需求

    ### 用户体验评价
    - ✅ 易用性：用户操作简单直观
    - ✅ 响应性：界面反馈及时流畅
    - ✅ 一致性：交互模式统一规范
    - ✅ 可访问性：支持不同用户群体

    ### 项目管理评价
    - ✅ 交付质量：按时按质完成任务
    - ✅ 文档完整性：技术文档齐全准确
    - ✅ 团队协作：沟通顺畅，配合高效
    - ✅ 持续改进：问题解决和优化及时
  </criteria>
</execution>
