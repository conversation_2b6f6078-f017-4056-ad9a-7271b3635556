#!/bin/bash

# 🦆 小鹅养成APP - 快速测试脚本
# 用于重构后代码验证

set -e

echo "🦆 ===== 小鹅养成APP 重构验证测试 ====="
echo "📅 测试时间: $(date)"
echo ""

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 状态检查函数
check_service() {
    local service=$1
    local check_cmd=$2
    
    echo -e "${BLUE}检查 $service 状态...${NC}"
    if eval $check_cmd > /dev/null 2>&1; then
        echo -e "${GREEN}✅ $service 运行正常${NC}"
        return 0
    else
        echo -e "${RED}❌ $service 未运行或异常${NC}"
        return 1
    fi
}

# 执行测试函数
run_test() {
    local test_name=$1
    local test_cmd=$2
    local expected_pattern=$3
    
    echo -e "${BLUE}执行测试: $test_name${NC}"
    
    local result=$(eval $test_cmd 2>&1)
    echo "$result"
    
    if echo "$result" | grep -q "$expected_pattern"; then
        echo -e "${GREEN}✅ 测试通过${NC}"
        return 0
    else
        echo -e "${RED}❌ 测试失败${NC}"
        return 1
    fi
    echo ""
}

# 1. 环境检查
echo "🔍 ===== 步骤1: 环境检查 ====="

# 检查必要工具
command -v mysql >/dev/null 2>&1 || { echo -e "${RED}❌ MySQL 未安装${NC}"; exit 1; }
command -v redis-cli >/dev/null 2>&1 || { echo -e "${RED}❌ Redis 未安装${NC}"; exit 1; }
command -v jq >/dev/null 2>&1 || { echo -e "${RED}❌ jq 未安装，请运行: brew install jq${NC}"; exit 1; }
command -v curl >/dev/null 2>&1 || { echo -e "${RED}❌ curl 未安装${NC}"; exit 1; }

echo -e "${GREEN}✅ 所有必要工具已安装${NC}"

# 检查服务状态
check_service "MySQL" "mysql -u root --execute='SELECT 1' 2>/dev/null"
mysql_status=$?

check_service "Redis" "redis-cli ping"
redis_status=$?

if [ $mysql_status -ne 0 ] || [ $redis_status -ne 0 ]; then
    echo -e "${YELLOW}⚠️  请启动必要服务后重新运行测试${NC}"
    echo "启动命令:"
    echo "  brew services start mysql"
    echo "  brew services start redis"
    exit 1
fi

# 2. 应用启动检查
echo ""
echo "🚀 ===== 步骤2: 应用启动检查 ====="

if curl -s http://localhost:8080/actuator/health > /dev/null; then
    echo -e "${GREEN}✅ 应用已启动 (端口8080)${NC}"
else
    echo -e "${YELLOW}⚠️  应用未启动，正在启动...${NC}"
    cd goose-app-backend
    
    # 后台启动应用
    nohup mvn spring-boot:run > ../app.log 2>&1 &
    echo $! > ../app.pid
    
    echo "等待应用启动..."
    for i in {1..30}; do
        if curl -s http://localhost:8080/actuator/health > /dev/null; then
            echo -e "${GREEN}✅ 应用启动成功${NC}"
            break
        fi
        echo -n "."
        sleep 2
    done
    
    if ! curl -s http://localhost:8080/actuator/health > /dev/null; then
        echo -e "${RED}❌ 应用启动失败，请检查日志: tail -f app.log${NC}"
        exit 1
    fi
    cd ..
fi

# 3. 创建测试用户
echo ""
echo "👥 ===== 步骤3: 创建测试用户 ====="

# 注册管理员
echo "创建管理员账户..."
curl -s -X POST "http://localhost:8080/api/v1/auth/register" \
  -H "Content-Type: application/json" \
  -d '{
    "username": "admin",
    "email": "<EMAIL>",
    "password": "admin123",
    "confirmPassword": "admin123",
    "nickname": "系统管理员"
  }' > /dev/null

# 添加管理员角色 (需要MySQL权限)
mysql -u root -e "
USE goose_app;
INSERT IGNORE INTO user_roles (user_id, role_name) 
SELECT id, 'ADMIN' FROM users WHERE username = 'admin';
" 2>/dev/null || echo -e "${YELLOW}⚠️  无法自动设置管理员角色，请手动执行${NC}"

# 注册普通用户
echo "创建普通用户..."
for i in 1 2; do
    curl -s -X POST "http://localhost:8080/api/v1/auth/register" \
      -H "Content-Type: application/json" \
      -d "{
        \"username\": \"user$i\",
        \"email\": \"user$<EMAIL>\",
        \"password\": \"user123\",
        \"confirmPassword\": \"user123\",
        \"nickname\": \"测试用户$i\"
      }" > /dev/null
done

# 获取Token
echo "获取用户Token..."
ADMIN_TOKEN=$(curl -s -X POST "http://localhost:8080/api/v1/auth/login" \
  -H "Content-Type: application/json" \
  -d '{
    "username": "admin",
    "password": "admin123"
  }' | jq -r '.data.token // empty')

USER1_TOKEN=$(curl -s -X POST "http://localhost:8080/api/v1/auth/login" \
  -H "Content-Type: application/json" \
  -d '{
    "username": "user1",
    "password": "user123"
  }' | jq -r '.data.token // empty')

if [ -z "$ADMIN_TOKEN" ] || [ -z "$USER1_TOKEN" ]; then
    echo -e "${RED}❌ 用户Token获取失败${NC}"
    exit 1
fi

echo -e "${GREEN}✅ 测试用户创建完成${NC}"

# 4. 核心功能测试
echo ""
echo "🧪 ===== 步骤4: 核心功能测试 ====="

# 测试计数器
TESTS_PASSED=0
TESTS_TOTAL=0

# 测试1: 权限验证
echo -e "${BLUE}测试1: 权限验证${NC}"

TESTS_TOTAL=$((TESTS_TOTAL + 1))
echo "1.1 普通用户尝试创建小鹅 (应该失败)"
RESULT=$(curl -s -X POST "http://localhost:8080/api/v1/admin/geese/create" \
  -H "Content-Type: application/json" \
  -H "satoken: Bearer $USER1_TOKEN" \
  -d '{
    "name": "非法小鹅",
    "breed": "白鹅",
    "gender": 1,
    "initialHealth": 100
  }')

if echo "$RESULT" | grep -q "权限不足\|403\|Forbidden\|无权限"; then
    echo -e "${GREEN}✅ 权限验证正常 - 普通用户无法创建小鹅${NC}"
    TESTS_PASSED=$((TESTS_PASSED + 1))
else
    echo -e "${RED}❌ 权限验证失败 - 普通用户可以创建小鹅${NC}"
    echo "响应: $RESULT"
fi

TESTS_TOTAL=$((TESTS_TOTAL + 1))
echo "1.2 管理员创建小鹅 (应该成功)"
GOOSE_RESULT=$(curl -s -X POST "http://localhost:8080/api/v1/admin/geese/create" \
  -H "Content-Type: application/json" \
  -H "satoken: Bearer $ADMIN_TOKEN" \
  -d '{
    "name": "测试小鹅",
    "breed": "白鹅",
    "gender": 1,
    "initialHealth": 100
  }')

GOOSE_ID=$(echo "$GOOSE_RESULT" | jq -r '.data.id // empty')
if [ ! -z "$GOOSE_ID" ] && [ "$GOOSE_ID" != "null" ]; then
    echo -e "${GREEN}✅ 管理员创建小鹅成功 (ID: $GOOSE_ID)${NC}"
    TESTS_PASSED=$((TESTS_PASSED + 1))
else
    echo -e "${RED}❌ 管理员创建小鹅失败${NC}"
    echo "响应: $GOOSE_RESULT"
fi

# 测试2: 商城功能
echo -e "${BLUE}测试2: 商城功能${NC}"

TESTS_TOTAL=$((TESTS_TOTAL + 1))
echo "2.1 查看待售小鹅"
SHOP_RESULT=$(curl -s -X GET "http://localhost:8080/api/v1/shop/geese/available" \
  -H "satoken: Bearer $USER1_TOKEN")

if echo "$SHOP_RESULT" | jq -e '.data | length > 0' > /dev/null; then
    echo -e "${GREEN}✅ 商城查看功能正常${NC}"
    TESTS_PASSED=$((TESTS_PASSED + 1))
else
    echo -e "${RED}❌ 商城查看功能异常${NC}"
    echo "响应: $SHOP_RESULT"
fi

# 测试3: 购买流程 (仅在有小鹅时执行)
if [ ! -z "$GOOSE_ID" ] && [ "$GOOSE_ID" != "null" ]; then
    echo -e "${BLUE}测试3: 购买流程${NC}"
    
    TESTS_TOTAL=$((TESTS_TOTAL + 1))
    echo "3.1 用户购买小鹅"
    PURCHASE_RESULT=$(curl -s -X POST "http://localhost:8080/api/v1/shop/purchase" \
      -H "Content-Type: application/json" \
      -H "satoken: Bearer $USER1_TOKEN" \
      -d "{
        \"gooseId\": $GOOSE_ID,
        \"paymentMethod\": \"WECHAT_PAY\"
      }")
    
    PURCHASE_ID=$(echo "$PURCHASE_RESULT" | jq -r '.data.id // empty')
    if [ ! -z "$PURCHASE_ID" ] && [ "$PURCHASE_ID" != "null" ]; then
        echo -e "${GREEN}✅ 购买流程正常 (购买ID: $PURCHASE_ID)${NC}"
        TESTS_PASSED=$((TESTS_PASSED + 1))
        
        # 验证归属权变更
        TESTS_TOTAL=$((TESTS_TOTAL + 1))
        echo "3.2 验证归属权变更"
        GOOSE_STATUS=$(curl -s -X GET "http://localhost:8080/api/v1/geese/$GOOSE_ID" \
          -H "satoken: Bearer $USER1_TOKEN")
        
        OWNERSHIP_STATUS=$(echo "$GOOSE_STATUS" | jq -r '.data.ownershipStatus // empty')
        if [ "$OWNERSHIP_STATUS" = "1" ] || echo "$GOOSE_STATUS" | grep -q "已归属"; then
            echo -e "${GREEN}✅ 归属权变更正常${NC}"
            TESTS_PASSED=$((TESTS_PASSED + 1))
        else
            echo -e "${RED}❌ 归属权变更异常${NC}"
            echo "响应: $GOOSE_STATUS"
        fi
    else
        echo -e "${RED}❌ 购买流程异常${NC}"
        echo "响应: $PURCHASE_RESULT"
    fi
fi

# 5. 测试结果汇总
echo ""
echo "📊 ===== 测试结果汇总 ====="
echo "通过测试: $TESTS_PASSED / $TESTS_TOTAL"

if [ $TESTS_PASSED -eq $TESTS_TOTAL ]; then
    echo -e "${GREEN}🎉 所有测试通过！重构验证成功！${NC}"
    exit 0
else
    echo -e "${YELLOW}⚠️  部分测试失败，请检查具体问题${NC}"
    echo ""
    echo "详细测试指导请参考: 重构验证测试指导.md"
    echo "查看应用日志: tail -f app.log"
    exit 1
fi 