# 陈述性记忆

## 高价值记忆（评分 ≥ 7）

- 2025/05/24 21:37 明天是用户的生日 #用户信息 #重要日期 #评分:9 #有效期:长期

- 2025/05/24 21:39 用户想设计小鹅养成APP产品，已有完整MVP设计文档 #产品设计 #APP开发 #农业互联网 #评分:8 #有效期:长期

- 2025/05/24 21:47 为用户创建了完整的UI/UX设计师角色提示词文件，包含角色定义、思维模式和执行框架 #角色设计 #UIUX设计 #PromptX框架 #评分:8 #有效期:长期

- 2025/05/24 21:50 开始为小鹅养成APP设计完整的UI/UX方案，包含用户研究、信息架构、交互设计和视觉设计 #UIUX设计 #小鹅养成APP #产品设计 #评分:9 #有效期:长期

- 2025/05/24 21:53 用户需要将小鹅养成APP的UI/UX设计方案生成为完整的设计文档，保存到项目根目录 #设计文档 #项目交付 #UIUX设计 #评分:8 #有效期:长期

- 2025/05/24 22:01 成功生成了完整的小鹅养成APP UI/UX设计文档，包含设计系统、页面设计、交互规范、可用性方案等全部内容，已保存到项目根目录 #设计文档完成 #项目交付 #UIUX设计 #评分:9 #有效期:长期

- 2025/05/24 22:16 用户需要创建全栈开发工程师的角色提示词文件，基于PromptX框架结构 #角色设计 #全栈开发 #PromptX框架 #评分:8 #有效期:长期

- 2025/05/24 22:18 成功创建了完整的全栈开发工程师角色提示词文件，包含角色定义、思维模式和执行框架，涵盖前后端开发全流程 #角色设计 #全栈开发 #PromptX框架 #评分:9 #有效期:长期

- 2025/05/24 22:31 开始为小鹅养成APP创建完整的HTML原型系统，包含移动端优先设计、完整文件架构、核心页面实现和交互功能 #HTML原型开发 #移动端开发 #小鹅养成APP #评分:9 #有效期:长期

- 2025/05/24 22:49 成功完成小鹅养成APP的完整HTML原型系统开发，包含5个核心页面、完整的设计系统、交互功能和主导航页面，提前12小时完成项目 #HTML原型完成 #项目交付 #小鹅养成APP #评分:10 #有效期:长期

- 2025/05/30 18:41 成功创建了完整的UIUX设计师角色提示词框架，包含视觉凸显应用主题、极致美学、奥卡姆剃刀原则、合理色彩搭配等核心设计原则，符合用户所有要求 #角色设计 #UIUX设计师 #PromptX框架 #设计原则 #评分:9 #有效期:长期
