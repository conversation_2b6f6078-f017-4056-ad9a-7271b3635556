{"inputs": [{"type": "promptString", "id": "github_token", "description": "GitHub Personal Access Token", "password": true}], "servers": {"github.com/github/github-mcp-server": {"command": "docker", "args": ["run", "-i", "--rm", "-e", "GITHUB_PERSONAL_ACCESS_TOKEN", "ghcr.io/github/github-mcp-server"], "env": {"GITHUB_PERSONAL_ACCESS_TOKEN": "${*********************************************************************************************}"}}}}