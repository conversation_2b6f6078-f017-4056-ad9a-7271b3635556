# GooseApp - 小鹅养成

---

## ⚠️ 项目状态：正在进行重大架构重构 ⚠️

**注意：** 本项目目前正处于由高级全栈开发顾问主导的全面架构重构阶段。

**核心目标：** 解决在初始版本中发现的严重架构、安全和性能问题，将项目从“高风险状态”转变为一个稳定、可维护、可扩展的现代化应用。

在所有标记为 **优先级1** 的重构任务完成之前，**请勿添加任何新功能**。

---

## 📖 文档中心

本项目所有的设计文档、架构报告、任务清单和开发规范，现已统一迁移至根目录下的 **`/docs`** 文件夹。

**这是获取项目信息的唯一可信来源。**

请在开始任何工作前，优先查阅以下核心文档：
-   [`/docs/项目架构与结构分析报告.md`](docs/项目架构与结构分析报告.md): 详细说明了本次重构的原因、目标和具体的技术路线图。
-   [`/docs/前端开发进度跟踪.md`](docs/前端开发进度跟踪.md): 实时更新当前的开发进展和已完成的任务。
-   [`/docs/任务清单/`](docs/任务清单/): 存放每日的详细开发任务清单。

---

## 🚀 快速启动

### 后端 (`goose-app-backend`)

1.  **环境要求**: JDK 17, Maven 3.x, MySQL 8.x
2.  **配置**:
    -   确保数据库已创建，并在 `goose-app-backend/src/main/resources/application-dev.yml` 中配置正确的数据库连接信息。
    -   注意：生产环境的敏感配置已使用 **Jasypt** 加密。启动时需要提供密钥。
3.  **运行**:
    ```bash
    cd goose-app-backend
    mvn spring-boot:run -Dspring-boot.run.profiles=dev
    ```

### 前端 (`GooseAppMobile`)

1.  **环境要求**: Node.js, Yarn, React Native CLI, Xcode (for iOS) / Android Studio (for Android)
2.  **安装依赖**:
    ```bash
    cd GooseAppMobile
    yarn install
    ```
3.  **运行**:
    -   **iOS**: `yarn ios`
    -   **Android**: `yarn android`

---

## 🛠️ 开发规范 (`fullstack-pro` 模式)

本项目的开发流程遵循在 `.kilocodemodes` 文件中定义的 **`fullstack-pro`** 模式。该模式包含了一套严格的工程实践规则，包括：

-   **任务驱动**: 所有开发都必须有对应的任务清单。
-   **文档先行**: 任何重大变更都需要更新相关文档。
-   **测试覆盖**: 核心逻辑必须有单元测试。
-   **持续审视**: 定期进行代码和架构的自我批判与改进。

激活此模式是参与项目开发的前提，以确保所有贡献者遵循统一的高标准。