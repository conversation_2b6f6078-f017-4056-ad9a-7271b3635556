#!/usr/bin/env node

/**
 * 小鹅养成APP - 前端API集成自动化测试脚本
 * 测试前端与后端API的连接和数据交互
 */

const fetch = require('node-fetch');

// 测试配置
const API_BASE_URL = 'http://localhost:8080/api/v1';
const TEST_USER = {
  username: 'testuser123',  // 3-20字符
  password: 'Test123456',
  confirmPassword: 'Test123456',  // 添加确认密码
  email: 'test_' + Date.now() + '@example.com',
  phone: '13800138000'
};

// 测试结果统计
let testResults = {
  total: 0,
  passed: 0,
  failed: 0,
  errors: []
};

// 存储测试过程中的token
let authToken = null;

/**
 * 执行HTTP请求
 */
async function makeRequest(method, endpoint, data = null, headers = {}) {
  const url = `${API_BASE_URL}${endpoint}`;
  const options = {
    method,
    headers: {
      'Content-Type': 'application/json',
      ...headers
    }
  };

  if (data) {
    options.body = JSON.stringify(data);
  }

  try {
    const response = await fetch(url, options);
    const responseData = await response.text();
    
    let jsonData;
    try {
      jsonData = JSON.parse(responseData);
    } catch (e) {
      jsonData = { rawResponse: responseData };
    }

    return {
      status: response.status,
      ok: response.ok,
      data: jsonData,
      headers: response.headers
    };
  } catch (error) {
    throw new Error(`Network error: ${error.message}`);
  }
}

/**
 * 测试用例执行器
 */
async function runTest(testName, testFunction) {
  testResults.total++;
  console.log(`\n🧪 测试: ${testName}`);
  
  try {
    await testFunction();
    testResults.passed++;
    console.log(`✅ 通过: ${testName}`);
  } catch (error) {
    testResults.failed++;
    testResults.errors.push({ test: testName, error: error.message });
    console.log(`❌ 失败: ${testName} - ${error.message}`);
  }
}

/**
 * 测试1: 服务器连接测试
 */
async function testServerConnection() {
  const response = await makeRequest('GET', '/auth/status');
  if (!response.ok) {
    throw new Error(`服务器连接失败: ${response.status}`);
  }
  console.log(`   📡 服务器响应: ${JSON.stringify(response.data)}`);
}

/**
 * 测试2: 用户注册测试
 */
async function testUserRegistration() {
  const response = await makeRequest('POST', '/auth/register', {
    username: TEST_USER.username,
    password: TEST_USER.password,
    confirmPassword: TEST_USER.confirmPassword,
    email: TEST_USER.email,
    phone: TEST_USER.phone
  });

  if (!response.ok) {
    throw new Error(`注册失败: ${response.status} - ${JSON.stringify(response.data)}`);
  }

  console.log(`   👤 注册成功: ${TEST_USER.username}`);
  console.log(`   📄 响应数据: ${JSON.stringify(response.data)}`);
}

/**
 * 测试3: 用户登录测试
 */
async function testUserLogin() {
  const response = await makeRequest('POST', '/auth/login', {
    username: TEST_USER.username,
    password: TEST_USER.password
  });

  if (!response.ok) {
    throw new Error(`登录失败: ${response.status} - ${JSON.stringify(response.data)}`);
  }

  // 提取token
  if (response.data && response.data.data && response.data.data.token) {
    authToken = response.data.data.token;
    console.log(`   🔑 获取Token: ${authToken.substring(0, 20)}...`);
  } else {
    throw new Error('登录响应中未找到token');
  }

  console.log(`   🎉 登录成功: ${TEST_USER.username}`);
  console.log(`   📄 响应数据: ${JSON.stringify(response.data)}`);
}

/**
 * 测试4: 获取用户信息测试
 */
async function testGetUserInfo() {
  if (!authToken) {
    throw new Error('需要先登录获取token');
  }

  const response = await makeRequest('GET', '/auth/me', null, {
    'satoken': `Bearer ${authToken}`
  });

  if (!response.ok) {
    throw new Error(`获取用户信息失败: ${response.status} - ${JSON.stringify(response.data)}`);
  }

  console.log(`   👤 用户信息: ${JSON.stringify(response.data)}`);
}

/**
 * 测试5: Token验证测试
 */
async function testTokenValidation() {
  if (!authToken) {
    throw new Error('需要先登录获取token');
  }

  const response = await makeRequest('GET', '/auth/status', null, {
    'satoken': authToken
  });

  if (!response.ok) {
    throw new Error(`Token验证失败: ${response.status}`);
  }

  console.log(`   🔐 Token验证通过`);
}

/**
 * 测试6: 用户名检查测试
 */
async function testUsernameCheck() {
  // 测试已存在的用户名
  const response1 = await makeRequest('GET', `/auth/check-username?username=${TEST_USER.username}`);
  if (response1.ok && response1.data && response1.data.data === false) {
    console.log(`   ✅ 用户名检查正确: ${TEST_USER.username} 已存在`);
  }

  // 测试不存在的用户名
  const newUsername = 'nonexistent_' + Date.now();
  const response2 = await makeRequest('GET', `/auth/check-username?username=${newUsername}`);
  if (response2.ok && response2.data && response2.data.data === true) {
    console.log(`   ✅ 用户名检查正确: ${newUsername} 可用`);
  }
}

/**
 * 测试7: 邮箱检查测试
 */
async function testEmailCheck() {
  // 测试已存在的邮箱
  const response1 = await makeRequest('GET', `/auth/check-email?email=${TEST_USER.email}`);
  if (response1.ok && response1.data && response1.data.data === false) {
    console.log(`   ✅ 邮箱检查正确: ${TEST_USER.email} 已存在`);
  }

  // 测试不存在的邮箱
  const newEmail = 'new_' + Date.now() + '@example.com';
  const response2 = await makeRequest('GET', `/auth/check-email?email=${newEmail}`);
  if (response2.ok && response2.data && response2.data.data === true) {
    console.log(`   ✅ 邮箱检查正确: ${newEmail} 可用`);
  }
}

/**
 * 测试8: 用户退出测试
 */
async function testUserLogout() {
  if (!authToken) {
    throw new Error('需要先登录获取token');
  }

  const response = await makeRequest('POST', '/auth/logout', null, {
    'satoken': `Bearer ${authToken}`
  });

  if (!response.ok) {
    throw new Error(`退出失败: ${response.status} - ${JSON.stringify(response.data)}`);
  }

  console.log(`   👋 退出成功`);
  authToken = null; // 清除token
}

/**
 * 主测试函数
 */
async function runAllTests() {
  console.log('🚀 开始执行小鹅养成APP前端API集成测试');
  console.log('=' .repeat(60));

  // 执行所有测试
  await runTest('服务器连接测试', testServerConnection);
  await runTest('用户注册测试', testUserRegistration);
  await runTest('用户登录测试', testUserLogin);
  await runTest('获取用户信息测试', testGetUserInfo);
  await runTest('Token验证测试', testTokenValidation);
  await runTest('用户名检查测试', testUsernameCheck);
  await runTest('邮箱检查测试', testEmailCheck);
  await runTest('用户退出测试', testUserLogout);

  // 输出测试结果
  console.log('\n' + '=' .repeat(60));
  console.log('📊 测试结果统计');
  console.log('=' .repeat(60));
  console.log(`总测试数: ${testResults.total}`);
  console.log(`通过数量: ${testResults.passed} ✅`);
  console.log(`失败数量: ${testResults.failed} ❌`);
  console.log(`成功率: ${((testResults.passed / testResults.total) * 100).toFixed(1)}%`);

  if (testResults.errors.length > 0) {
    console.log('\n❌ 失败的测试详情:');
    testResults.errors.forEach((error, index) => {
      console.log(`${index + 1}. ${error.test}: ${error.error}`);
    });
  }

  console.log('\n🎯 测试完成!');
  
  // 返回退出码
  process.exit(testResults.failed > 0 ? 1 : 0);
}

// 执行测试
if (require.main === module) {
  runAllTests().catch(error => {
    console.error('💥 测试执行出错:', error);
    process.exit(1);
  });
}

module.exports = {
  runAllTests,
  makeRequest,
  testResults
};
