/**
 * Sample React Native App
 * https://github.com/facebook/react-native
 *
 * @format
 */

import React from 'react';
import { SafeAreaProvider } from 'react-native-safe-area-context';
import { PaperProvider } from 'react-native-paper';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { AppNavigator } from './src/navigation/AppNavigator';
import { AuthProvider } from './src/context/AuthContext';
import { GooseProvider } from './src/context/GooseContext';
import { ShopProvider } from './src/context/ShopContext';
import { lightTheme } from './src/theme';

// Create a client
const queryClient = new QueryClient();

function App(): React.JSX.Element {
  return (
    <SafeAreaProvider>
      <PaperProvider theme={lightTheme}>
        <QueryClientProvider client={queryClient}>
          <AuthProvider>
            <GooseProvider>
              <ShopProvider>
                <AppNavigator />
              </ShopProvider>
            </GooseProvider>
          </AuthProvider>
        </QueryClientProvider>
      </PaperProvider>
    </SafeAreaProvider>
  );
}

export default App;
