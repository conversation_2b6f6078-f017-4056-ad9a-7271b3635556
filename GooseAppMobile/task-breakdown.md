# 小鹅养成App - 前端开发任务清单

本清单由 “全栈开发工程师（专家级）” 角色制定，旨在系统化地推进前端开发工作。

## 阶段一：架构优化与基础建设 (P0 - 最高优先级) - ✅ 已完成

- [x] **任务 1.1**: 创建 `src/components` 目录，用于存放可复用的UI组件。
- [x] **任务 1.2**: 在 `src/components` 中创建基础UI组件：
    - [x] `Button.tsx`: 应用统一风格的按钮。
    - [x] `Card.tsx`: 用于包裹小鹅信息、商品信息等的卡片容器。
    - [x] `LoadingIndicator.tsx`: 全局加载动画。
    - [x] `ErrorMessage.tsx`: 标准化的错误信息展示组件。
- [x] **任务 1.3**: 审查并重构 `LoginScreen.tsx`，将其UI元素提取到 `src/components` 中。（HomeScreen的重构将并入后续任务）

## 阶段二：核心功能屏幕开发 (P1 - 高优先级)

- [ ] **任务 2.1**: **小鹅列表与详情**
    - [ ] 创建 `MyGeeseScreen.tsx`，实现从API获取并展示用户拥有的小鹅列表。
    - [ ] 创建 `GooseDetailScreen.tsx`，用于展示单只小鹅的详细信息（属性、状态等）。
    - [ ] 在 `AppNavigator.tsx` 中添加从 `MyGeeseScreen` 到 `GooseDetailScreen` 的导航逻辑。
- [ ] **任务 2.2**: **商店功能**
    - [ ] 创建 `ShopScreen.tsx`，用于展示待售的小鹅品种。
    - [ ] 实现购买小鹅的API调用与交互流程。
    - [ ] 在 `AppNavigator.tsx` 中添加 `ShopScreen` 的导航。
- [ ] **任务 2.3**: **个人中心**
    - [ ] 创建 `ProfileScreen.tsx`，用于展示和编辑用户信息。
    - [ ] 实现用户登出功能。
    - [ ] 在 `AppNavigator.tsx` 中添加 `ProfileScreen` 的导航。

## 阶段三：高级功能开发 (P2 - 中优先级)

- [ ] **任务 3.1**: **转赠功能**
    - [ ] 设计并创建转赠流程的相关屏幕 (`GiftCreationScreen.tsx`, `GiftReceiveScreen.tsx`)。
    - [ ] 实现转赠和接收的完整API交互。