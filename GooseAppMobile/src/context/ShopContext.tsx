import React, { createContext, useReducer, useMemo, useContext, Dispatch } from 'react';
import { Goose } from '../types/goose';

// 1. 简化 State，只保留纯客户端状态
type ShopState = {
  cart: Goose[];
  orders: any[]; // Replace 'any' with a proper Order type later
};

// 2. 简化 Action
type ShopAction =
  | { type: 'ADD_TO_CART'; payload: Goose }
  | { type: 'REMOVE_FROM_CART'; payload: number }
  | { type: 'CLEAR_CART' };

const shopReducer = (state: ShopState, action: ShopAction): ShopState => {
  switch (action.type) {
    case 'ADD_TO_CART':
      // 避免重复添加
      if (state.cart.find(item => item.id === action.payload.id)) {
        return state;
      }
      return { ...state, cart: [...state.cart, action.payload] };
    case 'REMOVE_FROM_CART':
      return { ...state, cart: state.cart.filter(item => item.id !== action.payload) };
    case 'CLEAR_CART':
      return { ...state, cart: [] };
    default:
      return state;
  }
};

// 3. 明确 Context 类型，暴露 state 和 dispatch
type ShopContextType = {
  state: ShopState;
  dispatch: Dispatch<ShopAction>;
  // 未来可以添加 useMutation 驱动的 actions，如 checkout
};

export const ShopContext = createContext<ShopContextType | undefined>(undefined);

export const ShopProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const initialState: ShopState = {
    cart: [],
    orders: [],
  };

  const [state, dispatch] = useReducer(shopReducer, initialState);

  // 4. 将 state 和 dispatch 传入 context
  const contextValue = useMemo(() => ({
    state,
    dispatch,
  }), [state, dispatch]);

  return (
    <ShopContext.Provider value={contextValue}>
      {children}
    </ShopContext.Provider>
  );
};

export const useShop = (): ShopContextType => {
  const context = useContext(ShopContext);
  if (!context) {
    throw new Error('useShop must be used within a ShopProvider');
  }
  return context;
};