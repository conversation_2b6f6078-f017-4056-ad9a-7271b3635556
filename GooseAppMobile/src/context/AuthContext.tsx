import React, { createContext, useReducer, useEffect, useMemo, useContext } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { useMutation, useQueryClient, UseMutationResult } from '@tanstack/react-query';
import { authApi } from '../services/authApi';
import { api, STORAGE_KEYS } from '../services/api';
import { LoginRequest, PhoneLoginRequest, PhonePasswordLoginRequest, UserSession } from '../types/auth';

// 1. 简化 State 和 Action，移除 loading 和 error
type AuthState = {
  isSignout: boolean;
  userToken: string | null;
  user: UserSession | null;
  isLoading: boolean; // 保留用于初始化的 loading 状态
};

type AuthAction =
  | { type: 'RESTORE_TOKEN'; token: string | null; user: UserSession | null }
  | { type: 'SIGN_IN'; token: string; user: UserSession }
  | { type: 'SIGN_OUT' };

const authReducer = (prevState: AuthState, action: AuthAction): AuthState => {
  switch (action.type) {
    case 'RESTORE_TOKEN':
      return {
        ...prevState,
        userToken: action.token,
        user: action.user,
        isLoading: false,
      };
    case 'SIGN_IN':
      return {
        ...prevState,
        isSignout: false,
        userToken: action.token,
        user: action.user,
      };
    case 'SIGN_OUT':
      return {
        ...prevState,
        isSignout: true,
        userToken: null,
        user: null,
      };
    default:
      return prevState;
  }
};

// 2. 定义新的 Login Mutation 类型
type LoginData = LoginRequest | PhoneLoginRequest | PhonePasswordLoginRequest;
type LoginVariables = {
  type: 'username' | 'phone' | 'phonePassword';
  data: LoginData;
};

// 3. 定义新的 Context 类型
type AuthContextType = {
  login: UseMutationResult<UserSession & { token: string }, Error, LoginVariables>;
  logout: UseMutationResult<void, Error, void, unknown>;
  state: AuthState;
};

export const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [state, dispatch] = useReducer(authReducer, {
    isLoading: true, // 初始加载状态
    isSignout: false,
    userToken: null,
    user: null,
  });

  const queryClient = useQueryClient();

  // 4. 使用 useMutation 封装登录逻辑
  const loginMutation = useMutation<UserSession & { token: string }, Error, LoginVariables>({
    mutationFn: async ({ type, data }) => {
      switch (type) {
        case 'username':
          return authApi.login(data as LoginRequest);
        case 'phone':
          return authApi.loginByPhone(data as PhoneLoginRequest);
        case 'phonePassword':
          return authApi.loginByPhonePassword(data as PhonePasswordLoginRequest);
        default:
          throw new Error('Invalid login type');
      }
    },
    onSuccess: async (response) => {
      const { token, ...user } = response;
      // 处理副作用
      await AsyncStorage.setItem(STORAGE_KEYS.ACCESS_TOKEN, token);
      await AsyncStorage.setItem(STORAGE_KEYS.USER_INFO, JSON.stringify(user));
      api.setToken(token);
      
      // 更新全局状态
      dispatch({ type: 'SIGN_IN', token, user });

      // 可以让依赖于认证状态的查询失效
      // await queryClient.invalidateQueries({ queryKey: ['user-profile'] });
    },
    // onError 由 useMutation 自动处理，错误信息在 loginMutation.error 中
  });

  // 5. 使用 useMutation 封装登出逻辑
  const logoutMutation = useMutation<void, Error, void>({
    mutationFn: async () => {
      // await authApi.logout(); // 如果后端有登出接口
      await AsyncStorage.removeItem(STORAGE_KEYS.ACCESS_TOKEN);
      await AsyncStorage.removeItem(STORAGE_KEYS.USER_INFO);
      api.clearToken();
    },
    onSuccess: () => {
      dispatch({ type: 'SIGN_OUT' });
       // 清除所有查询缓存
       queryClient.clear();
    },
  });

  useEffect(() => {
    const bootstrapAsync = async () => {
      let userToken: string | null = null;
      let user: UserSession | null = null;
      try {
        userToken = await AsyncStorage.getItem(STORAGE_KEYS.ACCESS_TOKEN);
        const userJson = await AsyncStorage.getItem(STORAGE_KEYS.USER_INFO);
        if (userJson) user = JSON.parse(userJson);
        if (userToken) api.setToken(userToken);
      } catch (e) {
        console.error("Failed to restore token", e);
      }
      dispatch({ type: 'RESTORE_TOKEN', token: userToken, user });
    };
    bootstrapAsync();
  }, []);

  // 6. 构造新的 Context Value
  const authContext = useMemo(
    () => ({
      login: loginMutation,
      logout: logoutMutation,
      state,
    }),
    [loginMutation, logoutMutation, state]
  );

  return (
    <AuthContext.Provider value={authContext}>
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};