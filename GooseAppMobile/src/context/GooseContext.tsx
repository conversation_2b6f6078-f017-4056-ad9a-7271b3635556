import React, { createContext, useReducer, useEffect, useMemo, useContext, useCallback } from 'react';
import { api } from '../services/api';
import { ApiResponse } from '../types/api';
import { Goose } from '../types/goose';
import { useAuth } from './AuthContext';

type GooseState = {
  myGeese: Goose[];
  currentGoose: Goose | null;
  loading: boolean;
  error: string | null;
};

type GooseAction =
  | { type: 'SET_LOADING'; payload: boolean }
  | { type: 'SET_MY_GEESE'; payload: Goose[] }
  | { type: 'SET_CURRENT_GOOSE'; payload: Goose | null }
  | { type: 'SET_ERROR'; payload: string | null };

const gooseReducer = (state: GooseState, action: GooseAction): GooseState => {
  switch (action.type) {
    case 'SET_LOADING':
      return { ...state, loading: action.payload };
    case 'SET_MY_GEESE':
      return { ...state, myGeese: action.payload, loading: false, error: null };
    case 'SET_CURRENT_GOOSE':
        return { ...state, currentGoose: action.payload };
    case 'SET_ERROR':
      return { ...state, error: action.payload, loading: false };
    default:
      return state;
  }
};

type GooseContextType = {
  state: GooseState;
  fetchMyGeese: () => Promise<void>;
  setCurrentGoose: (goose: Goose | null) => void;
};

export const GooseContext = createContext<GooseContextType | undefined>(undefined);

export const GooseProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const { state: authState } = useAuth();
  const initialState: GooseState = {
    myGeese: [],
    currentGoose: null,
    loading: false,
    error: null,
  };

  const [state, dispatch] = useReducer(gooseReducer, initialState);

  const fetchMyGeese = useCallback(async () => {
    if (!authState.userToken) return;
    dispatch({ type: 'SET_LOADING', payload: true });
    try {
      // 1. Correct the endpoint from '/goose/my' to '/geese/my'
      // 2. Expect an ApiResponse wrapper
      // 3. Unwrap the data from response.data.data
      const response = await api.get<any>('/geese/my'); // Use <any> for flexible response handling
      
      // Check if the response data itself is the array (no ApiResponse wrapper)
      if (Array.isArray(response.data)) {
        dispatch({ type: 'SET_MY_GEESE', payload: response.data });
      }
      // Check for the standard ApiResponse wrapper
      else if (response.data && response.data.code === 200) {
        dispatch({ type: 'SET_MY_GEESE', payload: response.data.data });
      }
      // If neither matches, it's an error
      else {
        throw new Error(response.data.message || '获取数据失败');
      }
    } catch (e: any) {
      const errorMessage = e?.response?.data?.message || e?.message || '获取小鹅列表失败';
      dispatch({ type: 'SET_ERROR', payload: errorMessage });
      console.error('Failed to fetch geese in context:', e);
    }
  }, [authState.userToken]);

  useEffect(() => {
    if (authState.userToken) {
      fetchMyGeese();
    }
  }, [authState.userToken, fetchMyGeese]);

  const setCurrentGoose = (goose: Goose | null) => {
    dispatch({ type: 'SET_CURRENT_GOOSE', payload: goose });
  };

  const contextValue = useMemo(() => ({
    state,
    fetchMyGeese,
    setCurrentGoose,
  }), [state, fetchMyGeese]);

  return (
    <GooseContext.Provider value={contextValue}>
      {children}
    </GooseContext.Provider>
  );
};

export const useGoose = (): GooseContextType => {
  const context = useContext(GooseContext);
  if (!context) {
    throw new Error('useGoose must be used within a GooseProvider');
  }
  return context;
};