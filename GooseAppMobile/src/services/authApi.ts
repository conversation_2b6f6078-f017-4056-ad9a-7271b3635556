import { api, authApi as authApiClient, appApi } from './api';
import {
  LoginRequest,
  LoginResponse,
  RegisterRequest,
  User,
  LoginStatus,
  ChangePasswordRequest,
  PhoneLoginRequest,
  PhonePasswordLoginRequest,
  SendSmsCodeRequest,
  VerifySmsCodeRequest,
  CreateMobileAccountRequest,
  SetMobilePasswordRequest,
  MobileAccountResponse,
  RegistrationStatusResponse,
  SmsCodeType,
} from '../types/auth';

/**
 * 认证API服务
 * 封装所有与用户认证相关的API调用
 */
export class AuthApiService {

  /**
   * 用户登录
   * @param credentials 登录凭据
   * @returns 登录响应数据
   */
  static async login(credentials: LoginRequest): Promise<LoginResponse> {
    const response = await authApiClient.post<LoginResponse>('/login', credentials, false);

    // 登录成功后设置token
    if (response.data.token) {
      await api.setToken(response.data.token);
    }

    return response.data;
  }

  /**
   * 用户注册
   * @param userData 注册数据
   * @returns 注册后的登录响应
   */
  static async register(userData: RegisterRequest): Promise<LoginResponse> {
    const response = await authApiClient.post<LoginResponse>('/register', userData, false);

    // 注册成功后自动设置token
    if (response.data.token) {
      await api.setToken(response.data.token);
    }

    return response.data;
  }

  /**
   * 用户退出登录
   */
  static async logout(): Promise<void> {
    try {
      // 调用后端退出接口
      await authApiClient.post('/logout', {}, true);
    } catch (error) {
      console.warn('Logout API call failed:', error);
    } finally {
      // 无论后端调用是否成功，都清除本地token
      await api.clearToken();
    }
  }

  /**
   * 获取当前用户信息
   * @returns 用户信息
   */
  static async getCurrentUser(): Promise<User> {
    const response = await authApiClient.get<User>('/me', true);
    return response.data;
  }

  /**
   * 刷新Token
   * @returns 新的token信息
   */
  static async refreshToken(): Promise<{ token: string; expiresIn: number }> {
    const response = await authApiClient.post<{ token: string; expiresIn: number }>('/refresh', {}, true);

    // 更新本地token
    if (response.data.token) {
      await api.setToken(response.data.token);
    }

    return response.data;
  }

  /**
   * 修改密码
   * @param data 密码修改数据
   */
  static async changePassword(data: ChangePasswordRequest): Promise<void> {
    await authApiClient.post('/change-password', data, true);
  }

  /**
   * 获取登录状态
   * @returns 登录状态信息
   */
  static async getLoginStatus(): Promise<LoginStatus> {
    const response = await authApiClient.get<LoginStatus>('/status', false);
    return response.data;
  }

  /**
   * 检查用户名是否可用
   * @param username 用户名
   * @returns true表示可用，false表示已存在
   */
  static async checkUsername(username: string): Promise<boolean> {
    const response = await authApiClient.get<boolean>(`/check-username?username=${encodeURIComponent(username)}`, false);
    return response.data;
  }

  /**
   * 检查邮箱是否可用
   * @param email 邮箱地址
   * @returns true表示可用，false表示已存在
   */
  static async checkEmail(email: string): Promise<boolean> {
    const response = await authApiClient.get<boolean>(`/check-email?email=${encodeURIComponent(email)}`, false);
    return response.data;
  }

  /**
   * 检查手机号是否可用
   * @param phone 手机号
   * @returns true表示可用，false表示已存在
   */
  static async checkPhone(phone: string): Promise<boolean> {
    const response = await authApiClient.get<boolean>(`/check-phone?phone=${encodeURIComponent(phone)}`, false);
    return response.data;
  }

  /**
   * 发送短信验证码
   * @param phone 手机号
   */
  static async sendSmsCode(phone: string): Promise<void> {
    await authApiClient.post('/send-sms', { phone }, false);
  }

  /**
   * 验证短信验证码
   * @param phone 手机号
   * @param code 验证码
   * @returns 验证结果
   */
  static async verifySmsCode(phone: string, code: string): Promise<boolean> {
    const response = await authApiClient.post<boolean>('/verify-sms', { phone, code }, false);
    return response.data;
  }

  /**
   * 发送短信验证码（新版本，支持验证码类型）
   * @param request 发送验证码请求
   */
  static async sendSmsCodeV2(request: SendSmsCodeRequest): Promise<void> {
    await authApiClient.post('/send-sms-code', request, false);
  }

  /**
   * 验证短信验证码（新版本，支持验证码类型）
   * @param request 验证验证码请求
   * @returns 验证结果
   */
  static async verifySmsCodeV2(request: VerifySmsCodeRequest): Promise<boolean> {
    const response = await authApiClient.post<boolean>('/verify-sms-code', request, false);
    return response.data;
  }

  /**
   * 手机号验证码登录
   * @param request 手机登录请求
   * @returns 登录响应数据
   */
  static async loginByPhone(request: PhoneLoginRequest): Promise<LoginResponse> {
    const response = await authApiClient.post<LoginResponse>('/login-by-phone', request, false);

    // 登录成功后设置token
    if (response.data.token) {
      await api.setToken(response.data.token);
    }

    return response.data;
  }

  /**
   * 手机号密码登录
   * @param request 手机密码登录请求
   * @returns 登录响应数据
   */
  static async loginByPhonePassword(request: PhonePasswordLoginRequest): Promise<LoginResponse> {
    // 先查找用户是否存在（check-phone返回true表示可用，false表示已存在）
    const checkResult = await authApiClient.get<boolean>(`/check-phone?phone=${encodeURIComponent(request.phone)}`, false);

    if (checkResult.data) {
      // 如果返回true，说明手机号可用（即未注册）
      throw new Error('手机号未注册，请先注册');
    }

    // 使用传统登录接口，将手机号作为用户名
    const loginRequest: LoginRequest = {
      username: request.phone,
      password: request.password,
    };

    const response = await authApiClient.post<LoginResponse>('/login', loginRequest, false);

    // 登录成功后设置token
    if (response.data.token) {
      await api.setToken(response.data.token);
    }

    return response.data;
  }

  /**
   * C端用户创建账户（第一步：仅创建账户，不设置密码）
   * @param request 创建账户请求
   * @returns 账户响应数据
   */
  static async createMobileAccount(request: CreateMobileAccountRequest): Promise<MobileAccountResponse> {
    const response = await authApiClient.post<MobileAccountResponse>('/create-mobile-account', request, false);
    return response.data;
  }

  /**
   * C端用户设置密码（第二步：设置登录密码并完成注册）
   * @param request 设置密码请求
   * @param tempToken 临时访问Token
   * @returns 登录响应数据
   */
  static async setMobilePassword(request: SetMobilePasswordRequest, tempToken: string): Promise<LoginResponse> {
    const response = await authApiClient.post<LoginResponse>('/set-mobile-password', request, false, {
      'Temp-Token': tempToken,
    });

    // 设置密码成功后设置正式token
    if (response.data.token) {
      await api.setToken(response.data.token);
    }

    return response.data;
  }

  /**
   * 查询注册状态
   * @param tempToken 临时访问Token
   * @returns 注册状态信息
   */
  static async getRegistrationStatus(tempToken: string): Promise<RegistrationStatusResponse> {
    const response = await authApiClient.get<RegistrationStatusResponse>('/registration-status', false, {
      'Temp-Token': tempToken,
    });
    return response.data;
  }

  /**
   * 找回密码 - 发送重置链接
   * @param email 邮箱地址
   */
  static async sendPasswordResetEmail(email: string): Promise<void> {
    await api.post('/auth/reset-password', { email }, false);
  }

  /**
   * 更新用户资料
   * @param userInfo 用户信息
   * @returns 更新后的用户信息
   */
  static async updateProfile(userInfo: Partial<User>): Promise<User> {
    const response = await api.put<User>('/auth/profile', userInfo, true);
    return response.data;
  }

  /**
   * 上传头像
   * @param imageFile 头像文件
   * @returns 头像URL
   */
  static async uploadAvatar(imageFile: FormData): Promise<{ avatarUrl: string }> {
    const response = await api.upload<{ avatarUrl: string }>('/auth/upload-avatar', imageFile);
    return response.data;
  }

  /**
   * 绑定手机号
   * @param phone 手机号
   * @param code 验证码
   */
  static async bindPhone(phone: string, code: string): Promise<void> {
    await api.post('/auth/bind-phone', { phone, code }, true);
  }

  /**
   * 解绑手机号
   * @param code 验证码
   */
  static async unbindPhone(code: string): Promise<void> {
    await api.post('/auth/unbind-phone', { code }, true);
  }

  /**
   * 账户注销
   * @param password 当前密码
   */
  static async deleteAccount(_password: string): Promise<void> {
    await api.delete('/auth/account', true);
    // 注销成功后清除本地数据
    await api.clearToken();
  }
}

// 导出便捷的调用方法
export const authApi = {
  // 传统认证
  login: AuthApiService.login,
  register: AuthApiService.register,
  logout: AuthApiService.logout,
  getCurrentUser: AuthApiService.getCurrentUser,
  refreshToken: AuthApiService.refreshToken,
  changePassword: AuthApiService.changePassword,
  getLoginStatus: AuthApiService.getLoginStatus,

  // 验证相关
  checkUsername: AuthApiService.checkUsername,
  checkEmail: AuthApiService.checkEmail,
  checkPhone: AuthApiService.checkPhone,

  // 短信验证码（旧版本）
  sendSmsCode: AuthApiService.sendSmsCode,
  verifySmsCode: AuthApiService.verifySmsCode,

  // 短信验证码（新版本）
  sendSmsCodeV2: AuthApiService.sendSmsCodeV2,
  verifySmsCodeV2: AuthApiService.verifySmsCodeV2,

  // C端用户手机认证
  loginByPhone: AuthApiService.loginByPhone,
  loginByPhonePassword: AuthApiService.loginByPhonePassword,
  createMobileAccount: AuthApiService.createMobileAccount,
  setMobilePassword: AuthApiService.setMobilePassword,
  getRegistrationStatus: AuthApiService.getRegistrationStatus,

  // 其他功能
  sendPasswordResetEmail: AuthApiService.sendPasswordResetEmail,
  updateProfile: AuthApiService.updateProfile,
  uploadAvatar: AuthApiService.uploadAvatar,
  bindPhone: AuthApiService.bindPhone,
  unbindPhone: AuthApiService.unbindPhone,
  deleteAccount: AuthApiService.deleteAccount,
};
