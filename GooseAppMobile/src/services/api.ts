import AsyncStorage from '@react-native-async-storage/async-storage';

// API配置
const API_CONFIG = {
  BASE_URL: 'http://192.168.1.41:8080', // 您的后端服务地址
  API_PREFIX: '/api/v1',
  TIMEOUT: 10000,
  RETRY_COUNT: 3,
};

// 接口前缀配置
export const API_PREFIXES = {
  AUTH: '/api/v1/auth',        // 认证接口
  APP: '/api/v1/app',          // C端用户接口
  ADMIN: '/api/v1/admin',      // 管理后台接口
  COMMON: '/api/v1',           // 通用接口
};

// Sa-Token认证头配置（基于您的测试结果）
const SATOKEN_CONFIG = {
  TOKEN_HEADER: 'satoken',     // Sa-Token期望的header名称
  TOKEN_PREFIX: 'Bearer ',     // Sa-Token期望的前缀
};

// 存储键名
export const STORAGE_KEYS = {
  ACCESS_TOKEN: 'access_token',
  USER_INFO: 'user_info',
  REFRESH_TOKEN: 'refresh_token',
};

// API响应类型定义
export interface ApiResponse<T = any> {
  code: number;
  message: string;
  data: T;
  error?: string;
  timestamp: string;
}

// 请求头类型
type Headers = Record<string, string>;

// 网络请求类
class ApiService {
  private static instance: ApiService;
  private accessToken: string | null = null;

  private constructor() {
    this.initializeToken();
  }

  public static getInstance(): ApiService {
    if (!ApiService.instance) {
      ApiService.instance = new ApiService();
    }
    return ApiService.instance;
  }

  // 初始化Token
  private async initializeToken() {
    try {
      this.accessToken = await AsyncStorage.getItem(STORAGE_KEYS.ACCESS_TOKEN);
    } catch (error) {
      console.warn('Failed to load token from storage:', error);
    }
  }

  // 设置Token
  public async setToken(token: string): Promise<void> {
    this.accessToken = token;
    await AsyncStorage.setItem(STORAGE_KEYS.ACCESS_TOKEN, token);
  }

  // 清除Token
  public async clearToken(): Promise<void> {
    this.accessToken = null;
    await AsyncStorage.removeItem(STORAGE_KEYS.ACCESS_TOKEN);
    await AsyncStorage.removeItem(STORAGE_KEYS.USER_INFO);
    await AsyncStorage.removeItem(STORAGE_KEYS.REFRESH_TOKEN);
  }

  // 构建请求头
  private buildHeaders(includeAuth: boolean = true): Headers {
    const headers: Headers = {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
    };

    // 添加Sa-Token认证头
    if (includeAuth && this.accessToken) {
      headers[SATOKEN_CONFIG.TOKEN_HEADER] = `${SATOKEN_CONFIG.TOKEN_PREFIX}${this.accessToken}`;
    }

    return headers;
  }

  // 基础请求方法
  private async request<T>(
    endpoint: string,
    options: RequestInit = {},
    includeAuth: boolean = true,
    customHeaders: Headers = {},
    customPrefix?: string
  ): Promise<ApiResponse<T>> {
    const prefix = customPrefix || API_CONFIG.API_PREFIX;
    const url = `${API_CONFIG.BASE_URL}${prefix}${endpoint}`;

    const requestOptions: RequestInit = {
      ...options,
      headers: {
        ...this.buildHeaders(includeAuth),
        ...customHeaders,
        ...options.headers,
      },
    };

    try {
      console.log(`🌐 API Request: ${options.method || 'GET'} ${url}`);

      const response = await fetch(url, requestOptions);

      console.log(`📡 API Response: ${response.status} ${response.statusText}`);

      if (!response.ok) {
        if (response.status === 401) {
          await this.clearToken();
          throw new Error('登录已过期，请重新登录');
        }

        if (response.status >= 500) {
          throw new Error('服务器内部错误，请稍后重试');
        }

        const errorData = await response.json().catch(() => null);
        throw new Error(errorData?.message || `请求失败: ${response.status}`);
      }

      const data: ApiResponse<T> = await response.json();

      if (data.code !== 200) {
        throw new Error(data.message || '请求失败');
      }

      return data;
    } catch (error: any) {
      console.error('API Request Error:', error);
      throw error;
    }
  }

  // GET请求
  public async get<T>(endpoint: string, includeAuth: boolean = true, customHeaders: Headers = {}, customPrefix?: string): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, { method: 'GET' }, includeAuth, customHeaders, customPrefix);
  }

  // POST请求
  public async post<T>(endpoint: string, data?: any, includeAuth: boolean = true, customHeaders: Headers = {}, customPrefix?: string): Promise<ApiResponse<T>> {
    return this.request<T>(
      endpoint,
      {
        method: 'POST',
        body: data ? JSON.stringify(data) : undefined,
      },
      includeAuth,
      customHeaders,
      customPrefix
    );
  }

  // PUT请求
  public async put<T>(endpoint: string, data?: any, includeAuth: boolean = true, customPrefix?: string): Promise<ApiResponse<T>> {
    return this.request<T>(
      endpoint,
      {
        method: 'PUT',
        body: data ? JSON.stringify(data) : undefined,
      },
      includeAuth,
      {},
      customPrefix
    );
  }

  // DELETE请求
  public async delete<T>(endpoint: string, includeAuth: boolean = true, customPrefix?: string): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, { method: 'DELETE' }, includeAuth, {}, customPrefix);
  }

  // 上传文件
  public async uploadFile<T>(endpoint: string, file: FormData): Promise<ApiResponse<T>> {
    const url = `${API_CONFIG.BASE_URL}${API_CONFIG.API_PREFIX}${endpoint}`;

    const headers: Headers = {};
    if (this.accessToken) {
      headers[SATOKEN_CONFIG.TOKEN_HEADER] = `${SATOKEN_CONFIG.TOKEN_PREFIX}${this.accessToken}`;
    }

    const response = await fetch(url, {
      method: 'POST',
      headers,
      body: file,
    });

    if (!response.ok) {
      throw new Error(`Upload failed: ${response.status}`);
    }

    return response.json();
  }
}

// 导出单例实例
export const apiService = ApiService.getInstance();

// 便捷的API调用方法
export const api = {
  get: <T>(endpoint: string, includeAuth = true, customHeaders = {}, customPrefix?: string) =>
    apiService.get<T>(endpoint, includeAuth, customHeaders, customPrefix),
  post: <T>(endpoint: string, data?: any, includeAuth = true, customHeaders = {}, customPrefix?: string) =>
    apiService.post<T>(endpoint, data, includeAuth, customHeaders, customPrefix),
  put: <T>(endpoint: string, data?: any, includeAuth = true, customPrefix?: string) =>
    apiService.put<T>(endpoint, data, includeAuth, customPrefix),
  delete: <T>(endpoint: string, includeAuth = true, customPrefix?: string) =>
    apiService.delete<T>(endpoint, includeAuth, customPrefix),
  upload: <T>(endpoint: string, file: FormData) => apiService.uploadFile<T>(endpoint, file),
  setToken: (token: string) => apiService.setToken(token),
  clearToken: () => apiService.clearToken(),
};

// 专用的API调用方法（按接口类型分类）
export const authApi = {
  get: <T>(endpoint: string, includeAuth = true, customHeaders = {}) =>
    api.get<T>(endpoint, includeAuth, customHeaders, API_PREFIXES.AUTH),
  post: <T>(endpoint: string, data?: any, includeAuth = true, customHeaders = {}) =>
    api.post<T>(endpoint, data, includeAuth, customHeaders, API_PREFIXES.AUTH),
};

export const appApi = {
  get: <T>(endpoint: string, includeAuth = true, customHeaders = {}) =>
    api.get<T>(endpoint, includeAuth, customHeaders, API_PREFIXES.APP),
  post: <T>(endpoint: string, data?: any, includeAuth = true, customHeaders = {}) =>
    api.post<T>(endpoint, data, includeAuth, customHeaders, API_PREFIXES.APP),
  put: <T>(endpoint: string, data?: any, includeAuth = true) =>
    api.put<T>(endpoint, data, includeAuth, API_PREFIXES.APP),
  delete: <T>(endpoint: string, includeAuth = true) =>
    api.delete<T>(endpoint, includeAuth, API_PREFIXES.APP),
};

export const adminApi = {
  get: <T>(endpoint: string, includeAuth = true, customHeaders = {}) =>
    api.get<T>(endpoint, includeAuth, customHeaders, API_PREFIXES.ADMIN),
  post: <T>(endpoint: string, data?: any, includeAuth = true, customHeaders = {}) =>
    api.post<T>(endpoint, data, includeAuth, customHeaders, API_PREFIXES.ADMIN),
  put: <T>(endpoint: string, data?: any, includeAuth = true) =>
    api.put<T>(endpoint, data, includeAuth, API_PREFIXES.ADMIN),
  delete: <T>(endpoint: string, includeAuth = true) =>
    api.delete<T>(endpoint, includeAuth, API_PREFIXES.ADMIN),
};
