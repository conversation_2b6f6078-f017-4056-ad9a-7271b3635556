import React from 'react';
import { View, ActivityIndicator, StyleSheet, Text } from 'react-native';
import { NavigationContainer } from '@react-navigation/native';
import { createStackNavigator } from '@react-navigation/stack';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';

import { LoginScreen } from '../screens/auth/LoginScreen';
import { MobileRegistrationScreen } from '../screens/auth/MobileRegistrationScreen';
import { HomeScreen } from '../screens/app/HomeScreen'; // 保留，但现在作为 "我的" 页面
import { MyGeeseScreen } from '../screens/app/MyGeeseScreen';
import { ShopScreen } from '../screens/shop/ShopScreen';

import { useAuth } from '../context/AuthContext';
import { theme } from '../theme';

// --- 路由参数定义 ---
export type AuthStackParamList = {
  Login: undefined;
  MobileRegistration: undefined;
};

export type AppTabParamList = {
  Discover: undefined;
  Shop: undefined;
  MyGeese: undefined;
  Updates: undefined;
  Profile: undefined;
};

// --- 导航器实例 ---
const AuthStack = createStackNavigator<AuthStackParamList>();
const AppTab = createBottomTabNavigator<AppTabParamList>();

// --- 占位屏幕 ---
const PlaceholderScreen = ({ route }: { route: { name: string } }) => (
  <View style={styles.centered}>
    <Text style={styles.placeholderText}>{route.name} 页面</Text>
    <Text>内容开发中...</Text>
  </View>
);

// --- 认证流程导航 ---
const AuthScreens = () => (
  <AuthStack.Navigator
    initialRouteName="Login"
    screenOptions={{ headerShown: false }}
  >
    <AuthStack.Screen name="Login" component={LoginScreen} />
    <AuthStack.Screen
      name="MobileRegistration"
      component={MobileRegistrationScreen}
      options={{
        headerShown: true,
        title: '手机号注册',
        headerBackTitle: '返回',
      }}
    />
  </AuthStack.Navigator>
);

// --- 主应用流程 (底部标签导航) ---
const AppScreens = () => (
  <AppTab.Navigator
    initialRouteName="MyGeese"
    screenOptions={{
      tabBarActiveTintColor: theme.colors.gooseYellow,
      tabBarInactiveTintColor: 'gray',
    }}
  >
    <AppTab.Screen
      name="Discover"
      component={PlaceholderScreen}
      options={{
        tabBarLabel: '首页',
        tabBarIcon: ({ color, size }) => (
          <MaterialCommunityIcons name="compass" color={color} size={size} />
        ),
        title: '发现'
      }}
    />
    <AppTab.Screen
      name="Shop"
      component={ShopScreen}
      options={{
        tabBarLabel: '商城',
        tabBarIcon: ({ color, size }) => (
          <MaterialCommunityIcons name="store" color={color} size={size} />
        ),
      }}
    />
    <AppTab.Screen
      name="MyGeese"
      component={MyGeeseScreen}
      options={{
        tabBarLabel: '我的小鹅',
        tabBarIcon: ({ color, size }) => (
          <MaterialCommunityIcons name="duck" color={color} size={size} />
        ),
        title: '我的小鹅'
      }}
    />
    <AppTab.Screen
      name="Updates"
      component={PlaceholderScreen}
      options={{
        tabBarLabel: '动态',
        tabBarIcon: ({ color, size }) => (
          <MaterialCommunityIcons name="newspaper-variant-multiple" color={color} size={size} />
        ),
      }}
    />
    <AppTab.Screen
      name="Profile"
      component={HomeScreen} // 复用之前的 HomeScreen 作为个人中心
      options={{
        tabBarLabel: '我的',
        tabBarIcon: ({ color, size }) => (
          <MaterialCommunityIcons name="account-circle" color={color} size={size} />
        ),
        title: '个人中心'
      }}
    />
  </AppTab.Navigator>
);


// --- 根导航器 ---
export const AppNavigator: React.FC = () => {
  const { state } = useAuth();

  if (state.isLoading) {
    return (
      <View style={styles.centered}>
        <ActivityIndicator size="large" color={theme.colors.gooseYellow} />
      </View>
    );
  }

  return (
    <NavigationContainer>
      {state.userToken == null ? <AuthScreens /> : <AppScreens />}
    </NavigationContainer>
  );
};

const styles = StyleSheet.create({
  centered: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: theme.colors.background,
  },
  placeholderText: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 10,
  }
});
