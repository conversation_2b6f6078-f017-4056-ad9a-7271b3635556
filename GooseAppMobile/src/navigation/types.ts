import { StackNavigationProp } from '@react-navigation/stack';
import { RouteProp } from '@react-navigation/native';

export type RootStackParamList = {
  Login: undefined;
  MobileRegistration: undefined;
};

export type LoginScreenNavigationProp = StackNavigationProp<
  RootStackParamList,
  'Login'
>;

export type MobileRegistrationScreenNavigationProp = StackNavigationProp<
  RootStackParamList,
  'MobileRegistration'
>;

export type LoginScreenRouteProp = RouteProp<RootStackParamList, 'Login'>;
export type MobileRegistrationScreenRouteProp = RouteProp<RootStackParamList, 'MobileRegistration'>;
