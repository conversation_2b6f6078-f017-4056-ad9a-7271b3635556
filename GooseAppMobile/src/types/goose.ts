/**
 * @file 小鹅相关的数据类型定义
 */

/**
 * @description 小鹅基础信息 DTO，对应后端的 GooseDTO
 */
export interface Goose {
  /**
   * @description 小鹅ID
   */
  id: number;
  /**
   * @description 所属用户ID
   */
  userId: number | null;
  /**
   * @description 小鹅名称
   */
  name: string | null;
  /**
   * @description 品种
   */
  breed: string | null;
  /**
   * @description 性别：1-雄性，2-雌性
   */
  gender: number | null;
  /**
   * @description 年龄（周）
   */
  age: number | null;
  /**
   * @description 健康度（0-100）
   */
  health: number | null;
  /**
   * @description 饥饿度（0-100）
   */
  hunger: number | null;
  /**
   * @description 口渴度（0-100）
   */
  thirst: number | null;
  /**
   * @description 清洁度（0-100）
   */
  cleanliness: number | null;
  /**
   * @description 快乐度（0-100）
   */
  happiness: number | null;
  /**
   * @description 等级
   */
  level: number | null;
  /**
   * @description 经验值
   */
  experience: number | null;
  /**
   * @description 下一级所需经验
   */
  expToNextLevel: number | null;
  /**
   * @description 小鹅图片URL
   */
  imageUrl: string | null;
  /**
   * @description 直播视频URL
   */
  videoUrl: string | null;
  /**
   * @description 归属状态：0-待售，1-已归属
   */
  ownershipStatus: number | null;
  /**
   * @description 归属状态描述
   */
  ownershipStatusDescription: string | null;
  /**
   * @description 购买时间 (格式: 'yyyy-MM-dd HH:mm:ss')
   */
  purchaseTime: string | null;
  /**
   * @description 状态：0-已转赠，1-正常，2-生病
   */
  status: number | null;
  /**
   * @description 状态描述
   */
  statusDescription: string | null;
  /**
   * @description 整体状态评分（0-100）
   */
  overallScore: number | null;
  /**
   * @description 是否需要照顾
   */
  needsCare: boolean | null;
  /**
   * @description 创建时间 (格式: 'yyyy-MM-dd HH:mm:ss')
   */
  createdAt: string | null;
  /**
   * @description 更新时间 (格式: 'yyyy-MM-dd HH:mm:ss')
   */
  updatedAt: string | null;
}