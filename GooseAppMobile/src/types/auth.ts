// 用户基本信息
export interface User {
  id: number;
  username: string;
  email: string;
  nickname: string;
  avatar?: string;
  phone?: string;
  createdAt: string;
  updatedAt: string;
}

// 用户统计信息
export interface UserStats {
  totalGeese: number;      // 拥有小鹅总数
  totalDays: number;       // 累计养成天数
  totalFeeds: number;      // 累计喂食次数
}

// 登录响应
export interface LoginResponse {
  userId: number;
  username: string;
  nickname: string;
  email: string;
  token: string;
  tokenType: string;       // "Bearer"
  expiresIn: number;       // token过期时间（秒）
  loginTime: string;       // 登录时间
  stats: UserStats;        // 用户统计信息
}
// 用户会话信息 (登录后存储在Context中)
export type UserSession = Omit<LoginResponse, 'token' | 'tokenType' | 'expiresIn'>;

// 注册请求参数
export interface RegisterRequest {
  username: string;
  email: string;
  password: string;
  confirmPassword: string;
  nickname: string;
  phone?: string;
}

// 登录请求参数
export interface LoginRequest {
  username: string;
  password: string;
}

// 手机号验证码登录请求参数
export interface PhoneLoginRequest {
  phone: string;
  code: string;
  deviceInfo?: string;
}

// 手机号密码登录请求参数
export interface PhonePasswordLoginRequest {
  phone: string;
  password: string;
  deviceInfo?: string;
}

// 发送短信验证码请求参数
export interface SendSmsCodeRequest {
  phone: string;
  codeType: SmsCodeType;
}

// 验证短信验证码请求参数
export interface VerifySmsCodeRequest {
  phone: string;
  code: string;
  codeType: SmsCodeType;
}

// C端用户创建账户请求参数
export interface CreateMobileAccountRequest {
  phone: string;
  code: string;
  nickname?: string;
  deviceInfo?: string;
}

// C端用户设置密码请求参数
export interface SetMobilePasswordRequest {
  password: string;
  confirmPassword: string;
  deviceInfo?: string;
}

// C端用户账户响应
export interface MobileAccountResponse {
  userId: number;
  username: string;
  nickname: string;
  avatar: string;
  email: string;
  phone: string;
  registrationStatus: RegistrationStatus;
  registrationStatusDesc: string;
  tempToken?: string;
  tempTokenExpiresAt?: string;
  createdAt: string;
  stats: UserStats;
}

// 注册状态响应
export interface RegistrationStatusResponse {
  userId: number;
  username: string;
  nickname: string;
  phone: string;
  registrationStatus: RegistrationStatus;
  registrationStatusDesc: string;
  tempTokenExpiresAt?: string;
  needSetPassword: boolean;
}

// 修改密码请求参数
export interface ChangePasswordRequest {
  oldPassword: string;
  newPassword: string;
  confirmPassword: string;
}

// 登录状态响应
export interface LoginStatus {
  isLogin: boolean;
  userId?: number;
  username?: string;
  loginTime?: string;
}

// 用户验证响应
export interface ValidationResponse {
  available: boolean;      // true表示可用，false表示已存在
}

// 短信验证码类型
export enum SmsCodeType {
  LOGIN = 1,        // 登录
  REGISTER = 2,     // 注册
  RESET_PASSWORD = 3, // 重置密码
  BIND_PHONE = 4,   // 绑定手机
}

// 注册状态
export enum RegistrationStatus {
  ACCOUNT_CREATED = 1,        // 已创建账户待设密码
  REGISTRATION_COMPLETED = 2, // 注册完成
}

// 用户类型
export enum UserType {
  MOBILE_USER = 1,  // C端用户(移动端)
  ADMIN_USER = 2,   // 管理后台用户
}

// 登录方式
export enum LoginType {
  USERNAME_PASSWORD = 1, // 用户名密码
  PHONE_SMS = 2,        // 手机验证码
}

// 认证错误类型
export enum AuthErrorCode {
  USER_NOT_FOUND = 6001,           // 用户不存在
  EMAIL_ALREADY_EXISTS = 6002,     // 邮箱已存在
  USERNAME_ALREADY_EXISTS = 6003,  // 用户名已存在
  PHONE_ALREADY_EXISTS = 6004,     // 手机号已存在
  INVALID_VERIFICATION_CODE = 6005, // 验证码错误
  PASSWORD_ERROR = 6006,           // 密码错误
  VALIDATION_ERROR = 6701,         // 参数验证错误
  TOKEN_EXPIRED = 6801,            // Token过期
  UNAUTHORIZED = 6802,             // 未授权访问
}

// 认证状态枚举
export enum AuthState {
  IDLE = 'idle',
  LOADING = 'loading',
  AUTHENTICATED = 'authenticated',
  UNAUTHENTICATED = 'unauthenticated',
  ERROR = 'error',
}

// 认证Context类型
export interface AuthContextType {
  // 状态
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;

  // 方法
  login: (credentials: LoginRequest) => Promise<void>;
  register: (userData: RegisterRequest) => Promise<void>;
  logout: () => Promise<void>;
  refreshUser: () => Promise<void>;
  changePassword: (data: ChangePasswordRequest) => Promise<void>;
  checkUsername: (username: string) => Promise<boolean>;
  checkEmail: (email: string) => Promise<boolean>;
  checkPhone: (phone: string) => Promise<boolean>;
  clearError: () => void;
}
