import { useQuery } from '@tanstack/react-query';
import { shopApi } from '../services/shopApi';
import { Goose } from '../types/goose';

// React Query 的查询键 (Query Keys)
export const productKeys = {
  all: ['products'] as const,
  lists: () => [...productKeys.all, 'list'] as const,
  list: (filters: string) => [...productKeys.lists(), { filters }] as const,
  details: () => [...productKeys.all, 'detail'] as const,
  detail: (id: number) => [...productKeys.details(), id] as const,
};

/**
 * 获取商品列表的自定义 Hook
 * 
 * 这个 Hook 封装了 useQuery，提供了获取商品列表所需的所有状态：
 * - data: 商品列表数据
 * - isLoading: 初始加载状态
 * - isFetching: 后台刷新状态
 * - isError: 是否发生错误
 * - error: 错误对象
 * - refetch: 手动重新获取数据的函数
 */
export const useProducts = () => {
  return useQuery<Goose[], Error>({
    queryKey: productKeys.lists(),
    queryFn: shopApi.fetchProducts,
    // 在这里可以配置 React Query 的各种高级功能，例如：
    // staleTime: 5 * 60 * 1000, // 5 分钟内数据被认为是新鲜的，不会重新触发请求
    // cacheTime: 10 * 60 * 1000, // 数据在 10 分钟后从缓存中清除
    // refetchOnWindowFocus: false, // 窗口重新获得焦点时，不自动重新获取数据
  });
};