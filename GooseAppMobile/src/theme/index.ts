import { MD3LightTheme, MD3DarkTheme } from 'react-native-paper';

// 小鹅养成APP 色彩系统（基于UI设计文档）
export const colors = {
  // 主色调
  gooseYellow: '#FFD93D',    // 小鹅黄
  grassGreen: '#7CB342',     // 草地绿
  skyBlue: '#42A5F5',        // 天空蓝

  // 辅助色
  warmOrange: '#FF8A65',     // 温暖橙
  softPink: '#F8BBD9',       // 柔和粉
  neutralGray: '#757575',    // 中性灰

  // 状态色
  successGreen: '#4CAF50',   // 成功绿
  warningOrange: '#FF9800',  // 警告橙
  errorRed: '#F44336',       // 错误红

  // 背景色
  background: '#FAFAFA',     // 浅灰背景
  surface: '#FFFFFF',        // 表面白色

  // 文字色
  textPrimary: '#212121',    // 主要文字
  textSecondary: '#757575',  // 次要文字
  textHint: '#BDBDBD',       // 提示文字
};

// 字体配置
export const fonts = {
  sizes: {
    xs: 10,
    sm: 12,
    base: 14,
    md: 16,
    lg: 18,
    xl: 20,
    xxl: 24,
    xxxl: 32,
  },
  weights: {
    light: '300' as const,
    normal: '400' as const,
    medium: '500' as const,
    semibold: '600' as const,
    bold: '700' as const,
  },
};

// 间距系统
export const spacing = {
  xs: 4,
  sm: 8,
  md: 12,
  lg: 16,
  xl: 20,
  xxl: 24,
  xxxl: 32,
};

// 圆角系统
export const borderRadius = {
  xs: 4,
  sm: 8,
  md: 12,
  lg: 16,
  xl: 20,
  round: 9999,
};

// React Native Paper 主题配置
export const lightTheme = {
  ...MD3LightTheme,
  colors: {
    ...MD3LightTheme.colors,
    primary: colors.gooseYellow,
    secondary: colors.grassGreen,
    tertiary: colors.skyBlue,
    surface: colors.surface,
    background: colors.background,
    error: colors.errorRed,
    onPrimary: colors.textPrimary,
    onSecondary: colors.textPrimary,
    onSurface: colors.textPrimary,
    onBackground: colors.textPrimary,
  },
};

export const darkTheme = {
  ...MD3DarkTheme,
  colors: {
    ...MD3DarkTheme.colors,
    primary: colors.gooseYellow,
    secondary: colors.grassGreen,
    tertiary: colors.skyBlue,
    error: colors.errorRed,
  },
};

export const theme = {
  colors,
  fonts,
  spacing,
  borderRadius,
  light: lightTheme,
  dark: darkTheme,
};
