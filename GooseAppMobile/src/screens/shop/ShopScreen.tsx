import React from 'react';
import { View, StyleSheet, FlatList, Text } from 'react-native';
import { Button, Card, Title, Paragraph } from 'react-native-paper';
import { useProducts } from '../../hooks/useProducts';
import { useShop } from '../../context/ShopContext';
import { theme } from '../../theme';
import LoadingIndicator from '../../components/LoadingIndicator';
import ErrorMessage from '../../components/ErrorMessage';
import { Goose } from '../../types/goose';

export const ShopScreen: React.FC = () => {
  const { data: products, isLoading, isError, error } = useProducts();
  const { dispatch } = useShop();

  const handleAddToCart = (product: Goose) => {
    dispatch({ type: 'ADD_TO_CART', payload: product });
  };

  if (isLoading) {
    return <LoadingIndicator />;
  }

  if (isError) {
    return <ErrorMessage message={error?.message || '加载商品失败'} />;
  }

  const renderItem = ({ item }: { item: Goose }) => (
    <Card style={styles.card}>
      <Card.Cover source={{ uri: item.imageUrl || 'https://via.placeholder.com/300' }} />
      <Card.Content>
        <Title>{item.name}</Title>
        <Paragraph>品种: {item.breed || '未知'}</Paragraph>
        <Paragraph style={styles.price}>健康值: {item.health || 'N/A'}</Paragraph>
      </Card.Content>
      <Card.Actions>
        <Button onPress={() => handleAddToCart(item)}>加入购物车</Button>
      </Card.Actions>
    </Card>
  );

  return (
    <FlatList
      data={products}
      renderItem={renderItem}
      keyExtractor={(item) => item.id.toString()}
      contentContainerStyle={styles.container}
      ListEmptyComponent={<Text>没有可用的商品</Text>}
    />
  );
};

const styles = StyleSheet.create({
  container: {
    padding: theme.spacing.md,
  },
  card: {
    marginBottom: theme.spacing.md,
  },
  price: {
    fontWeight: theme.fonts.weights.bold,
    fontSize: theme.fonts.sizes.lg,
    color: theme.colors.gooseYellow,
    marginTop: theme.spacing.sm,
  },
});