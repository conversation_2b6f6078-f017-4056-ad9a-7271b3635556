import React from 'react';
import { View, StyleSheet } from 'react-native';
import { Button, Text, Card, Title, Paragraph } from 'react-native-paper';
import { useAuth } from '../../context/AuthContext';
import { theme } from '../../theme';

export const HomeScreen: React.FC = () => {
  const { logout, state } = useAuth();
  const { user } = state;

  return (
    <View style={styles.container}>
      <Card style={styles.card}>
        <Card.Content>
          <Title style={styles.title}>欢迎回来！</Title>
          <Paragraph style={styles.paragraph}>
            {user ? `你好, ${user.nickname}` : '你好, 游客'}
          </Paragraph>
          {user && (
            <>
              <Paragraph style={styles.info}>ID: {user.userId}</Paragraph>
              <Paragraph style={styles.info}>用户名: {user.username}</Paragraph>
              <Paragraph style={styles.info}>邮箱: {user.email}</Paragraph>
            </>
          )}
          <Button 
            mode="contained" 
            onPress={() => logout.mutate()}
            loading={logout.isPending}
            disabled={logout.isPending}
            style={styles.button}
            icon="logout"
          >
            退出登录
          </Button>
        </Card.Content>
      </Card>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    padding: theme.spacing.lg,
    backgroundColor: theme.colors.background,
  },
  card: {
    elevation: 4,
    borderRadius: theme.borderRadius.lg,
  },
  title: {
    textAlign: 'center',
    fontSize: theme.fonts.sizes.xl,
    fontWeight: theme.fonts.weights.bold,
    marginBottom: theme.spacing.md,
  },
  paragraph: {
    textAlign: 'center',
    fontSize: theme.fonts.sizes.lg,
    marginBottom: theme.spacing.lg,
  },
  info: {
    fontSize: theme.fonts.sizes.md,
    color: theme.colors.textSecondary,
    marginBottom: theme.spacing.sm,
  },
  button: {
    marginTop: theme.spacing.xl,
  },
});