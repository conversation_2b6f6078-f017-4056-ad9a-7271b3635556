import React, { useState } from 'react';
import { View, StyleSheet, ScrollView } from 'react-native';
import {
  TextInput,
  Button,
  Card,
  Title,
  Paragraph,
  ActivityIndicator,
  Snackbar,
  ProgressBar,
} from 'react-native-paper';
import { useNavigation } from '@react-navigation/native';
import { authApi } from '../../services/authApi';
import { SmsCodeType, CreateMobileAccountRequest, MobileAccountResponse } from '../../types/auth';
import { MobileRegistrationScreenNavigationProp } from '../../navigation/types';
import { theme } from '../../theme';

// 注册步骤枚举
enum RegistrationStep {
  PHONE_INPUT = 1,      // 输入手机号
  VERIFY_CODE = 2,      // 验证验证码
  SET_PASSWORD = 3,     // 设置密码
  COMPLETED = 4,        // 注册完成
}

export const MobileRegistrationScreen: React.FC = () => {
  const navigation = useNavigation<MobileRegistrationScreenNavigationProp>();

  // 状态管理
  const [currentStep, setCurrentStep] = useState<RegistrationStep>(RegistrationStep.PHONE_INPUT);
  const [loading, setLoading] = useState(false);
  const [countdown, setCountdown] = useState(0);
  
  // 表单数据
  const [formData, setFormData] = useState({
    phone: '',
    code: '',
    nickname: '',
    password: '',
    confirmPassword: '',
  });
  
  // 临时数据
  const [tempToken, setTempToken] = useState<string>('');
  const [accountData, setAccountData] = useState<MobileAccountResponse | null>(null);
  
  // 消息提示
  const [snackbar, setSnackbar] = useState({
    visible: false,
    message: '',
    type: 'error' as 'success' | 'error',
  });

  // 显示消息
  const showMessage = (message: string, type: 'success' | 'error' = 'error') => {
    setSnackbar({ visible: true, message, type });
  };

  // 验证手机号格式
  const validatePhone = (phone: string): boolean => {
    const phoneRegex = /^1[3-9]\d{9}$/;
    return phoneRegex.test(phone);
  };

  // 验证密码强度
  const validatePassword = (password: string): boolean => {
    const passwordRegex = /^(?=.*[A-Za-z])(?=.*\d)[A-Za-z\d@$!%*#?&]{8,20}$/;
    return passwordRegex.test(password);
  };

  // 开始倒计时
  const startCountdown = () => {
    setCountdown(60);
    const timer = setInterval(() => {
      setCountdown((prev) => {
        if (prev <= 1) {
          clearInterval(timer);
          return 0;
        }
        return prev - 1;
      });
    }, 1000);
  };

  // 第一步：发送验证码
  const handleSendCode = async () => {
    if (!validatePhone(formData.phone)) {
      showMessage('请输入正确的手机号格式');
      return;
    }

    setLoading(true);
    try {
      await authApi.sendSmsCodeV2({
        phone: formData.phone,
        codeType: SmsCodeType.REGISTER,
      });

      showMessage('验证码发送成功', 'success');
      startCountdown();
      setCurrentStep(RegistrationStep.VERIFY_CODE);
    } catch (error: any) {
      showMessage(error.message || '验证码发送失败');
    } finally {
      setLoading(false);
    }
  };

  // 第二步：创建账户
  const handleCreateAccount = async () => {
    if (!formData.code || formData.code.length !== 6) {
      showMessage('请输入6位验证码');
      return;
    }

    setLoading(true);
    try {
      const request: CreateMobileAccountRequest = {
        phone: formData.phone,
        code: formData.code,
        nickname: formData.nickname || undefined,
        deviceInfo: 'React Native App',
      };

      const response = await authApi.createMobileAccount(request);
      
      setAccountData(response);
      setTempToken(response.tempToken || '');
      showMessage('账户创建成功！请设置登录密码', 'success');
      setCurrentStep(RegistrationStep.SET_PASSWORD);
    } catch (error: any) {
      showMessage(error.message || '账户创建失败');
    } finally {
      setLoading(false);
    }
  };

  // 第三步：设置密码
  const handleSetPassword = async () => {
    if (!validatePassword(formData.password)) {
      showMessage('密码必须8-20位，包含字母和数字');
      return;
    }

    if (formData.password !== formData.confirmPassword) {
      showMessage('两次输入的密码不一致');
      return;
    }

    setLoading(true);
    try {
      const response = await authApi.setMobilePassword({
        password: formData.password,
        confirmPassword: formData.confirmPassword,
        deviceInfo: 'React Native App',
      }, tempToken);

      showMessage(`注册成功！欢迎，${response.nickname}`, 'success');
      setCurrentStep(RegistrationStep.COMPLETED);
      
      // 这里可以导航到主页面
      console.log('Registration completed:', response);
    } catch (error: any) {
      showMessage(error.message || '密码设置失败');
    } finally {
      setLoading(false);
    }
  };

  // 重新开始
  const handleRestart = () => {
    setCurrentStep(RegistrationStep.PHONE_INPUT);
    setFormData({
      phone: '',
      code: '',
      nickname: '',
      password: '',
      confirmPassword: '',
    });
    setTempToken('');
    setAccountData(null);
    setCountdown(0);
  };

  // 返回登录页面
  const handleBackToLogin = () => {
    navigation.navigate('Login');
  };

  // 获取进度值
  const getProgress = (): number => {
    return (currentStep - 1) / 3; // 3个主要步骤
  };

  // 获取步骤标题
  const getStepTitle = (): string => {
    switch (currentStep) {
      case RegistrationStep.PHONE_INPUT:
        return '📱 输入手机号';
      case RegistrationStep.VERIFY_CODE:
        return '🔐 验证手机号';
      case RegistrationStep.SET_PASSWORD:
        return '🔑 设置密码';
      case RegistrationStep.COMPLETED:
        return '🎉 注册完成';
      default:
        return '';
    }
  };

  return (
    <ScrollView style={styles.container}>
      <Card style={styles.card}>
        <Card.Content>
          <Title style={styles.title}>🦆 小鹅养成APP</Title>
          <Paragraph style={styles.subtitle}>C端用户注册</Paragraph>

          {/* 进度条 */}
          <View style={styles.progressContainer}>
            <Paragraph style={styles.stepTitle}>{getStepTitle()}</Paragraph>
            <ProgressBar 
              progress={getProgress()} 
              color={theme.colors.gooseYellow}
              style={styles.progressBar}
            />
            <Paragraph style={styles.stepInfo}>
              第 {currentStep} 步，共 3 步
            </Paragraph>
          </View>

          {/* 第一步：输入手机号 */}
          {currentStep === RegistrationStep.PHONE_INPUT && (
            <View style={styles.stepContainer}>
              <TextInput
                label="手机号"
                value={formData.phone}
                onChangeText={(text) => setFormData(prev => ({ ...prev, phone: text }))}
                mode="outlined"
                style={styles.input}
                disabled={loading}
                keyboardType="phone-pad"
                maxLength={11}
                placeholder="请输入11位手机号"
              />

              <TextInput
                label="昵称（可选）"
                value={formData.nickname}
                onChangeText={(text) => setFormData(prev => ({ ...prev, nickname: text }))}
                mode="outlined"
                style={styles.input}
                disabled={loading}
                placeholder="不填写将自动生成"
              />

              <Button
                mode="contained"
                onPress={handleSendCode}
                loading={loading}
                disabled={loading || !formData.phone}
                style={styles.button}
                contentStyle={styles.buttonContent}
              >
                发送验证码
              </Button>
            </View>
          )}

          {/* 第二步：验证验证码 */}
          {currentStep === RegistrationStep.VERIFY_CODE && (
            <View style={styles.stepContainer}>
              <Paragraph style={styles.phoneDisplay}>
                验证码已发送至：{formData.phone.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2')}
              </Paragraph>

              <TextInput
                label="验证码"
                value={formData.code}
                onChangeText={(text) => setFormData(prev => ({ ...prev, code: text }))}
                mode="outlined"
                style={styles.input}
                disabled={loading}
                keyboardType="number-pad"
                maxLength={6}
                placeholder="请输入6位验证码"
              />

              <View style={styles.buttonRow}>
                <Button
                  mode="outlined"
                  onPress={handleSendCode}
                  disabled={loading || countdown > 0}
                  style={[styles.button, styles.halfButton]}
                >
                  {countdown > 0 ? `重发(${countdown}s)` : '重新发送'}
                </Button>

                <Button
                  mode="contained"
                  onPress={handleCreateAccount}
                  loading={loading}
                  disabled={loading || !formData.code}
                  style={[styles.button, styles.halfButton]}
                  contentStyle={styles.buttonContent}
                >
                  创建账户
                </Button>
              </View>
            </View>
          )}

          {/* 第三步：设置密码 */}
          {currentStep === RegistrationStep.SET_PASSWORD && (
            <View style={styles.stepContainer}>
              <Paragraph style={styles.successMessage}>
                🎉 账户创建成功！
              </Paragraph>
              <Paragraph style={styles.accountInfo}>
                用户名：{accountData?.username}
              </Paragraph>
              <Paragraph style={styles.accountInfo}>
                昵称：{accountData?.nickname}
              </Paragraph>

              <TextInput
                label="登录密码"
                value={formData.password}
                onChangeText={(text) => setFormData(prev => ({ ...prev, password: text }))}
                mode="outlined"
                style={styles.input}
                disabled={loading}
                secureTextEntry
                placeholder="8-20位，包含字母和数字"
              />

              <TextInput
                label="确认密码"
                value={formData.confirmPassword}
                onChangeText={(text) => setFormData(prev => ({ ...prev, confirmPassword: text }))}
                mode="outlined"
                style={styles.input}
                disabled={loading}
                secureTextEntry
                placeholder="请再次输入密码"
              />

              <Button
                mode="contained"
                onPress={handleSetPassword}
                loading={loading}
                disabled={loading || !formData.password || !formData.confirmPassword}
                style={styles.button}
                contentStyle={styles.buttonContent}
              >
                完成注册
              </Button>
            </View>
          )}

          {/* 第四步：注册完成 */}
          {currentStep === RegistrationStep.COMPLETED && (
            <View style={styles.stepContainer}>
              <Paragraph style={styles.completedMessage}>
                🎉 恭喜！注册成功
              </Paragraph>
              <Paragraph style={styles.completedInfo}>
                您已成功注册小鹅养成APP，现在可以开始您的养鹅之旅了！
              </Paragraph>

              <Button
                mode="contained"
                onPress={handleBackToLogin}
                style={styles.button}
                contentStyle={styles.buttonContent}
              >
                返回登录
              </Button>
            </View>
          )}

          {loading && (
            <View style={styles.loadingContainer}>
              <ActivityIndicator size="small" color={theme.colors.gooseYellow} />
              <Paragraph style={styles.loadingText}>处理中...</Paragraph>
            </View>
          )}
        </Card.Content>
      </Card>

      <Snackbar
        visible={snackbar.visible}
        onDismiss={() => setSnackbar(prev => ({ ...prev, visible: false }))}
        duration={3000}
        style={[
          styles.snackbar,
          { backgroundColor: snackbar.type === 'success' ? theme.colors.successGreen : theme.colors.errorRed },
        ]}
      >
        {snackbar.message}
      </Snackbar>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  card: {
    margin: theme.spacing.lg,
    elevation: 4,
    borderRadius: theme.borderRadius.lg,
  },
  title: {
    textAlign: 'center',
    fontSize: theme.fonts.sizes.xxl,
    fontWeight: theme.fonts.weights.bold,
    color: theme.colors.gooseYellow,
    marginBottom: theme.spacing.sm,
  },
  subtitle: {
    textAlign: 'center',
    fontSize: theme.fonts.sizes.md,
    color: theme.colors.textSecondary,
    marginBottom: theme.spacing.xl,
  },
  progressContainer: {
    marginBottom: theme.spacing.xl,
  },
  stepTitle: {
    textAlign: 'center',
    fontSize: theme.fonts.sizes.lg,
    fontWeight: theme.fonts.weights.medium,
    color: theme.colors.textPrimary,
    marginBottom: theme.spacing.sm,
  },
  progressBar: {
    height: 6,
    borderRadius: 3,
    marginBottom: theme.spacing.sm,
  },
  stepInfo: {
    textAlign: 'center',
    fontSize: theme.fonts.sizes.sm,
    color: theme.colors.textSecondary,
  },
  stepContainer: {
    marginTop: theme.spacing.lg,
  },
  input: {
    marginBottom: theme.spacing.md,
  },
  button: {
    marginTop: theme.spacing.md,
    borderRadius: theme.borderRadius.md,
  },
  buttonContent: {
    paddingVertical: theme.spacing.sm,
  },
  buttonRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    gap: theme.spacing.md,
  },
  halfButton: {
    flex: 1,
  },
  phoneDisplay: {
    textAlign: 'center',
    fontSize: theme.fonts.sizes.md,
    color: theme.colors.textSecondary,
    marginBottom: theme.spacing.lg,
  },
  successMessage: {
    textAlign: 'center',
    fontSize: theme.fonts.sizes.lg,
    fontWeight: theme.fonts.weights.medium,
    color: theme.colors.successGreen,
    marginBottom: theme.spacing.md,
  },
  accountInfo: {
    textAlign: 'center',
    fontSize: theme.fonts.sizes.md,
    color: theme.colors.textSecondary,
    marginBottom: theme.spacing.sm,
  },
  completedMessage: {
    textAlign: 'center',
    fontSize: theme.fonts.sizes.xl,
    fontWeight: theme.fonts.weights.bold,
    color: theme.colors.successGreen,
    marginBottom: theme.spacing.lg,
  },
  completedInfo: {
    textAlign: 'center',
    fontSize: theme.fonts.sizes.md,
    color: theme.colors.textSecondary,
    marginBottom: theme.spacing.xl,
  },
  loadingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: theme.spacing.lg,
  },
  loadingText: {
    marginLeft: theme.spacing.sm,
    color: theme.colors.textSecondary,
  },
  snackbar: {
    margin: theme.spacing.lg,
  },
});
