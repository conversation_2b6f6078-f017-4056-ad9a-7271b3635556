import React from 'react';
import { View, Text, StyleSheet, ViewStyle } from 'react-native';

interface ErrorMessageProps {
  message: string | null;
  style?: ViewStyle;
}

const ErrorMessage: React.FC<ErrorMessageProps> = ({ message, style }) => {
  if (!message) {
    return null;
  }

  return (
    <View style={[styles.container, style]}>
      <Text style={styles.text}>{message}</Text>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    padding: 10,
    backgroundColor: '#FFD2D2',
    borderRadius: 8,
    marginVertical: 10,
  },
  text: {
    color: '#D8000C',
    fontSize: 14,
    textAlign: 'center',
  },
});

export default ErrorMessage;