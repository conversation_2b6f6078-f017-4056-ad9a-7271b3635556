#!/bin/bash

# 小鹅养成APP - 测试环境启动脚本
# 使用方法：./start-test-environment.sh

echo "🚀 启动小鹅养成APP测试环境..."

# 1. 检查Docker是否运行
echo "🔍 检查Docker状态..."
if ! docker ps >/dev/null 2>&1; then
    echo "❌ Docker未运行，请先启动Docker"
    exit 1
fi

# 2. 检查MySQL容器状态
echo "🐬 检查MySQL容器状态..."
if docker ps | grep -q mysql; then
    echo "✅ MySQL容器已运行"
else
    echo "⚠️  MySQL容器未运行，请确保MySQL容器正常启动"
    echo "容器名称应为: mysql"
    echo "端口映射: 3306:3306"
    exit 1
fi

# 3. 等待MySQL准备就绪
echo "⏳ 等待MySQL准备就绪..."
while ! docker exec mysql mysql -u root -p123456 -e "SELECT 1" >/dev/null 2>&1; do
    echo "   等待MySQL启动..."
    sleep 2
done
echo "✅ MySQL准备就绪"

# 4. 验证数据库表结构
echo "🗄️ 验证数据库表结构..."
TABLES=$(docker exec mysql mysql -u root -p123456 -e "USE goose_app; SHOW TABLES;" 2>/dev/null | grep -v Tables)
if [[ -z "$TABLES" ]]; then
    echo "❌ 数据库表未创建，请先执行数据库迁移"
    echo "建议执行: docker exec mysql mysql -u root -p123456 goose_app < scripts/migration/001_ownership_refactor.sql"
    exit 1
fi
echo "✅ 数据库表结构正常"

# 5. 进入后端目录
cd goose-app-backend || {
    echo "❌ 找不到goose-app-backend目录"
    exit 1
}

# 6. 检查Maven依赖
echo "📦 检查Maven依赖..."
if [ ! -d "target" ]; then
    echo "⚠️  未找到编译目录，正在执行Maven编译..."
    mvn clean compile -q
fi

# 7. 启动Spring Boot应用
echo "🌱 启动Spring Boot应用..."
echo "访问地址: http://localhost:8080"
echo "健康检查: http://localhost:8080/actuator/health"
echo ""
echo "📝 测试准备清单："
echo "   1. ✅ MySQL容器运行中"
echo "   2. ✅ 数据库表结构正常"
echo "   3. ✅ 后端应用即将启动"
echo ""
echo "🧪 开始测试步骤："
echo "   1. 导入Postman集合: 小鹅养成APP-Postman测试集合.json"
echo "   2. 配置环境变量: base_url = http://localhost:8080"
echo "   3. 按照测试文档执行用例"
echo ""
echo "按 Ctrl+C 停止服务"
echo "============================================"

# 启动应用（前台运行，便于查看日志）
mvn spring-boot:run 