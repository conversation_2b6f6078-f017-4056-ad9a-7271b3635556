# 🦆 小鹅养成APP - 全栈开发方案

## 📋 项目概述

**项目名称：** 小鹅养成APP
**技术栈：** Java + Spring Boot + MySQL + Sa-Token + React Native
**架构模式：** 分层架构（Controller-Service-Repository-Entity）
**开发工具：** Maven + IntelliJ IDEA + VS Code + React Native CLI
**版本：** v1.0
**制定时间：** 2024年12月19日
**React Native方案验证时间：** 2024年12月19日（基于最新官方文档）

---

## 📑 目录

1. [项目架构设计](#1-项目架构设计)
2. [React Native前端架构](#2-react-native前端架构)
3. [数据库设计方案](#3-数据库设计方案)
4. [RESTful API接口设计规范](#4-restful-api接口设计规范)
5. [前后端交互方案](#5-前后端交互方案)
6. [Sa-Token认证授权实现方案](#6-sa-token认证授权实现方案)
7. [开发环境搭建指南](#7-开发环境搭建指南)
8. [项目目录结构规划](#8-项目目录结构规划)
9. [开发阶段划分和里程碑计划](#9-开发阶段划分和里程碑计划)

---

## 1. 项目架构设计

### 1.1 整体架构图

```
┌─────────────────────────────────────────────────────────────┐
│                React Native 移动端应用                         │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐           │
│  │ Navigation  │ │    State    │ │  UI Layer   │           │
│  │    Stack    │ │ Management  │ │  (Paper)    │           │
│  └─────────────┘ └─────────────┘ └─────────────┘           │
├─────────────────────────────────────────────────────────────┤
│                    HTTP/HTTPS 通信层 (Fetch API)             │
├─────────────────────────────────────────────────────────────┤
│                    Spring Boot 后端服务                     │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐           │
│  │ Controller  │ │   Service   │ │ Repository  │           │
│  │    层       │ │     层      │ │     层      │           │
│  └─────────────┘ └─────────────┘ └─────────────┘           │
├─────────────────────────────────────────────────────────────┤
│                    MySQL 数据库                             │
└─────────────────────────────────────────────────────────────┘
```

### 1.2 分层架构设计

#### Controller层（控制器层）
- **职责**：处理HTTP请求，参数验证，响应格式化
- **组件**：
  - `UserController` - 用户管理
  - `GooseController` - 小鹅管理
  - `ShopController` - 商城功能
  - `InteractionController` - 互动操作
  - `GiftController` - 转赠功能
  - `OrderController` - 订单管理

#### Service层（业务逻辑层）
- **职责**：核心业务逻辑，事务管理，数据处理
- **组件**：
  - `UserService` - 用户业务逻辑
  - `GooseService` - 小鹅养成逻辑
  - `ShopService` - 商城业务逻辑
  - `InteractionService` - 互动业务逻辑
  - `GiftService` - 转赠业务逻辑
  - `OrderService` - 订单业务逻辑

#### Repository层（数据访问层）
- **职责**：数据库操作，数据持久化
- **组件**：
  - `UserRepository` - 用户数据访问
  - `GooseRepository` - 小鹅数据访问
  - `ProductRepository` - 商品数据访问
  - `InteractionRepository` - 互动记录访问
  - `OrderRepository` - 订单数据访问

#### Entity层（实体层）
- **职责**：数据模型定义，数据库映射
- **组件**：
  - `User` - 用户实体
  - `Goose` - 小鹅实体
  - `Product` - 商品实体
  - `Interaction` - 互动记录实体
  - `Order` - 订单实体

### 1.3 模块划分策略

#### 用户模块（User Module）
```
com.gooseapp.user
├── controller/
│   └── UserController.java
├── service/
│   ├── UserService.java
│   └── impl/
│       └── UserServiceImpl.java
├── repository/
│   └── UserRepository.java
├── entity/
│   └── User.java
└── dto/
    ├── UserLoginDTO.java
    ├── UserRegisterDTO.java
    └── UserProfileDTO.java
```

#### 小鹅养成模块（Goose Module）
```
com.gooseapp.goose
├── controller/
│   ├── GooseController.java
│   └── InteractionController.java
├── service/
│   ├── GooseService.java
│   ├── InteractionService.java
│   └── impl/
│       ├── GooseServiceImpl.java
│       └── InteractionServiceImpl.java
├── repository/
│   ├── GooseRepository.java
│   └── InteractionRepository.java
├── entity/
│   ├── Goose.java
│   └── Interaction.java
└── dto/
    ├── GooseDetailDTO.java
    ├── GooseStatusDTO.java
    └── InteractionDTO.java
```

#### 商城模块（Shop Module）
```
com.gooseapp.shop
├── controller/
│   ├── ShopController.java
│   └── OrderController.java
├── service/
│   ├── ShopService.java
│   ├── OrderService.java
│   └── impl/
│       ├── ShopServiceImpl.java
│       └── OrderServiceImpl.java
├── repository/
│   ├── ProductRepository.java
│   └── OrderRepository.java
├── entity/
│   ├── Product.java
│   ├── Order.java
│   └── OrderItem.java
└── dto/
    ├── ProductDTO.java
    ├── OrderDTO.java
    └── CartItemDTO.java
```

#### 转赠模块（Gift Module）
```
com.gooseapp.gift
├── controller/
│   └── GiftController.java
├── service/
│   ├── GiftService.java
│   └── impl/
│       └── GiftServiceImpl.java
├── repository/
│   └── GiftRepository.java
├── entity/
│   └── Gift.java
└── dto/
    ├── GiftDTO.java
    └── GiftCardDTO.java
```

### 1.4 微服务架构考虑

虽然当前采用单体架构，但设计时考虑未来微服务拆分：

#### 潜在微服务拆分点
1. **用户服务**：用户管理、认证授权
2. **小鹅服务**：小鹅管理、状态更新、互动逻辑
3. **商城服务**：商品管理、订单处理、支付集成
4. **消息服务**：推送通知、消息队列
5. **文件服务**：图片上传、视频处理

#### 服务间通信预留
- 使用统一的DTO格式
- 预留服务发现机制
- 设计无状态的Service层
- 数据库按模块独立设计

---

## 2. React Native前端架构

### 2.1 技术栈验证（基于最新官方文档）

#### 核心技术栈选择
- **React Native**: v0.73+ （经过文档验证，稳定可靠）
- **React Navigation**: v7.x （Native Stack Navigator + Tab Navigator）
- **状态管理**: Context API + useReducer （内置方案，无需第三方依赖）
- **网络请求**: Fetch API （React Native内置，官方推荐）
- **UI组件库**: React Native Paper v5.x （Material Design 3支持）
- **本地存储**: AsyncStorage （官方推荐）
- **图标**: @react-native-vector-icons/material-design-icons

### 2.2 状态管理

#### 使用Context API + useReducer进行状态管理
```javascript
// src/context/UserContext.js
import React, { createContext, useContext, useReducer } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';

const UserContext = createContext();

const userReducer = (state, action) => {
  switch (action.type) {
    case 'LOGIN_START':
      return { ...state, loading: true, error: null };
    case 'LOGIN_SUCCESS':
      return { ...state, loading: false, user: action.payload, isAuthenticated: true };
    case 'LOGIN_ERROR':
      return { ...state, loading: false, error: action.payload };
    case 'LOGOUT':
      return { ...state, user: null, isAuthenticated: false };
    case 'UPDATE_PROFILE':
      return { ...state, user: { ...state.user, ...action.payload } };
    default:
      return state;
  }
};

const initialState = {
  user: null,
  isAuthenticated: false,
  loading: false,
  error: null,
};

export const UserProvider = ({ children }) => {
  const [state, dispatch] = useReducer(userReducer, initialState);

  const login = async (username, password) => {
    dispatch({ type: 'LOGIN_START' });
    try {
      const response = await fetch('/api/v1/auth/login', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ username, password }),
      });
      
      if (!response.ok) throw new Error('登录失败');
      
      const userData = await response.json();
      // 保存用户信息到本地存储
      await AsyncStorage.setItem('user', JSON.stringify(userData.data));
      dispatch({ type: 'LOGIN_SUCCESS', payload: userData.data });
    } catch (error) {
      dispatch({ type: 'LOGIN_ERROR', payload: error.message });
    }
  };

  const logout = async () => {
    await AsyncStorage.removeItem('user');
    await AsyncStorage.removeItem('token');
    dispatch({ type: 'LOGOUT' });
  };

  const loadProfile = async () => {
    try {
      const response = await fetch('/api/v1/user/profile');
      if (!response.ok) throw new Error('获取用户信息失败');
      
      const userData = await response.json();
      dispatch({ type: 'UPDATE_PROFILE', payload: userData.data });
    } catch (error) {
      console.error('加载用户信息失败:', error);
    }
  };

  return (
    <UserContext.Provider value={{ ...state, login, logout, loadProfile, dispatch }}>
      {children}
    </UserContext.Provider>
  );
};

export const useUser = () => {
  const context = useContext(UserContext);
  if (!context) {
    throw new Error('useUser must be used within UserProvider');
  }
  return context;
};
```

#### 小鹅状态管理
```javascript
// src/context/GooseContext.js
import React, { createContext, useContext, useReducer } from 'react';
import { GooseService } from '../services/ApiService';

const GooseContext = createContext();

const gooseReducer = (state, action) => {
  switch (action.type) {
    case 'LOAD_GEESE_START':
      return { ...state, loading: true, error: null };
    case 'LOAD_GEESE_SUCCESS':
      return { ...state, loading: false, geese: action.payload };
    case 'LOAD_GEESE_ERROR':
      return { ...state, loading: false, error: action.payload };
    case 'UPDATE_GOOSE':
      return {
        ...state,
        geese: state.geese.map(goose =>
          goose.id === action.payload.id ? { ...goose, ...action.payload } : goose
        ),
      };
    case 'ADD_INTERACTION':
      return { ...state, interactions: [...state.interactions, action.payload] };
    case 'CLEAR_ERROR':
      return { ...state, error: null };
    default:
      return state;
  }
};

const initialState = {
  geese: [],
  interactions: [],
  loading: false,
  error: null,
};

export const GooseProvider = ({ children }) => {
  const [state, dispatch] = useReducer(gooseReducer, initialState);

  const loadGeese = async () => {
    dispatch({ type: 'LOAD_GEESE_START' });
    try {
      const response = await GooseService.getGeese();
      dispatch({ type: 'LOAD_GEESE_SUCCESS', payload: response.data });
    } catch (error) {
      dispatch({ type: 'LOAD_GEESE_ERROR', payload: error.message });
    }
  };

  const interactWithGoose = async (gooseId, interactionType) => {
    try {
      const response = await GooseService.interactWithGoose(gooseId, interactionType);
      dispatch({ type: 'UPDATE_GOOSE', payload: response.data.goose });
      dispatch({ type: 'ADD_INTERACTION', payload: response.data.interaction });
      return response.data;
    } catch (error) {
      dispatch({ type: 'LOAD_GEESE_ERROR', payload: error.message });
      throw error;
    }
  };

  const feedGoose = async (gooseId, foodType) => {
    try {
      const response = await GooseService.feedGoose(gooseId, foodType);
      dispatch({ type: 'UPDATE_GOOSE', payload: response.data });
      return response.data;
    } catch (error) {
      dispatch({ type: 'LOAD_GEESE_ERROR', payload: error.message });
      throw error;
    }
  };

  const clearError = () => {
    dispatch({ type: 'CLEAR_ERROR' });
  };

  return (
    <GooseContext.Provider value={{ 
      ...state, 
      loadGeese, 
      interactWithGoose, 
      feedGoose, 
      clearError,
      dispatch 
    }}>
      {children}
    </GooseContext.Provider>
  );
};

export const useGoose = () => {
  const context = useContext(GooseContext);
  if (!context) {
    throw new Error('useGoose must be used within GooseProvider');
  }
  return context;
};
```

### 2.3 网络请求

#### 使用Fetch API进行网络请求（官方推荐）
```javascript
// src/services/ApiService.js
const BASE_URL = 'https://api.goose-app.com/api/v1';

class ApiService {
  static async request(endpoint, options = {}) {
    const url = `${BASE_URL}${endpoint}`;
    const config = {
      headers: {
        'Content-Type': 'application/json',
        ...options.headers,
      },
      ...options,
    };

    try {
      const response = await fetch(url, config);
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      
      return await response.json();
    } catch (error) {
      console.error('API请求错误:', error);
      throw error;
    }
  }

  static async get(endpoint, params = {}) {
    const queryString = new URLSearchParams(params).toString();
    const url = queryString ? `${endpoint}?${queryString}` : endpoint;
    return this.request(url);
  }

  static async post(endpoint, data) {
    return this.request(endpoint, {
      method: 'POST',
      body: JSON.stringify(data),
    });
  }

  static async put(endpoint, data) {
    return this.request(endpoint, {
      method: 'PUT',
      body: JSON.stringify(data),
    });
  }

  static async delete(endpoint) {
    return this.request(endpoint, {
      method: 'DELETE',
    });
  }
}

// 具体服务类示例
export class GooseService {
  static async getGeese() {
    return ApiService.get('/geese');
  }

  static async getGooseDetail(id) {
    return ApiService.get(`/geese/${id}`);
  }

  static async interactWithGoose(id, type) {
    return ApiService.post(`/geese/${id}/interact`, { type });
  }

  static async feedGoose(id, foodType) {
    return ApiService.post(`/geese/${id}/feed`, { foodType });
  }
}

export default ApiService;
```

### 2.4 导航和路由

#### 使用React Navigation v7进行导航（基于官方文档验证）
```javascript
// src/navigation/AppNavigator.js
import React from 'react';
import { NavigationContainer } from '@react-navigation/native';
import { createNativeStackNavigator } from '@react-navigation/native-stack';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import { useUser } from '../context/UserContext';

// 屏幕导入
import LoginScreen from '../screens/LoginScreen';
import HomeScreen from '../screens/HomeScreen';
import GooseDetailScreen from '../screens/GooseDetailScreen';
import ShopScreen from '../screens/ShopScreen';
import ProfileScreen from '../screens/ProfileScreen';

const Stack = createNativeStackNavigator();
const Tab = createBottomTabNavigator();

// 主要标签导航器
const MainTabNavigator = () => {
  return (
    <Tab.Navigator
      screenOptions={{
        tabBarActiveTintColor: '#6200EE',
        tabBarInactiveTintColor: 'gray',
      }}
    >
      <Tab.Screen 
        name="Home" 
        component={HomeScreen} 
        options={{
          title: '我的小鹅',
          tabBarIcon: ({ color }) => <Icon name="home" size={24} color={color} />,
        }}
      />
      <Tab.Screen 
        name="Shop" 
        component={ShopScreen} 
        options={{
          title: '商城',
          tabBarIcon: ({ color }) => <Icon name="shopping-cart" size={24} color={color} />,
        }}
      />
      <Tab.Screen 
        name="Profile" 
        component={ProfileScreen} 
        options={{
          title: '我的',
          tabBarIcon: ({ color }) => <Icon name="account" size={24} color={color} />,
        }}
      />
    </Tab.Navigator>
  );
};

// 认证栈导航器
const AuthStack = () => {
  return (
    <Stack.Navigator screenOptions={{ headerShown: false }}>
      <Stack.Screen name="Login" component={LoginScreen} />
    </Stack.Navigator>
  );
};

// 主栈导航器
const MainStack = () => {
  return (
    <Stack.Navigator>
      <Stack.Screen 
        name="MainTabs" 
        component={MainTabNavigator} 
        options={{ headerShown: false }}
      />
      <Stack.Screen 
        name="GooseDetail" 
        component={GooseDetailScreen}
        options={{ title: '小鹅详情' }}
      />
    </Stack.Navigator>
  );
};

// 根导航器
const AppNavigator = () => {
  const { isAuthenticated, loading } = useUser();

  if (loading) {
    return <LoadingScreen />; // 显示加载屏幕
  }

  return (
    <NavigationContainer>
      {isAuthenticated ? <MainStack /> : <AuthStack />}
    </NavigationContainer>
  );
};

export default AppNavigator;
```

### 2.5 UI组件库

#### 使用React Native Paper v5进行UI设计（基于官方文档验证）
```javascript
// src/components/GooseCard.js
import React from 'react';
import { View, Image } from 'react-native';
import { Card, Title, Paragraph, Button, ProgressBar, Chip } from 'react-native-paper';
import { useNavigation } from '@react-navigation/native';

const GooseCard = ({ goose }) => {
  const navigation = useNavigation();

  const getHealthColor = (health) => {
    if (health >= 80) return '#4CAF50'; // 绿色
    if (health >= 50) return '#FF9800'; // 橙色
    return '#F44336'; // 红色
  };

  return (
    <Card style={{ margin: 16, elevation: 4 }}>
      <Card.Cover 
        source={{ uri: goose.imageUrl }} 
        style={{ height: 200 }}
      />
      <Card.Content style={{ padding: 16 }}>
        <Title>{goose.name}</Title>
        <Paragraph>品种: {goose.breed}</Paragraph>
        <Paragraph>性别: {goose.gender === 1 ? '公鹅' : '母鹅'}</Paragraph>
        <Paragraph>年龄: {goose.age} 周</Paragraph>
        
        <View style={{ marginTop: 12 }}>
          <View style={{ flexDirection: 'row', alignItems: 'center', marginBottom: 8 }}>
            <Paragraph style={{ flex: 1 }}>健康度</Paragraph>
            <Paragraph>{goose.health}%</Paragraph>
          </View>
          <ProgressBar 
            progress={goose.health / 100} 
            color={getHealthColor(goose.health)}
            style={{ height: 8, borderRadius: 4 }}
          />
        </View>

        <View style={{ marginTop: 12 }}>
          <View style={{ flexDirection: 'row', alignItems: 'center', marginBottom: 8 }}>
            <Paragraph style={{ flex: 1 }}>饥饿度</Paragraph>
            <Paragraph>{goose.hunger}%</Paragraph>
          </View>
          <ProgressBar 
            progress={goose.hunger / 100} 
            color="#FF5722"
            style={{ height: 8, borderRadius: 4 }}
          />
        </View>

        <View style={{ flexDirection: 'row', marginTop: 16, justifyContent: 'space-between' }}>
          <Chip icon="star">等级 {goose.level}</Chip>
          <Chip icon="trophy">经验 {goose.experience}</Chip>
        </View>
      </Card.Content>
      <Card.Actions>
        <Button 
          mode="outlined"
          onPress={() => navigation.navigate('GooseDetail', { gooseId: goose.id })}
        >
          查看详情
        </Button>
        <Button 
          mode="contained"
          onPress={() => console.log('开始互动')}
        >
          开始互动
        </Button>
      </Card.Actions>
    </Card>
  );
};

export default GooseCard;
```

#### Paper主题配置
```javascript
// src/theme/paperTheme.js
import { MD3LightTheme, configureFonts } from 'react-native-paper';

const fontConfig = {
  default: {
    regular: {
      fontFamily: 'System',
      fontWeight: 'normal',
    },
    medium: {
      fontFamily: 'System',
      fontWeight: '500',
    },
    bold: {
      fontFamily: 'System',
      fontWeight: 'bold',
    },
  },
};

export const theme = {
  ...MD3LightTheme,
  fonts: configureFonts({ config: fontConfig }),
  colors: {
    ...MD3LightTheme.colors,
    primary: '#6200EE',
    secondary: '#03DAC6',
    accent: '#FF6F00',
  },
};
```

### 2.6 本地存储

#### 使用AsyncStorage进行本地存储（官方推荐）
```javascript
// src/services/StorageService.js
import AsyncStorage from '@react-native-async-storage/async-storage';

class StorageService {
  // 保存用户信息
  static async saveUser(user) {
    try {
      await AsyncStorage.setItem('user', JSON.stringify(user));
    } catch (error) {
      console.error('保存用户信息失败:', error);
    }
  }

  // 获取用户信息
  static async getUser() {
    try {
      const userJson = await AsyncStorage.getItem('user');
      return userJson ? JSON.parse(userJson) : null;
    } catch (error) {
      console.error('获取用户信息失败:', error);
      return null;
    }
  }

  // 缓存小鹅数据
  static async cacheGeese(geese) {
    try {
      const cacheData = {
        data: geese,
        timestamp: Date.now(),
      };
      await AsyncStorage.setItem('cached_geese', JSON.stringify(cacheData));
    } catch (error) {
      console.error('缓存小鹅数据失败:', error);
    }
  }

  // 获取缓存的小鹅数据
  static async getCachedGeese(maxAge = 5 * 60 * 1000) { // 默认5分钟过期
    try {
      const cachedJson = await AsyncStorage.getItem('cached_geese');
      if (!cachedJson) return null;

      const cached = JSON.parse(cachedJson);
      const age = Date.now() - cached.timestamp;
      
      if (age > maxAge) {
        await AsyncStorage.removeItem('cached_geese');
        return null;
      }

      return cached.data;
    } catch (error) {
      console.error('获取缓存小鹅数据失败:', error);
      return null;
    }
  }

  // 通用缓存方法
  static async setCache(key, value, ttl = null) {
    try {
      const cacheData = {
        value,
        timestamp: Date.now(),
        ttl,
      };
      await AsyncStorage.setItem(`cache_${key}`, JSON.stringify(cacheData));
    } catch (error) {
      console.error(`缓存数据失败 (${key}):`, error);
    }
  }

  static async getCache(key) {
    try {
      const cachedJson = await AsyncStorage.getItem(`cache_${key}`);
      if (!cachedJson) return null;

      const cached = JSON.parse(cachedJson);
      
      // 检查是否过期
      if (cached.ttl && (Date.now() - cached.timestamp) > cached.ttl) {
        await AsyncStorage.removeItem(`cache_${key}`);
        return null;
      }

      return cached.value;
    } catch (error) {
      console.error(`获取缓存数据失败 (${key}):`, error);
      return null;
    }
  }

  // 清除所有缓存
  static async clearCache() {
    try {
      const keys = await AsyncStorage.getAllKeys();
      const cacheKeys = keys.filter(key => key.startsWith('cache_'));
      await AsyncStorage.multiRemove(cacheKeys);
    } catch (error) {
      console.error('清除缓存失败:', error);
    }
  }
}

export default StorageService;
```

### 2.7 网络状态监听

#### 使用NetInfo进行网络状态监听（官方推荐）
```javascript
// src/hooks/useNetworkStatus.js
import { useState, useEffect } from 'react';
import NetInfo from '@react-native-community/netinfo';

export const useNetworkStatus = () => {
  const [isConnected, setIsConnected] = useState(true);
  const [networkType, setNetworkType] = useState('unknown');

  useEffect(() => {
    const unsubscribe = NetInfo.addEventListener(state => {
      setIsConnected(state.isConnected);
      setNetworkType(state.type);
      
      // 网络状态变化时的处理逻辑
      if (state.isConnected) {
        console.log('网络已连接，类型:', state.type);
        // 可以在这里触发数据同步
      } else {
        console.log('网络已断开');
      }
    });

    // 获取初始网络状态
    NetInfo.fetch().then(state => {
      setIsConnected(state.isConnected);
      setNetworkType(state.type);
    });

    return () => unsubscribe();
  }, []);

  return { isConnected, networkType };
};
```

### 2.8 应用入口配置

#### App.js根组件配置
```javascript
// App.js
import React from 'react';
import { StatusBar } from 'react-native';
import { PaperProvider } from 'react-native-paper';
import { UserProvider } from './src/context/UserContext';
import { GooseProvider } from './src/context/GooseContext';
import AppNavigator from './src/navigation/AppNavigator';
import { theme } from './src/theme/paperTheme';

const App = () => {
  return (
    <PaperProvider theme={theme}>
      <UserProvider>
        <GooseProvider>
          <StatusBar barStyle="dark-content" backgroundColor="#6200EE" />
          <AppNavigator />
        </GooseProvider>
      </UserProvider>
    </PaperProvider>
  );
};

export default App;
```

---

## 3. 数据库设计方案

### 3.1 ER图设计

```
┌─────────────┐    ┌─────────────┐    ┌─────────────┐
│    User     │    │    Goose    │    │  Product    │
│             │    │             │    │             │
│ + id        │◄──►│ + id        │    │ + id        │
│ + username  │    │ + name      │    │ + name      │
│ + email     │    │ + user_id   │    │ + price     │
│ + password  │    │ + health    │    │ + category  │
│ + avatar    │    │ + hunger    │    │ + status    │
│ + created_at│    │ + cleanliness│   │ + created_at│
└─────────────┘    │ + age       │    └─────────────┘
                   │ + gender    │
                   │ + created_at│
                   └─────────────┘
                          │
                          ▼
                   ┌─────────────┐
                   │ Interaction │
                   │             │
                   │ + id        │
                   │ + goose_id  │
                   │ + type      │
                   │ + value     │
                   │ + created_at│
                   └─────────────┘

┌─────────────┐    ┌─────────────┐    ┌─────────────┐
│    Order    │    │ Order_Item  │    │    Gift     │
│             │    │             │    │             │
│ + id        │◄──►│ + id        │    │ + id        │
│ + user_id   │    │ + order_id  │    │ + sender_id │
│ + total     │    │ + product_id│    │ + receiver_id│
│ + status    │    │ + quantity  │    │ + goose_id  │
│ + created_at│    │ + price     │    │ + message   │
└─────────────┘    └─────────────┘    │ + status    │
                                      │ + created_at│
                                      └─────────────┘
```

### 3.2 详细表结构定义

#### 用户表（users）
```sql
CREATE TABLE users (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '用户ID',
    username VARCHAR(50) NOT NULL UNIQUE COMMENT '用户名',
    email VARCHAR(100) NOT NULL UNIQUE COMMENT '邮箱',
    password VARCHAR(255) NOT NULL COMMENT '密码（加密）',
    nickname VARCHAR(50) COMMENT '昵称',
    avatar VARCHAR(255) COMMENT '头像URL',
    phone VARCHAR(20) COMMENT '手机号',
    gender TINYINT DEFAULT 0 COMMENT '性别：0-未知，1-男，2-女',
    birthday DATE COMMENT '生日',
    total_geese INT DEFAULT 0 COMMENT '拥有小鹅总数',
    total_days INT DEFAULT 0 COMMENT '养成总天数',
    total_feeds INT DEFAULT 0 COMMENT '喂食总次数',
    status TINYINT DEFAULT 1 COMMENT '状态：0-禁用，1-正常',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',

    INDEX idx_username (username),
    INDEX idx_email (email),
    INDEX idx_phone (phone),
    INDEX idx_status (status),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户表';
```

#### 小鹅表（geese）
```sql
CREATE TABLE geese (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '小鹅ID',
    user_id BIGINT NOT NULL COMMENT '所属用户ID',
    name VARCHAR(50) NOT NULL COMMENT '小鹅名称',
    breed VARCHAR(30) NOT NULL COMMENT '品种',
    gender TINYINT NOT NULL COMMENT '性别：1-雄性，2-雌性',
    age INT DEFAULT 0 COMMENT '年龄（周）',
    health INT DEFAULT 100 COMMENT '健康度（0-100）',
    hunger INT DEFAULT 50 COMMENT '饥饿度（0-100）',
    thirst INT DEFAULT 50 COMMENT '口渴度（0-100）',
    cleanliness INT DEFAULT 100 COMMENT '清洁度（0-100）',
    happiness INT DEFAULT 80 COMMENT '快乐度（0-100）',
    level INT DEFAULT 1 COMMENT '等级',
    experience INT DEFAULT 0 COMMENT '经验值',
    image_url VARCHAR(255) COMMENT '小鹅图片URL',
    video_url VARCHAR(255) COMMENT '直播视频URL',
    last_feed_time TIMESTAMP NULL COMMENT '最后喂食时间',
    last_water_time TIMESTAMP NULL COMMENT '最后喂水时间',
    last_clean_time TIMESTAMP NULL COMMENT '最后清洁时间',
    status TINYINT DEFAULT 1 COMMENT '状态：0-已转赠，1-正常，2-生病',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',

    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_breed (breed),
    INDEX idx_status (status),
    INDEX idx_level (level),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='小鹅表';
```

#### 商品表（products）
```sql
CREATE TABLE products (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '商品ID',
    name VARCHAR(100) NOT NULL COMMENT '商品名称',
    description TEXT COMMENT '商品描述',
    breed VARCHAR(30) NOT NULL COMMENT '小鹅品种',
    price DECIMAL(10,2) NOT NULL COMMENT '价格',
    original_price DECIMAL(10,2) COMMENT '原价',
    category VARCHAR(30) NOT NULL COMMENT '分类',
    age_range VARCHAR(20) COMMENT '年龄范围',
    health_range VARCHAR(20) COMMENT '健康度范围',
    image_url VARCHAR(255) COMMENT '商品图片URL',
    images JSON COMMENT '商品图片列表',
    tags JSON COMMENT '标签列表',
    stock INT DEFAULT 0 COMMENT '库存数量',
    sales_count INT DEFAULT 0 COMMENT '销售数量',
    rating DECIMAL(3,2) DEFAULT 5.00 COMMENT '评分（1-5）',
    is_featured BOOLEAN DEFAULT FALSE COMMENT '是否推荐',
    is_limited BOOLEAN DEFAULT FALSE COMMENT '是否限量',
    status TINYINT DEFAULT 1 COMMENT '状态：0-下架，1-上架',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',

    INDEX idx_category (category),
    INDEX idx_breed (breed),
    INDEX idx_price (price),
    INDEX idx_status (status),
    INDEX idx_is_featured (is_featured),
    INDEX idx_sales_count (sales_count),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='商品表';
```

#### 订单表（orders）
```sql
CREATE TABLE orders (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '订单ID',
    order_no VARCHAR(32) NOT NULL UNIQUE COMMENT '订单号',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    total_amount DECIMAL(10,2) NOT NULL COMMENT '订单总金额',
    discount_amount DECIMAL(10,2) DEFAULT 0.00 COMMENT '优惠金额',
    final_amount DECIMAL(10,2) NOT NULL COMMENT '实付金额',
    payment_method VARCHAR(20) COMMENT '支付方式',
    payment_status TINYINT DEFAULT 0 COMMENT '支付状态：0-待支付，1-已支付，2-已退款',
    order_status TINYINT DEFAULT 0 COMMENT '订单状态：0-待支付，1-已支付，2-已完成，3-已取消',
    remark TEXT COMMENT '订单备注',
    paid_at TIMESTAMP NULL COMMENT '支付时间',
    completed_at TIMESTAMP NULL COMMENT '完成时间',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',

    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_order_no (order_no),
    INDEX idx_user_id (user_id),
    INDEX idx_payment_status (payment_status),
    INDEX idx_order_status (order_status),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='订单表';
```

#### 订单项表（order_items）
```sql
CREATE TABLE order_items (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '订单项ID',
    order_id BIGINT NOT NULL COMMENT '订单ID',
    product_id BIGINT NOT NULL COMMENT '商品ID',
    product_name VARCHAR(100) NOT NULL COMMENT '商品名称',
    product_image VARCHAR(255) COMMENT '商品图片',
    price DECIMAL(10,2) NOT NULL COMMENT '商品单价',
    quantity INT NOT NULL DEFAULT 1 COMMENT '购买数量',
    total_price DECIMAL(10,2) NOT NULL COMMENT '小计金额',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',

    FOREIGN KEY (order_id) REFERENCES orders(id) ON DELETE CASCADE,
    FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE CASCADE,
    INDEX idx_order_id (order_id),
    INDEX idx_product_id (product_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='订单项表';
```

#### 互动记录表（interactions）
```sql
CREATE TABLE interactions (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '互动记录ID',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    goose_id BIGINT NOT NULL COMMENT '小鹅ID',
    interaction_type VARCHAR(20) NOT NULL COMMENT '互动类型：feed-喂食，water-喂水，clean-清洁，play-玩耍',
    value_change INT DEFAULT 0 COMMENT '数值变化',
    experience_gained INT DEFAULT 0 COMMENT '获得经验',
    cooldown_minutes INT DEFAULT 0 COMMENT '冷却时间（分钟）',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',

    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (goose_id) REFERENCES geese(id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_goose_id (goose_id),
    INDEX idx_interaction_type (interaction_type),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='互动记录表';
```

#### 转赠记录表（gifts）
```sql
CREATE TABLE gifts (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '转赠记录ID',
    gift_code VARCHAR(32) NOT NULL UNIQUE COMMENT '转赠码',
    sender_id BIGINT NOT NULL COMMENT '赠送者ID',
    receiver_id BIGINT NULL COMMENT '接收者ID',
    goose_id BIGINT NOT NULL COMMENT '小鹅ID',
    message TEXT COMMENT '祝福语',
    share_method VARCHAR(20) NOT NULL COMMENT '分享方式：wechat-微信，link-链接',
    status TINYINT DEFAULT 0 COMMENT '状态：0-待领取，1-已领取，2-已过期',
    expires_at TIMESTAMP NOT NULL COMMENT '过期时间',
    received_at TIMESTAMP NULL COMMENT '领取时间',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',

    FOREIGN KEY (sender_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (receiver_id) REFERENCES users(id) ON DELETE SET NULL,
    FOREIGN KEY (goose_id) REFERENCES geese(id) ON DELETE CASCADE,
    INDEX idx_gift_code (gift_code),
    INDEX idx_sender_id (sender_id),
    INDEX idx_receiver_id (receiver_id),
    INDEX idx_status (status),
    INDEX idx_expires_at (expires_at),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='转赠记录表';
```

### 3.3 数据库初始化脚本

#### 创建数据库
```sql
-- 创建数据库
CREATE DATABASE goose_app
DEFAULT CHARACTER SET utf8mb4
DEFAULT COLLATE utf8mb4_unicode_ci;

-- 使用数据库
USE goose_app;
```

#### 插入初始数据
```sql
-- 插入商品数据
INSERT INTO products (name, description, breed, price, original_price, category, age_range, health_range, image_url, tags, stock, is_featured, is_limited) VALUES
('小白鹅·豆豆', '温顺可爱的小白鹅，适合新手养成', '白鹅', 199.00, 299.00, '新手推荐', '1-2周', '85-95%', '/images/white_goose_1.jpg', '["新手友好", "温顺", "可爱"]', 50, TRUE, FALSE),
('小灰鹅·灰灰', '聪明活泼的小灰鹅，互动性强', '灰鹅', 299.00, 399.00, '热门推荐', '2-3周', '88-98%', '/images/gray_goose_1.jpg', '["聪明", "活泼", "互动"]', 30, TRUE, FALSE),
('小花鹅·花花', '稀有品种小花鹅，颜值超高', '花鹅', 399.00, 499.00, '限量版', '3-4周', '90-100%', '/images/flower_goose_1.jpg', '["稀有", "高颜值", "限量"]', 10, TRUE, TRUE),
('小黑鹅·小黑', '神秘优雅的小黑鹅，成长潜力大', '黑鹅', 159.00, 199.00, '潜力股', '1周', '80-90%', '/images/black_goose_1.jpg', '["神秘", "优雅", "潜力"]', 40, FALSE, FALSE);

-- 插入测试用户
INSERT INTO users (username, email, password, nickname, avatar, total_geese, total_days, total_feeds) VALUES
('testuser', '<EMAIL>', '$2a$10$encrypted_password_hash', '小明', '👤', 3, 15, 42);
```

---

## 4. RESTful API接口设计规范

### 4.1 统一响应格式

#### 成功响应格式
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    // 具体数据内容
  },
  "timestamp": "2024-12-19T10:30:00Z"
}
```

#### 错误响应格式
```json
{
  "code": 400,
  "message": "参数错误",
  "error": "详细错误信息",
  "timestamp": "2024-12-19T10:30:00Z"
}
```

#### 分页响应格式
```json
{
  "code": 200,
  "message": "查询成功",
  "data": {
    "list": [
      // 数据列表
    ],
    "pagination": {
      "page": 1,
      "size": 10,
      "total": 100,
      "pages": 10
    }
  },
  "timestamp": "2024-12-19T10:30:00Z"
}
```

### 4.2 错误码定义

```java
public enum ErrorCode {
    SUCCESS(200, "操作成功"),

    // 客户端错误 4xx
    BAD_REQUEST(400, "请求参数错误"),
    UNAUTHORIZED(401, "未授权访问"),
    FORBIDDEN(403, "禁止访问"),
    NOT_FOUND(404, "资源不存在"),
    METHOD_NOT_ALLOWED(405, "请求方法不允许"),
    CONFLICT(409, "资源冲突"),

    // 业务错误 5xx
    INTERNAL_ERROR(500, "服务器内部错误"),
    SERVICE_UNAVAILABLE(503, "服务不可用"),

    // 自定义业务错误 6xxx
    USER_NOT_FOUND(6001, "用户不存在"),
    USER_ALREADY_EXISTS(6002, "用户已存在"),
    PASSWORD_ERROR(6003, "密码错误"),
    TOKEN_EXPIRED(6004, "Token已过期"),
    TOKEN_INVALID(6005, "Token无效"),

    GOOSE_NOT_FOUND(6101, "小鹅不存在"),
    GOOSE_NOT_OWNED(6102, "小鹅不属于当前用户"),
    INTERACTION_COOLDOWN(6103, "互动冷却中"),
    GOOSE_STATUS_ERROR(6104, "小鹅状态异常"),

    PRODUCT_NOT_FOUND(6201, "商品不存在"),
    PRODUCT_OUT_OF_STOCK(6202, "商品库存不足"),
    ORDER_NOT_FOUND(6203, "订单不存在"),
    ORDER_STATUS_ERROR(6204, "订单状态错误"),

    GIFT_NOT_FOUND(6301, "转赠记录不存在"),
    GIFT_EXPIRED(6302, "转赠已过期"),
    GIFT_ALREADY_RECEIVED(6303, "转赠已被领取");
}
```

### 4.3 接口版本控制策略

#### URL版本控制
```
/api/v1/users          # 版本1
/api/v2/users          # 版本2
```

#### Header版本控制
```
Accept: application/vnd.gooseapp.v1+json
Accept: application/vnd.gooseapp.v2+json
```

### 4.4 核心API接口定义

#### 用户模块API

##### 用户注册
```
POST /api/v1/auth/register
Content-Type: application/json

Request Body:
{
  "username": "testuser",
  "email": "<EMAIL>",
  "password": "password123",
  "nickname": "小明"
}

Response:
{
  "code": 200,
  "message": "注册成功",
  "data": {
    "userId": 1,
    "username": "testuser",
    "nickname": "小明",
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
  }
}
```

##### 用户登录
```
POST /api/v1/auth/login
Content-Type: application/json

Request Body:
{
  "username": "testuser",
  "password": "password123"
}

Response:
{
  "code": 200,
  "message": "登录成功",
  "data": {
    "userId": 1,
    "username": "testuser",
    "nickname": "小明",
    "avatar": "👤",
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "expiresIn": 7200
  }
}
```

##### 获取用户信息
```
GET /api/v1/users/profile
Authorization: Bearer {token}

Response:
{
  "code": 200,
  "message": "查询成功",
  "data": {
    "id": 1,
    "username": "testuser",
    "nickname": "小明",
    "avatar": "👤",
    "email": "<EMAIL>",
    "totalGeese": 3,
    "totalDays": 15,
    "totalFeeds": 42,
    "createdAt": "2024-12-01T10:00:00Z"
  }
}
```

#### 小鹅模块API

##### 获取用户的小鹅列表
```
GET /api/v1/geese
Authorization: Bearer {token}
Query Parameters:
  - page: 页码 (默认1)
  - size: 每页数量 (默认10)
  - status: 状态筛选 (可选)

Response:
{
  "code": 200,
  "message": "查询成功",
  "data": {
    "list": [
      {
        "id": 1,
        "name": "小白鹅·豆豆",
        "breed": "白鹅",
        "gender": 2,
        "age": 3,
        "health": 85,
        "hunger": 60,
        "thirst": 45,
        "cleanliness": 75,
        "happiness": 80,
        "level": 2,
        "experience": 150,
        "imageUrl": "/images/white_goose_1.jpg",
        "videoUrl": "/videos/live_stream_1.m3u8",
        "status": 1,
        "createdAt": "2024-12-01T10:00:00Z"
      }
    ],
    "pagination": {
      "page": 1,
      "size": 10,
      "total": 3,
      "pages": 1
    }
  }
}
```

##### 获取小鹅详情
```
GET /api/v1/geese/{gooseId}
Authorization: Bearer {token}

Response:
{
  "code": 200,
  "message": "查询成功",
  "data": {
    "id": 1,
    "name": "小白鹅·豆豆",
    "breed": "白鹅",
    "gender": 2,
    "age": 3,
    "health": 85,
    "hunger": 60,
    "thirst": 45,
    "cleanliness": 75,
    "happiness": 80,
    "level": 2,
    "experience": 150,
    "imageUrl": "/images/white_goose_1.jpg",
    "videoUrl": "/videos/live_stream_1.m3u8",
    "lastFeedTime": "2024-12-19T08:30:00Z",
    "lastWaterTime": "2024-12-19T09:15:00Z",
    "lastCleanTime": "2024-12-19T07:45:00Z",
    "cooldowns": {
      "feed": 0,
      "water": 45,
      "clean": 120
    },
    "status": 1,
    "createdAt": "2024-12-01T10:00:00Z"
  }
}
```

##### 小鹅互动操作
```
POST /api/v1/geese/{gooseId}/interact
Authorization: Bearer {token}
Content-Type: application/json

Request Body:
{
  "type": "feed",  // feed-喂食, water-喂水, clean-清洁, play-玩耍
  "value": 10      // 可选，特定操作的数值
}

Response:
{
  "code": 200,
  "message": "互动成功",
  "data": {
    "interactionId": 123,
    "type": "feed",
    "valueChange": 15,
    "experienceGained": 5,
    "cooldownMinutes": 60,
    "gooseStatus": {
      "health": 85,
      "hunger": 75,
      "thirst": 45,
      "cleanliness": 75,
      "happiness": 85,
      "experience": 155,
      "level": 2
    }
  }
}
```

#### 商城模块API

##### 获取商品列表
```
GET /api/v1/shop/products
Query Parameters:
  - page: 页码 (默认1)
  - size: 每页数量 (默认10)
  - category: 分类筛选 (可选)
  - breed: 品种筛选 (可选)
  - sortBy: 排序字段 (price, sales, rating)
  - sortOrder: 排序方向 (asc, desc)
  - minPrice: 最低价格 (可选)
  - maxPrice: 最高价格 (可选)

Response:
{
  "code": 200,
  "message": "查询成功",
  "data": {
    "list": [
      {
        "id": 1,
        "name": "小白鹅·豆豆",
        "description": "温顺可爱的小白鹅，适合新手养成",
        "breed": "白鹅",
        "price": 199.00,
        "originalPrice": 299.00,
        "category": "新手推荐",
        "ageRange": "1-2周",
        "healthRange": "85-95%",
        "imageUrl": "/images/white_goose_1.jpg",
        "images": ["/images/white_goose_1.jpg", "/images/white_goose_2.jpg"],
        "tags": ["新手友好", "温顺", "可爱"],
        "stock": 50,
        "salesCount": 128,
        "rating": 4.8,
        "isFeatured": true,
        "isLimited": false,
        "status": 1
      }
    ],
    "pagination": {
      "page": 1,
      "size": 10,
      "total": 20,
      "pages": 2
    }
  }
}
```

##### 创建订单
```
POST /api/v1/orders
Authorization: Bearer {token}
Content-Type: application/json

Request Body:
{
  "items": [
    {
      "productId": 1,
      "quantity": 1
    },
    {
      "productId": 2,
      "quantity": 2
    }
  ],
  "remark": "请尽快发货"
}

Response:
{
  "code": 200,
  "message": "订单创建成功",
  "data": {
    "orderId": 1001,
    "orderNo": "GO20241219001",
    "totalAmount": 697.00,
    "discountAmount": 0.00,
    "finalAmount": 697.00,
    "paymentStatus": 0,
    "orderStatus": 0,
    "items": [
      {
        "productId": 1,
        "productName": "小白鹅·豆豆",
        "price": 199.00,
        "quantity": 1,
        "totalPrice": 199.00
      },
      {
        "productId": 2,
        "productName": "小灰鹅·灰灰",
        "price": 299.00,
        "quantity": 2,
        "totalPrice": 498.00
      }
    ],
    "createdAt": "2024-12-19T10:30:00Z"
  }
}
```

#### 转赠模块API

##### 创建转赠
```
POST /api/v1/gifts
Authorization: Bearer {token}
Content-Type: application/json

Request Body:
{
  "gooseId": 1,
  "message": "希望这只小鹅能给你带来快乐！",
  "shareMethod": "wechat",
  "expiresHours": 72
}

Response:
{
  "code": 200,
  "message": "转赠创建成功",
  "data": {
    "giftId": 1,
    "giftCode": "GIFT20241219001",
    "goose": {
      "id": 1,
      "name": "小白鹅·豆豆",
      "breed": "白鹅",
      "health": 85,
      "age": 3,
      "imageUrl": "/images/white_goose_1.jpg"
    },
    "message": "希望这只小鹅能给你带来快乐！",
    "shareMethod": "wechat",
    "shareUrl": "https://goose-app.com/gift/GIFT20241219001",
    "expiresAt": "2024-12-22T10:30:00Z",
    "createdAt": "2024-12-19T10:30:00Z"
  }
}
```

##### 领取转赠
```
POST /api/v1/gifts/{giftCode}/receive
Authorization: Bearer {token}

Response:
{
  "code": 200,
  "message": "转赠领取成功",
  "data": {
    "giftId": 1,
    "goose": {
      "id": 1,
      "name": "小白鹅·豆豆",
      "breed": "白鹅",
      "health": 85,
      "age": 3,
      "imageUrl": "/images/white_goose_1.jpg"
    },
    "message": "希望这只小鹅能给你带来快乐！",
    "sender": {
      "nickname": "小明",
      "avatar": "👤"
    },
    "receivedAt": "2024-12-19T11:00:00Z"
  }
}
```

## 5. 前后端交互方案

### 5.1 React Native与Spring Boot通信架构

#### HTTP客户端配置
```typescript
// src/services/httpClient.ts
import axios from 'axios';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { Platform } from 'react-native';

const getBaseURL = () => {
  if (__DEV__) {
    return Platform.OS === 'android' 
      ? 'http://********:8080/api/v1'  // Android模拟器
      : 'http://localhost:8080/api/v1'; // iOS模拟器
  }
  return 'https://api.goose-app.com/api/v1';
};

class HttpClient {
  private static instance: HttpClient;
  private client: any;

  private constructor() {
    this.client = axios.create({
      baseURL: getBaseURL(),
      timeout: 10000,
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      },
    });

    this.setupInterceptors();
  }

  public static getInstance(): HttpClient {
    if (!HttpClient.instance) {
      HttpClient.instance = new HttpClient();
    }
    return HttpClient.instance;
  }

  private setupInterceptors() {
    // 请求拦截器
    this.client.interceptors.request.use(
      async (config: any) => {
        const token = await AsyncStorage.getItem('sa-token');
        if (token) {
          config.headers.satoken = token;
        }
        return config;
      },
      (error: any) => Promise.reject(error)
    );

    // 响应拦截器
    this.client.interceptors.response.use(
      async (response: any) => {
        const newToken = response.headers['satoken'];
        if (newToken) {
          await AsyncStorage.setItem('sa-token', newToken);
        }
        return response;
      },
      async (error: any) => {
        if (error.response?.status === 401) {
          await AsyncStorage.removeItem('sa-token');
          // 导航到登录页面
          // NavigationService.navigate('Login');
        }
        return Promise.reject(error);
      }
    );
  }

  public get axios() {
    return this.client;
  }
}

export default HttpClient.getInstance();
```

### 5.2 数据传输格式（JSON Schema定义）

#### 用户数据模型
```dart
// lib/models/user.dart
import 'package:json_annotation/json_annotation.dart';

part 'user.g.dart';

@JsonSerializable()
class User {
  final int id;
  final String username;
  final String nickname;
  final String? avatar;
  final String email;
  @JsonKey(name: 'total_geese')
  final int totalGeese;
  @JsonKey(name: 'total_days')
  final int totalDays;
  @JsonKey(name: 'total_feeds')
  final int totalFeeds;
  @JsonKey(name: 'created_at')
  final DateTime createdAt;

  User({
    required this.id,
    required this.username,
    required this.nickname,
    this.avatar,
    required this.email,
    required this.totalGeese,
    required this.totalDays,
    required this.totalFeeds,
    required this.createdAt,
  });

  factory User.fromJson(Map<String, dynamic> json) => _$UserFromJson(json);
  Map<String, dynamic> toJson() => _$UserToJson(this);
}
```

#### 小鹅数据模型
```dart
// lib/models/goose.dart
@JsonSerializable()
class Goose {
  final int id;
  final String name;
  final String breed;
  final int gender;
  final int age;
  final int health;
  final int hunger;
  final int thirst;
  final int cleanliness;
  final int happiness;
  final int level;
  final int experience;
  @JsonKey(name: 'image_url')
  final String? imageUrl;
  @JsonKey(name: 'video_url')
  final String? videoUrl;
  @JsonKey(name: 'last_feed_time')
  final DateTime? lastFeedTime;
  @JsonKey(name: 'last_water_time')
  final DateTime? lastWaterTime;
  @JsonKey(name: 'last_clean_time')
  final DateTime? lastCleanTime;
  final Map<String, int>? cooldowns;
  final int status;
  @JsonKey(name: 'created_at')
  final DateTime createdAt;

  Goose({
    required this.id,
    required this.name,
    required this.breed,
    required this.gender,
    required this.age,
    required this.health,
    required this.hunger,
    required this.thirst,
    required this.cleanliness,
    required this.happiness,
    required this.level,
    required this.experience,
    this.imageUrl,
    this.videoUrl,
    this.lastFeedTime,
    this.lastWaterTime,
    this.lastCleanTime,
    this.cooldowns,
    required this.status,
    required this.createdAt,
  });

  factory Goose.fromJson(Map<String, dynamic> json) => _$GooseFromJson(json);
  Map<String, dynamic> toJson() => _$GooseToJson(this);
}
```

### 5.3 异步处理和状态管理

#### 使用Context API + useReducer进行状态管理
```javascript
// src/context/UserContext.js
import React, { createContext, useContext, useReducer } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';

const UserContext = createContext();

const userReducer = (state, action) => {
  switch (action.type) {
    case 'LOGIN_START':
      return { ...state, loading: true, error: null };
    case 'LOGIN_SUCCESS':
      return { ...state, loading: false, user: action.payload, isAuthenticated: true };
    case 'LOGIN_ERROR':
      return { ...state, loading: false, error: action.payload };
    case 'LOGOUT':
      return { ...state, user: null, isAuthenticated: false };
    case 'UPDATE_PROFILE':
      return { ...state, user: { ...state.user, ...action.payload } };
    default:
      return state;
  }
};

const initialState = {
  user: null,
  isAuthenticated: false,
  loading: false,
  error: null,
};

export const UserProvider = ({ children }) => {
  const [state, dispatch] = useReducer(userReducer, initialState);

  const login = async (username, password) => {
    dispatch({ type: 'LOGIN_START' });
    try {
      const response = await fetch('/api/v1/auth/login', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ username, password }),
      });
      
      if (!response.ok) throw new Error('登录失败');
      
      const userData = await response.json();
      // 保存用户信息到本地存储
      await AsyncStorage.setItem('user', JSON.stringify(userData.data));
      dispatch({ type: 'LOGIN_SUCCESS', payload: userData.data });
    } catch (error) {
      dispatch({ type: 'LOGIN_ERROR', payload: error.message });
    }
  };

  const logout = async () => {
    await AsyncStorage.removeItem('user');
    await AsyncStorage.removeItem('token');
    dispatch({ type: 'LOGOUT' });
  };

  const loadProfile = async () => {
    try {
      const response = await fetch('/api/v1/user/profile');
      if (!response.ok) throw new Error('获取用户信息失败');
      
      const userData = await response.json();
      dispatch({ type: 'UPDATE_PROFILE', payload: userData.data });
    } catch (error) {
      console.error('加载用户信息失败:', error);
    }
  };

  return (
    <UserContext.Provider value={{ ...state, login, logout, loadProfile, dispatch }}>
      {children}
    </UserContext.Provider>
  );
};

export const useUser = () => {
  const context = useContext(UserContext);
  if (!context) {
    throw new Error('useUser must be used within UserProvider');
  }
  return context;
};
```

#### 小鹅状态管理
```javascript
// src/context/GooseContext.js
import React, { createContext, useContext, useReducer } from 'react';
import { GooseService } from '../services/ApiService';

const GooseContext = createContext();

const gooseReducer = (state, action) => {
  switch (action.type) {
    case 'LOAD_GEESE_START':
      return { ...state, loading: true, error: null };
    case 'LOAD_GEESE_SUCCESS':
      return { ...state, loading: false, geese: action.payload };
    case 'LOAD_GEESE_ERROR':
      return { ...state, loading: false, error: action.payload };
    case 'UPDATE_GOOSE':
      return {
        ...state,
        geese: state.geese.map(goose =>
          goose.id === action.payload.id ? { ...goose, ...action.payload } : goose
        ),
      };
    case 'ADD_INTERACTION':
      return { ...state, interactions: [...state.interactions, action.payload] };
    case 'CLEAR_ERROR':
      return { ...state, error: null };
    default:
      return state;
  }
};

const initialState = {
  geese: [],
  interactions: [],
  loading: false,
  error: null,
};

export const GooseProvider = ({ children }) => {
  const [state, dispatch] = useReducer(gooseReducer, initialState);

  const loadGeese = async () => {
    dispatch({ type: 'LOAD_GEESE_START' });
    try {
      const response = await GooseService.getGeese();
      dispatch({ type: 'LOAD_GEESE_SUCCESS', payload: response.data });
    } catch (error) {
      dispatch({ type: 'LOAD_GEESE_ERROR', payload: error.message });
    }
  };

  const interactWithGoose = async (gooseId, interactionType) => {
    try {
      const response = await GooseService.interactWithGoose(gooseId, interactionType);
      dispatch({ type: 'UPDATE_GOOSE', payload: response.data.goose });
      dispatch({ type: 'ADD_INTERACTION', payload: response.data.interaction });
      return response.data;
    } catch (error) {
      dispatch({ type: 'LOAD_GEESE_ERROR', payload: error.message });
      throw error;
    }
  };

  const feedGoose = async (gooseId, foodType) => {
    try {
      const response = await GooseService.feedGoose(gooseId, foodType);
      dispatch({ type: 'UPDATE_GOOSE', payload: response.data });
      return response.data;
    } catch (error) {
      dispatch({ type: 'LOAD_GEESE_ERROR', payload: error.message });
      throw error;
    }
  };

  const clearError = () => {
    dispatch({ type: 'CLEAR_ERROR' });
  };

  return (
    <GooseContext.Provider value={{ 
      ...state, 
      loadGeese, 
      interactWithGoose, 
      feedGoose, 
      clearError,
      dispatch 
    }}>
      {children}
    </GooseContext.Provider>
  );
};

export const useGoose = () => {
  const context = useContext(GooseContext);
  if (!context) {
    throw new Error('useGoose must be used within GooseProvider');
  }
  return context;
};
```

### 5.4 离线数据缓存策略

#### 本地数据存储（使用AsyncStorage）
```javascript
// src/services/StorageService.js
import AsyncStorage from '@react-native-async-storage/async-storage';

class StorageService {
  // 用户数据缓存
  static async saveUser(user) {
    try {
      await AsyncStorage.setItem('user', JSON.stringify(user));
    } catch (error) {
      console.error('保存用户信息失败:', error);
    }
  }

  static async getUser() {
    try {
      const userJson = await AsyncStorage.getItem('user');
      return userJson ? JSON.parse(userJson) : null;
    } catch (error) {
      console.error('获取用户信息失败:', error);
      return null;
    }
  }

  static async removeUser() {
    try {
      await AsyncStorage.removeItem('user');
    } catch (error) {
      console.error('删除用户信息失败:', error);
    }
  }

  // 小鹅数据缓存
  static async saveGeese(geese) {
    try {
      const cacheData = {
        data: geese,
        timestamp: Date.now(),
      };
      await AsyncStorage.setItem('cached_geese', JSON.stringify(cacheData));
    } catch (error) {
      console.error('缓存小鹅数据失败:', error);
    }
  }

  static async getGeese(maxAge = 5 * 60 * 1000) { // 默认5分钟过期
    try {
      const cachedJson = await AsyncStorage.getItem('cached_geese');
      if (!cachedJson) return null;

      const cached = JSON.parse(cachedJson);
      const age = Date.now() - cached.timestamp;
      
      if (age > maxAge) {
        await AsyncStorage.removeItem('cached_geese');
        return null;
      }

      return cached.data;
    } catch (error) {
      console.error('获取缓存小鹅数据失败:', error);
      return null;
    }
  }

  // 通用缓存方法
  static async setCache(key, value, ttl = null) {
    try {
      const cacheData = {
        value,
        timestamp: Date.now(),
        ttl,
      };
      await AsyncStorage.setItem(`cache_${key}`, JSON.stringify(cacheData));
    } catch (error) {
      console.error(`缓存数据失败 (${key}):`, error);
    }
  }

  static async getCache(key) {
    try {
      const cachedJson = await AsyncStorage.getItem(`cache_${key}`);
      if (!cachedJson) return null;

      const cached = JSON.parse(cachedJson);
      
      // 检查是否过期
      if (cached.ttl && (Date.now() - cached.timestamp) > cached.ttl) {
        await AsyncStorage.removeItem(`cache_${key}`);
        return null;
      }

      return cached.value;
    } catch (error) {
      console.error(`获取缓存数据失败 (${key}):`, error);
      return null;
    }
  }

  // Token管理
  static async saveToken(token) {
    try {
      await AsyncStorage.setItem('auth_token', token);
    } catch (error) {
      console.error('保存Token失败:', error);
    }
  }

  static async getToken() {
    try {
      return await AsyncStorage.getItem('auth_token');
    } catch (error) {
      console.error('获取Token失败:', error);
      return null;
    }
  }

  static async removeToken() {
    try {
      await AsyncStorage.removeItem('auth_token');
    } catch (error) {
      console.error('删除Token失败:', error);
    }
  }

  // 清除所有缓存
  static async clearCache() {
    try {
      const keys = await AsyncStorage.getAllKeys();
      const cacheKeys = keys.filter(key => key.startsWith('cache_'));
      await AsyncStorage.multiRemove(cacheKeys);
    } catch (error) {
      console.error('清除缓存失败:', error);
    }
  }

  // 清除所有数据
  static async clearAll() {
    try {
      await AsyncStorage.clear();
    } catch (error) {
      console.error('清除所有数据失败:', error);
    }
  }
}

export default StorageService;
```

#### 网络状态监听和离线处理
```javascript
// src/hooks/useNetworkStatus.js
import { useState, useEffect } from 'react';
import NetInfo from '@react-native-community/netinfo';

export const useNetworkStatus = () => {
  const [isConnected, setIsConnected] = useState(true);
  const [networkType, setNetworkType] = useState('unknown');

  useEffect(() => {
    const unsubscribe = NetInfo.addEventListener(state => {
      setIsConnected(state.isConnected);
      setNetworkType(state.type);
      
      // 网络状态变化时的处理逻辑
      if (state.isConnected) {
        console.log('网络已连接，类型:', state.type);
        // 可以在这里触发数据同步
      } else {
        console.log('网络已断开');
      }
    });

    // 获取初始网络状态
    NetInfo.fetch().then(state => {
      setIsConnected(state.isConnected);
      setNetworkType(state.type);
    });

    return () => unsubscribe();
  }, []);

  return { isConnected, networkType };
};
```

#### 离线优先的数据管理策略
```javascript
// src/services/OfflineDataService.js
import StorageService from './StorageService';
import { useNetworkStatus } from '../hooks/useNetworkStatus';
import ApiService from './ApiService';

class OfflineDataService {
  static async getGeeseWithOfflineSupport() {
    const { isConnected } = useNetworkStatus();
    
    try {
      if (isConnected) {
        // 在线时从服务器获取最新数据
        const response = await ApiService.get('/geese');
        const geese = response.data;
        
        // 缓存到本地
        await StorageService.saveGeese(geese);
        return geese;
      } else {
        // 离线时从缓存获取数据
        const cachedGeese = await StorageService.getGeese();
        if (cachedGeese) {
          return cachedGeese;
        } else {
          throw new Error('无网络连接且没有缓存数据');
        }
      }
    } catch (error) {
      // 网络请求失败时尝试从缓存获取
      const cachedGeese = await StorageService.getGeese();
      if (cachedGeese) {
        console.warn('使用缓存数据:', error.message);
        return cachedGeese;
      }
      throw error;
    }
  }

  static async syncDataWhenOnline() {
    const { isConnected } = useNetworkStatus();
    
    if (!isConnected) {
      console.log('网络未连接，跳过数据同步');
      return;
    }

    try {
      // 同步小鹅数据
      const geese = await ApiService.get('/geese');
      await StorageService.saveGeese(geese.data);
      
      // 可以添加更多数据同步逻辑
      console.log('数据同步完成');
    } catch (error) {
      console.error('数据同步失败:', error);
    }
  }
}

export default OfflineDataService;
```

---

## 6. Sa-Token认证授权实现方案

### 6.1 Sa-Token配置

#### Maven依赖配置
```xml
<!-- pom.xml -->
<dependencies>
    <!-- Sa-Token 权限认证 -->
    <dependency>
        <groupId>cn.dev33</groupId>
        <artifactId>sa-token-spring-boot-starter</artifactId>
        <version>1.37.0</version>
    </dependency>

    <!-- Sa-Token 整合 Redis -->
    <dependency>
        <groupId>cn.dev33</groupId>
        <artifactId>sa-token-dao-redis-jackson</artifactId>
        <version>1.37.0</version>
    </dependency>

    <!-- Redis连接池 -->
    <dependency>
        <groupId>org.apache.commons</groupId>
        <artifactId>commons-pool2</artifactId>
    </dependency>
</dependencies>
```

#### Sa-Token配置文件
```yaml
# application.yml
sa-token:
  # token名称 (同时也是cookie名称)
  token-name: satoken
  # token有效期，单位s 默认30天, -1代表永不过期
  timeout: 2592000
  # token临时有效期 (指定时间内无操作就视为token过期) 单位: 秒
  activity-timeout: -1
  # 是否允许同一账号并发登录 (为true时允许一起登录, 为false时新登录挤掉旧登录)
  is-concurrent: true
  # 在多人登录同一账号时，是否共用一个token (为true时所有登录共用一个token, 为false时每次登录新建一个token)
  is-share: true
  # token风格
  token-style: uuid
  # 是否输出操作日志
  is-log: false
  # 是否从cookie中读取token
  is-read-cookie: false
  # 是否从header中读取token
  is-read-header: true
  # token前缀
  token-prefix: Bearer
  # jwt秘钥
  jwt-secret-key: abcdefghijklmnopqrstuvwxyz
```

### 6.2 JWT Token配置和管理

#### JWT工具类
```java
// src/main/java/com/gooseapp/common/utils/JwtUtils.java
@Component
public class JwtUtils {

    @Value("${sa-token.jwt-secret-key}")
    private String secretKey;

    /**
     * 生成JWT Token
     */
    public String generateToken(Long userId, String username) {
        return StpUtil.createLoginSession(userId, SaTokenConsts.DEFAULT_LOGIN_DEVICE);
    }

    /**
     * 解析Token获取用户ID
     */
    public Long getUserIdFromToken(String token) {
        try {
            return StpUtil.getLoginIdAsLong();
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 验证Token是否有效
     */
    public boolean validateToken(String token) {
        try {
            return StpUtil.isLogin();
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 获取Token剩余有效时间
     */
    public long getTokenTimeout() {
        return StpUtil.getTokenTimeout();
    }
}
```

### 6.3 用户登录/注册流程

#### 认证控制器
```java
// src/main/java/com/gooseapp/user/controller/AuthController.java
@RestController
@RequestMapping("/api/v1/auth")
@Validated
public class AuthController {

    @Autowired
    private UserService userService;

    @Autowired
    private JwtUtils jwtUtils;

    /**
     * 用户注册
     */
    @PostMapping("/register")
    public ApiResponse<LoginResponse> register(@Valid @RequestBody UserRegisterDTO registerDTO) {
        // 检查用户是否已存在
        if (userService.existsByUsername(registerDTO.getUsername())) {
            throw new BusinessException(ErrorCode.USER_ALREADY_EXISTS);
        }

        if (userService.existsByEmail(registerDTO.getEmail())) {
            throw new BusinessException(ErrorCode.USER_ALREADY_EXISTS, "邮箱已被注册");
        }

        // 创建用户
        User user = userService.createUser(registerDTO);

        // 自动登录
        StpUtil.login(user.getId());
        String token = StpUtil.getTokenValue();

        LoginResponse response = LoginResponse.builder()
                .userId(user.getId())
                .username(user.getUsername())
                .nickname(user.getNickname())
                .avatar(user.getAvatar())
                .token(token)
                .expiresIn(StpUtil.getTokenTimeout())
                .build();

        return ApiResponse.success("注册成功", response);
    }

    /**
     * 用户登录
     */
    @PostMapping("/login")
    public ApiResponse<LoginResponse> login(@Valid @RequestBody UserLoginDTO loginDTO) {
        // 验证用户名密码
        User user = userService.authenticate(loginDTO.getUsername(), loginDTO.getPassword());

        if (user == null) {
            throw new BusinessException(ErrorCode.PASSWORD_ERROR);
        }

        // 执行登录
        StpUtil.login(user.getId());
        String token = StpUtil.getTokenValue();

        LoginResponse response = LoginResponse.builder()
                .userId(user.getId())
                .username(user.getUsername())
                .nickname(user.getNickname())
                .avatar(user.getAvatar())
                .token(token)
                .expiresIn(StpUtil.getTokenTimeout())
                .build();

        return ApiResponse.success("登录成功", response);
    }

    /**
     * 用户登出
     */
    @PostMapping("/logout")
    @SaCheckLogin
    public ApiResponse<Void> logout() {
        StpUtil.logout();
        return ApiResponse.success("登出成功");
    }

    /**
     * 刷新Token
     */
    @PostMapping("/refresh")
    @SaCheckLogin
    public ApiResponse<TokenRefreshResponse> refreshToken() {
        // 刷新Token
        StpUtil.renewTimeout(2592000); // 30天
        String newToken = StpUtil.getTokenValue();

        TokenRefreshResponse response = TokenRefreshResponse.builder()
                .token(newToken)
                .expiresIn(StpUtil.getTokenTimeout())
                .build();

        return ApiResponse.success("Token刷新成功", response);
    }
}
```

### 6.4 权限控制和角色管理

#### 权限注解使用
```java
// src/main/java/com/gooseapp/goose/controller/GooseController.java
@RestController
@RequestMapping("/api/v1/geese")
@SaCheckLogin  // 类级别登录校验
public class GooseController {

    @Autowired
    private GooseService gooseService;

    /**
     * 获取用户的小鹅列表
     */
    @GetMapping
    public ApiResponse<PageResponse<GooseDTO>> getGeese(
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(required = false) Integer status) {

        Long userId = StpUtil.getLoginIdAsLong();
        PageResponse<GooseDTO> geese = gooseService.getUserGeese(userId, page, size, status);
        return ApiResponse.success("查询成功", geese);
    }

    /**
     * 获取小鹅详情
     */
    @GetMapping("/{gooseId}")
    public ApiResponse<GooseDetailDTO> getGooseDetail(@PathVariable Long gooseId) {
        Long userId = StpUtil.getLoginIdAsLong();

        // 验证小鹅所有权
        if (!gooseService.isGooseOwnedByUser(gooseId, userId)) {
            throw new BusinessException(ErrorCode.GOOSE_NOT_OWNED);
        }

        GooseDetailDTO goose = gooseService.getGooseDetail(gooseId);
        return ApiResponse.success("查询成功", goose);
    }

    /**
     * 小鹅互动操作
     */
    @PostMapping("/{gooseId}/interact")
    public ApiResponse<InteractionResultDTO> interact(
            @PathVariable Long gooseId,
            @Valid @RequestBody InteractionDTO interactionDTO) {

        Long userId = StpUtil.getLoginIdAsLong();

        // 验证小鹅所有权
        if (!gooseService.isGooseOwnedByUser(gooseId, userId)) {
            throw new BusinessException(ErrorCode.GOOSE_NOT_OWNED);
        }

        InteractionResultDTO result = gooseService.interact(userId, gooseId, interactionDTO);
        return ApiResponse.success("互动成功", result);
    }
}
```

### 6.5 Token刷新机制

#### 自动刷新拦截器
```java
// src/main/java/com/gooseapp/common/interceptor/TokenRefreshInterceptor.java
@Component
public class TokenRefreshInterceptor implements HandlerInterceptor {

    private static final long REFRESH_THRESHOLD = 7 * 24 * 60 * 60; // 7天

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) {
        // 检查是否需要刷新Token
        if (StpUtil.isLogin()) {
            long timeout = StpUtil.getTokenTimeout();

            // 如果Token剩余时间少于7天，自动续期
            if (timeout > 0 && timeout < REFRESH_THRESHOLD) {
                StpUtil.renewTimeout(2592000); // 续期30天

                // 在响应头中返回新的过期时间
                response.setHeader("X-Token-Timeout", String.valueOf(StpUtil.getTokenTimeout()));
            }
        }

        return true;
    }
}
```

#### 配置拦截器
```java
// src/main/java/com/gooseapp/config/WebConfig.java
@Configuration
public class WebConfig implements WebMvcConfigurer {

    @Autowired
    private TokenRefreshInterceptor tokenRefreshInterceptor;

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        registry.addInterceptor(tokenRefreshInterceptor)
                .addPathPatterns("/api/**")
                .excludePathPatterns("/api/v1/auth/login", "/api/v1/auth/register");
    }
}
```

---

## 7. 开发环境搭建指南

### 7.1 JDK、Maven、MySQL安装配置

#### JDK 17安装
```bash
# macOS (使用Homebrew)
brew install openjdk@17

# Ubuntu/Debian
sudo apt update
sudo apt install openjdk-17-jdk

# Windows
# 下载Oracle JDK 17或OpenJDK 17
# 配置JAVA_HOME环境变量
```

#### Maven安装配置
```bash
# macOS
brew install maven

# Ubuntu/Debian
sudo apt install maven

# Windows
# 下载Maven二进制包，解压并配置环境变量

# 验证安装
mvn -version
```

#### Maven配置文件（settings.xml）
```xml
<?xml version="1.0" encoding="UTF-8"?>
<settings xmlns="http://maven.apache.org/SETTINGS/1.0.0"
          xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
          xsi:schemaLocation="http://maven.apache.org/SETTINGS/1.0.0
          http://maven.apache.org/xsd/settings-1.0.0.xsd">

  <!-- 本地仓库路径 -->
  <localRepository>${user.home}/.m2/repository</localRepository>

  <!-- 镜像配置 -->
  <mirrors>
    <mirror>
      <id>aliyun</id>
      <name>Aliyun Maven</name>
      <url>https://maven.aliyun.com/repository/public</url>
      <mirrorOf>central</mirrorOf>
    </mirror>
  </mirrors>

  <!-- 配置文件 -->
  <profiles>
    <profile>
      <id>jdk-17</id>
      <activation>
        <activeByDefault>true</activeByDefault>
        <jdk>17</jdk>
      </activation>
      <properties>
        <maven.compiler.source>17</maven.compiler.source>
        <maven.compiler.target>17</maven.compiler.target>
        <maven.compiler.compilerVersion>17</maven.compiler.compilerVersion>
      </properties>
    </profile>
  </profiles>
</settings>
```

#### MySQL 8.0安装配置
```bash
# macOS
brew install mysql@8.0
brew services start mysql@8.0

# Ubuntu/Debian
sudo apt update
sudo apt install mysql-server-8.0

# Windows
# 下载MySQL Installer，选择Developer Default安装

# 初始化数据库
mysql_secure_installation

# 创建应用数据库
mysql -u root -p
CREATE DATABASE goose_app DEFAULT CHARACTER SET utf8mb4 DEFAULT COLLATE utf8mb4_unicode_ci;
CREATE USER 'goose_user'@'localhost' IDENTIFIED BY 'goose_password';
GRANT ALL PRIVILEGES ON goose_app.* TO 'goose_user'@'localhost';
FLUSH PRIVILEGES;
```

### 7.2 React Native开发环境配置

#### React Native SDK安装
```bash
# macOS
brew install react-native-cli

# 或手动下载
git clone https://github.com/facebook/react-native.git -b stable
export PATH="$PATH:`pwd`/react-native/bin"

# Windows/Linux
# 下载React Native SDK，解压并配置PATH环境变量

# 验证安装
react-native doctor
```

#### React Native依赖配置
```json
// package.json
{
  "name": "goose-app",
  "version": "1.0.0",
  "description": "小鹅养成APP React Native客户端",
  "main": "index.js",
  "scripts": {
    "android": "react-native run-android",
    "ios": "react-native run-ios",
    "start": "react-native start",
    "test": "jest",
    "lint": "eslint ."
  },
  "dependencies": {
    "react": "18.2.0",
    "react-native": "0.72.6",
    
    // 状态管理
    "@reduxjs/toolkit": "^1.9.7",
    "react-redux": "^8.1.3",
    "redux-persist": "^6.0.0",
    
    // 导航
    "@react-navigation/native": "^6.1.9",
    "@react-navigation/stack": "^6.3.20",
    "@react-navigation/bottom-tabs": "^6.5.11",
    
    // UI组件
    "react-native-elements": "^3.4.3",
    "react-native-vector-icons": "^10.0.2",
    "react-native-paper": "^5.11.3",
    
    // 网络请求
    "axios": "^1.6.2",
    
    // 本地存储
    "@react-native-async-storage/async-storage": "^1.19.5",
    
    // 微信SDK
    "react-native-wechat-lib": "^1.2.2",
    
    // 多媒体
    "react-native-video": "^5.2.1",
    "react-native-image-picker": "^7.0.3",
    "react-native-image-crop-picker": "^0.40.3",
    
    // 分享
    "react-native-share": "^10.0.2",
    
    // 动画
    "react-native-reanimated": "^3.6.1",
    "react-native-gesture-handler": "^2.14.0",
    
    // 安全相关
    "react-native-keychain": "^8.1.3",
    
    // 工具库
    "react-native-device-info": "^10.11.0",
    "react-native-permissions": "^4.1.4",
    "react-native-bootsplash": "^5.4.1"
  },
  "devDependencies": {
    "@babel/core": "^7.20.0",
    "@babel/preset-env": "^7.20.0",
    "@babel/runtime": "^7.20.0",
    "@react-native/eslint-config": "^0.72.2",
    "@react-native/metro-config": "^0.72.11",
    "@tsconfig/react-native": "^3.0.0",
    "@types/react": "^18.0.24",
    "@types/react-test-renderer": "^18.0.0",
    "babel-jest": "^29.2.1",
    "eslint": "^8.19.0",
    "jest": "^29.2.1",
    "metro-react-native-babel-preset": "0.76.8",
    "prettier": "^2.4.1",
    "react-test-renderer": "18.2.0",
    "typescript": "4.8.4"
  },
  "engines": {
    "node": ">=16"
  }
}
```

### 7.3 IDE推荐配置

#### IntelliJ IDEA配置（后端开发）
```
推荐插件：
1. Spring Boot
2. Maven Helper
3. MyBatis Log Plugin
4. Rainbow Brackets
5. Translation
6. Alibaba Java Coding Guidelines
7. SonarLint
8. GitToolBox

配置建议：
- 设置JDK 17
- 配置Maven settings.xml路径
- 启用自动导入
- 配置代码格式化规则
- 设置数据库连接
```

#### VS Code配置（React Native开发）
```json
// .vscode/settings.json
{
  "typescript.suggest.autoImports": true,
  "typescript.updateImportsOnFileMove.enabled": "always",
  "editor.formatOnSave": true,
  "editor.codeActionsOnSave": {
    "source.fixAll.eslint": true,
    "source.organizeImports": true
  },
  "emmet.includeLanguages": {
    "typescript": "typescriptreact",
    "javascript": "javascriptreact"
  },
  "files.associations": {
    "*.js": "javascriptreact",
    "*.ts": "typescriptreact"
  },
  "reactNative.experiments.fastRefresh": true
}
```

```json
// .vscode/extensions.json
{
  "recommendations": [
    "ms-vscode.vscode-typescript-next",
    "msjsdiag.vscode-react-native",
    "bradlc.vscode-tailwindcss",
    "usernamehw.errorlens",
    "gruntfuggly.todo-tree",
    "ms-vscode.vscode-json",
    "formulahendry.auto-rename-tag",
    "christian-kohler.path-intellisense",
    "ms-vscode.vscode-eslint"
  ]
}
```

### 7.4 本地调试和热重载配置

#### Spring Boot开发配置
```yaml
# application-dev.yml
server:
  port: 8080
  servlet:
    context-path: /

spring:
  profiles:
    active: dev

  datasource:
    url: **********************************************************************************************************************
    username: goose_user
    password: goose_password
    driver-class-name: com.mysql.cj.jdbc.Driver

  jpa:
    hibernate:
      ddl-auto: update
    show-sql: true
    properties:
      hibernate:
        format_sql: true

  redis:
    host: localhost
    port: 6379
    password:
    database: 0

  devtools:
    restart:
      enabled: true
    livereload:
      enabled: true

logging:
  level:
    com.gooseapp: DEBUG
    org.springframework.web: DEBUG
    org.hibernate.SQL: DEBUG
    org.hibernate.type.descriptor.sql.BasicBinder: TRACE
```

#### React Native调试配置
```json
// .vscode/launch.json
{
  "version": "0.2.0",
  "configurations": [
    {
      "name": "React Native Android (Debug)",
      "type": "reactnative",
      "request": "launch",
      "platform": "android",
      "sourceMaps": true,
      "outDir": "${workspaceFolder}/.vscode/.react"
    },
    {
      "name": "React Native iOS (Debug)",
      "type": "reactnative",
      "request": "launch",
      "platform": "ios",
      "sourceMaps": true,
      "outDir": "${workspaceFolder}/.vscode/.react"
    },
    {
      "name": "Attach to packager",
      "type": "reactnative",
      "request": "attach",
      "sourceMaps": true,
      "outDir": "${workspaceFolder}/.vscode/.react"
    }
  ]
}
```

#### 环境变量配置
```typescript
// src/config/environment.ts
interface Config {
  API_BASE_URL: string;
  APP_NAME: string;
  VERSION: string;
  IS_DEV: boolean;
}

const config: Config = {
  API_BASE_URL: __DEV__ 
    ? 'http://localhost:8080/api/v1'
    : 'https://api.goose-app.com/api/v1',
  APP_NAME: __DEV__ ? '小鹅养成APP (Dev)' : '小鹅养成APP',
  VERSION: '1.0.0',
  IS_DEV: __DEV__
};

export default config;
```

---

## 8. 项目目录结构规划

### 8.1 后端Spring Boot项目结构

```
goose-app-backend/
├── pom.xml                                 # Maven配置文件
├── README.md                               # 项目说明文档
├── .gitignore                              # Git忽略文件
├── docker-compose.yml                      # Docker编排文件
├── Dockerfile                              # Docker镜像构建文件
├── docs/                                   # 项目文档
│   ├── api.md                             # API文档
│   ├── database.md                        # 数据库设计文档
│   └── deployment.md                      # 部署文档
├── scripts/                               # 脚本文件
│   ├── init-db.sql                        # 数据库初始化脚本
│   ├── start.sh                           # 启动脚本
│   └── deploy.sh                          # 部署脚本
└── src/
    ├── main/
    │   ├── java/
    │   │   └── com/
    │   │       └── gooseapp/
    │   │           ├── GooseAppApplication.java    # 主启动类
    │   │           ├── common/                     # 公共模块
    │   │           │   ├── config/                 # 配置类
    │   │           │   │   ├── WebConfig.java
    │   │           │   │   ├── SaTokenConfig.java
    │   │           │   │   ├── RedisConfig.java
    │   │           │   │   └── SwaggerConfig.java
    │   │           │   ├── constant/               # 常量定义
    │   │           │   │   ├── ErrorCode.java
    │   │           │   │   └── CommonConstants.java
    │   │           │   ├── exception/              # 异常处理
    │   │           │   │   ├── BusinessException.java
    │   │           │   │   ├── GlobalExceptionHandler.java
    │   │           │   │   └── ValidationException.java
    │   │           │   ├── interceptor/            # 拦截器
    │   │           │   │   ├── TokenRefreshInterceptor.java
    │   │           │   │   └── LoggingInterceptor.java
    │   │           │   ├── response/               # 响应封装
    │   │           │   │   ├── ApiResponse.java
    │   │           │   │   ├── PageResponse.java
    │   │           │   │   └── ErrorResponse.java
    │   │           │   └── utils/                  # 工具类
    │   │           │       ├── JwtUtils.java
    │   │           │       ├── PasswordUtils.java
    │   │           │       ├── DateUtils.java
    │   │           │       └── ValidationUtils.java
    │   │           ├── user/                       # 用户模块
    │   │           │   ├── controller/
    │   │           │   │   ├── AuthController.java
    │   │           │   │   └── UserController.java
    │   │           │   ├── service/
    │   │           │   │   ├── UserService.java
    │   │           │   │   └── impl/
    │   │           │   │       └── UserServiceImpl.java
    │   │           │   ├── repository/
    │   │           │   │   └── UserRepository.java
    │   │           │   ├── entity/
    │   │           │   │   └── User.java
    │   │           │   └── dto/
    │   │           │       ├── UserLoginDTO.java
    │   │           │       ├── UserRegisterDTO.java
    │   │           │       ├── UserProfileDTO.java
    │   │           │       ├── LoginResponse.java
    │   │           │       └── TokenRefreshResponse.java
    │   │           ├── goose/                      # 小鹅模块
    │   │           │   ├── controller/
    │   │           │   │   ├── GooseController.java
    │   │           │   │   └── InteractionController.java
    │   │           │   ├── service/
    │   │           │   │   ├── GooseService.java
    │   │           │   │   ├── InteractionService.java
    │   │           │   │   └── impl/
    │   │           │   │       ├── GooseServiceImpl.java
    │   │           │   │       └── InteractionServiceImpl.java
    │   │           │   ├── repository/
    │   │           │   │   ├── GooseRepository.java
    │   │           │   │   └── InteractionRepository.java
    │   │           │   ├── entity/
    │   │           │   │   ├── Goose.java
    │   │           │   │   └── Interaction.java
    │   │           │   └── dto/
    │   │           │       ├── GooseDTO.java
    │   │           │       ├── GooseDetailDTO.java
    │   │           │       ├── GooseStatusDTO.java
    │   │           │       ├── InteractionDTO.java
    │   │           │       └── InteractionResultDTO.java
    │   │           ├── shop/                       # 商城模块
    │   │           │   ├── controller/
    │   │           │   │   ├── ShopController.java
    │   │           │   │   └── OrderController.java
    │   │           │   ├── service/
    │   │           │   │   ├── ShopService.java
    │   │           │   │   ├── OrderService.java
    │   │           │   │   └── impl/
    │   │           │   │       ├── ShopServiceImpl.java
    │   │           │   │       └── OrderServiceImpl.java
    │   │           │   ├── repository/
    │   │           │   │   ├── ProductRepository.java
    │   │           │   │   ├── OrderRepository.java
    │   │           │   │   └── OrderItemRepository.java
    │   │           │   ├── entity/
    │   │           │   │   ├── Product.java
    │   │           │   │   ├── Order.java
    │   │           │   │   └── OrderItem.java
    │   │           │   └── dto/
    │   │           │       ├── ProductDTO.java
    │   │           │       ├── OrderDTO.java
    │   │           │       ├── OrderCreateDTO.java
    │   │           │       └── CartItemDTO.java
    │   │           └── gift/                       # 转赠模块
    │   │               ├── controller/
    │   │               │   └── GiftController.java
    │   │               ├── service/
    │   │               │   ├── GiftService.java
    │   │               │   └── impl/
    │   │               │       └── GiftServiceImpl.java
    │   │               ├── repository/
    │   │               │   └── GiftRepository.java
    │   │               ├── entity/
    │   │               │   └── Gift.java
    │   │               └── dto/
    │   │                   ├── GiftDTO.java
    │   │                   ├── GiftCreateDTO.java
    │   │                   └── GiftCardDTO.java
    │   └── resources/
    │       ├── application.yml                     # 主配置文件
    │       ├── application-dev.yml                 # 开发环境配置
    │       ├── application-prod.yml                # 生产环境配置
    │       ├── logback-spring.xml                  # 日志配置
    │       ├── static/                             # 静态资源
    │       │   ├── images/
    │       │   ├── css/
    │       │   └── js/
    │       └── templates/                          # 模板文件
    └── test/
        └── java/
            └── com/
                └── gooseapp/
                    ├── GooseAppApplicationTests.java
                    ├── user/
                    │   ├── UserServiceTest.java
                    │   └── UserControllerTest.java
                    ├── goose/
                    │   ├── GooseServiceTest.java
                    │   └── InteractionServiceTest.java
                    ├── shop/
                    │   ├── ShopServiceTest.java
                    │   └── OrderServiceTest.java
                    └── gift/
                        └── GiftServiceTest.java
```

### 8.2 React Native项目目录组织

```
goose-app-react-native/
├── package.json                            # 项目配置和依赖
├── README.md                               # 项目说明
├── .gitignore                              # Git忽略文件
├── .eslintrc.js                            # ESLint配置
├── .prettierrc                             # Prettier配置
├── babel.config.js                         # Babel配置
├── metro.config.js                         # Metro打包配置
├── tsconfig.json                           # TypeScript配置
├── .vscode/                                # VS Code配置
│   ├── settings.json
│   ├── launch.json
│   └── extensions.json
├── android/                                # Android平台配置
├── ios/                                    # iOS平台配置
├── assets/                                 # 资源文件
│   ├── images/                             # 图片资源
│   │   ├── logo.png
│   │   ├── placeholder.png
│   │   └── geese/
│   ├── icons/                              # 图标资源
│   │   ├── app_icon.png
│   │   └── tab_icons/
│   ├── fonts/                              # 字体文件
│   │   ├── PingFang-Regular.ttf
│   │   ├── PingFang-Medium.ttf
│   │   └── PingFang-Bold.ttf
│   └── config/                             # 配置文件
│       ├── dev.json
│       └── prod.json
└── src/
    ├── App.tsx                             # 应用入口组件
    ├── index.js                            # 应用注册入口
    ├── config/                             # 配置管理
    │   ├── environment.ts
    │   ├── appConfig.ts
    │   └── themeConfig.ts
    ├── core/                               # 核心功能
    │   ├── constants/                      # 常量定义
    │   │   ├── appConstants.ts
    │   │   ├── apiConstants.ts
    │   │   └── storageConstants.ts
    │   ├── errors/                         # 错误处理
    │   │   ├── exceptions.ts
    │   │   ├── failures.ts
    │   │   └── errorHandler.ts
    │   ├── network/                        # 网络层
    │   │   ├── apiClient.ts
    │   │   ├── interceptors.ts
    │   │   └── networkInfo.ts
    │   ├── storage/                        # 存储层
    │   │   ├── localStorage.ts
    │   │   ├── secureStorage.ts
    │   │   └── cacheManager.ts
    │   └── utils/                          # 工具类
    │       ├── dateUtils.ts
    │       ├── validationUtils.ts
    │       ├── formatUtils.ts
    │       └── logger.ts
    ├── shared/                             # 共享组件
    │   ├── components/                     # 通用组件
    │   │   ├── buttons/
    │   │   │   ├── PrimaryButton.tsx
    │   │   │   ├── SecondaryButton.tsx
    │   │   │   └── IconButton.tsx
    │   │   ├── cards/
    │   │   │   ├── BaseCard.tsx
    │   │   │   ├── GooseCard.tsx
    │   │   │   └── ProductCard.tsx
    │   │   ├── inputs/
    │   │   │   ├── TextField.tsx
    │   │   │   ├── SearchField.tsx
    │   │   │   └── PasswordField.tsx
    │   │   ├── loading/
    │   │   │   ├── LoadingIndicator.tsx
    │   │   │   ├── SkeletonLoader.tsx
    │   │   │   └── RefreshIndicator.tsx
    │   │   ├── navigation/
    │   │   │   ├── BottomTabBar.tsx
    │   │   │   ├── AppHeader.tsx
    │   │   │   └── DrawerContent.tsx
    │   │   └── modals/
    │   │       ├── AlertModal.tsx
    │   │       ├── ConfirmationModal.tsx
    │   │       └── LoadingModal.tsx
    │   ├── styles/                         # 样式配置
    │   │   ├── theme.ts
    │   │   ├── colors.ts
    │   │   ├── typography.ts
    │   │   └── dimensions.ts
    │   ├── hooks/                          # 自定义Hook
    │   │   ├── useAuth.ts
    │   │   ├── useStorage.ts
    │   │   ├── useNetwork.ts
    │   │   └── usePermissions.ts
    │   └── types/                          # 类型定义
    │       ├── common.ts
    │       ├── api.ts
    │       └── navigation.ts
    ├── features/                           # 功能模块
    │   ├── auth/                           # 认证模块
    │   │   ├── types/
    │   │   │   ├── user.ts
    │   │   │   ├── loginRequest.ts
    │   │   │   └── loginResponse.ts
    │   │   ├── services/
    │   │   │   ├── authService.ts
    │   │   │   └── userService.ts
    │   │   ├── store/
    │   │   │   ├── authSlice.ts
    │   │   │   └── userSlice.ts
    │   │   ├── screens/
    │   │   │   ├── LoginScreen.tsx
    │   │   │   ├── RegisterScreen.tsx
    │   │   │   └── SplashScreen.tsx
    │   │   └── components/
    │   │       ├── LoginForm.tsx
    │   │       └── RegisterForm.tsx
    │   ├── discover/                       # 发现页模块
    │   │   ├── types/
    │   │   ├── services/
    │   │   ├── store/
    │   │   ├── screens/
    │   │   └── components/
    │   ├── goose/                          # 小鹅模块
    │   │   ├── types/
    │   │   │   ├── goose.ts
    │   │   │   └── interaction.ts
    │   │   ├── services/
    │   │   │   ├── gooseService.ts
    │   │   │   └── interactionService.ts
    │   │   ├── store/
    │   │   │   ├── gooseSlice.ts
    │   │   │   └── interactionSlice.ts
    │   │   ├── screens/
    │   │   │   ├── GooseListScreen.tsx
    │   │   │   ├── GooseDetailScreen.tsx
    │   │   │   └── VideoPlayerScreen.tsx
    │   │   └── components/
    │   │       ├── GooseCard.tsx
    │   │       ├── StatusBar.tsx
    │   │       ├── InteractionButtons.tsx
    │   │       └── VideoPlayer.tsx
    │   ├── shop/                           # 商城模块
    │   │   ├── types/
    │   │   ├── services/
    │   │   ├── store/
    │   │   ├── screens/
    │   │   └── components/
    │   ├── gift/                           # 转赠模块
    │   │   ├── types/
    │   │   ├── services/
    │   │   ├── store/
    │   │   ├── screens/
    │   │   └── components/
    │   └── profile/                        # 个人中心模块
    │       ├── types/
    │       ├── services/
    │       ├── store/
    │       ├── screens/
    │       └── components/
    ├── navigation/                         # 导航配置
    │   ├── AppNavigator.tsx
    │   ├── AuthNavigator.tsx
    │   ├── TabNavigator.tsx
    │   ├── routeNames.ts
    │   └── navigationService.ts
    ├── store/                              # 状态管理
    │   ├── index.ts
    │   ├── rootReducer.ts
    │   ├── middleware.ts
    │   └── persistConfig.ts
    └── services/                           # 全局服务
        ├── apiService.ts
        ├── authService.ts
        ├── notificationService.ts
        ├── storageService.ts
        └── analyticsService.ts
```

## 9. 开发阶段划分和里程碑计划

### 9.1 MVP功能优先级排序

#### P0级功能（核心必备功能）
1. **用户认证系统**
   - 用户注册/登录
   - Token管理
   - 基础权限控制

2. **小鹅养成核心功能**
   - 小鹅基础信息展示
   - 状态系统（健康、饥饿、清洁）
   - 基础互动操作（喂食、喂水、清洁）
   - 冷却时间机制

3. **商城基础功能**
   - 商品列表展示
   - 商品详情查看
   - 基础购买流程

#### P1级功能（重要功能）
1. **小鹅高级功能**
   - 视频直播展示
   - 经验值和等级系统
   - 成长数据统计

2. **商城高级功能**
   - 商品搜索和筛选
   - 购物车管理
   - 订单管理

3. **转赠功能**
   - 转赠流程
   - 分享卡片生成
   - 转赠记录管理

#### P2级功能（优化功能）
1. **社交功能**
   - 用户动态
   - 好友系统
   - 评论互动

2. **个性化功能**
   - 个人中心完善
   - 设置页面
   - 主题切换

### 9.2 具体开发时间线（周为单位）

#### 第1-2周：项目初始化和基础架构
**后端开发（1.5周）**
- [x] 项目创建和基础配置
- [x] 数据库设计和创建
- [x] Sa-Token认证框架集成
- [x] 基础架构搭建（Controller-Service-Repository）
- [x] 统一响应格式和异常处理
- [x] 用户模块基础功能（注册/登录）

**前端开发（1.5周）**
- [x] React Native项目创建和配置
- [x] 基础UI组件库搭建（React Native Elements）
- [x] 导航和状态管理配置（React Navigation + Redux Toolkit）
- [x] HTTP客户端和拦截器配置（Axios）
- [x] 登录/注册页面开发

**里程碑1：基础架构完成**
- ✅ 后端基础框架搭建完成
- ✅ 前端基础架构搭建完成
- ✅ 用户认证功能可用
- ✅ 前后端通信正常

#### 第3-4周：小鹅核心功能开发
**后端开发（2周）**
- [ ] 小鹅实体和数据模型设计
- [ ] 小鹅CRUD操作接口
- [ ] 互动系统业务逻辑
- [ ] 状态更新和冷却时间机制
- [ ] 小鹅数据初始化

**前端开发（2周）**
- [ ] 小鹅列表页面开发
- [ ] 小鹅详情页面开发
- [ ] 状态条组件开发
- [ ] 互动按钮组件开发
- [ ] 实时状态更新机制

**里程碑2：小鹅核心功能完成**
- [ ] 小鹅基础信息展示
- [ ] 状态系统正常运行
- [ ] 互动操作功能完整
- [ ] 冷却时间机制生效

#### 第5-6周：商城基础功能开发
**后端开发（2周）**
- [ ] 商品管理系统
- [ ] 商品列表和详情接口
- [ ] 订单创建和管理
- [ ] 支付流程模拟
- [ ] 库存管理机制

**前端开发（2周）**
- [ ] 商城首页开发
- [ ] 商品列表页面
- [ ] 商品详情页面
- [ ] 购物车功能
- [ ] 订单确认页面

**里程碑3：商城基础功能完成**
- [ ] 商品展示功能完整
- [ ] 购买流程顺畅
- [ ] 订单管理正常
- [ ] 支付模拟可用

#### 第7-8周：高级功能开发
**后端开发（2周）**
- [ ] 视频直播URL管理
- [ ] 经验值和等级系统
- [ ] 商品搜索和筛选
- [ ] 转赠功能后端逻辑
- [ ] 数据统计接口

**前端开发（2周）**
- [ ] 视频播放器集成
- [ ] 搜索和筛选功能
- [ ] 转赠流程页面
- [ ] 个人中心页面
- [ ] 数据统计展示

**里程碑4：高级功能完成**
- [ ] 视频播放功能正常
- [ ] 搜索筛选可用
- [ ] 转赠功能完整
- [ ] 个人中心完善

#### 第9-10周：测试和优化
**测试阶段（1.5周）**
- [ ] 单元测试编写和执行
- [ ] 集成测试
- [ ] 端到端测试
- [ ] 性能测试
- [ ] 兼容性测试

**优化阶段（0.5周）**
- [ ] 性能优化
- [ ] UI/UX优化
- [ ] 代码重构
- [ ] 文档完善
- [ ] 部署准备

**里程碑5：项目完成**
- [ ] 所有功能测试通过
- [ ] 性能指标达标
- [ ] 代码质量合格
- [ ] 文档完整
- [ ] 可部署上线

### 9.3 测试和部署计划

#### 测试策略
```
测试金字塔：
├── E2E测试 (10%)
│   ├── 关键用户流程测试
│   ├── 跨平台兼容性测试
│   └── 性能基准测试
├── 集成测试 (30%)
│   ├── API接口测试
│   ├── 数据库集成测试
│   └── 第三方服务集成测试
└── 单元测试 (60%)
    ├── 业务逻辑测试
    ├── 工具类测试
    └── 组件测试
```

#### 后端测试配置
```java
// src/test/java/com/gooseapp/user/UserServiceTest.java
@SpringBootTest
@Transactional
class UserServiceTest {

    @Autowired
    private UserService userService;

    @Test
    void testUserRegistration() {
        UserRegisterDTO registerDTO = new UserRegisterDTO();
        registerDTO.setUsername("testuser");
        registerDTO.setEmail("<EMAIL>");
        registerDTO.setPassword("password123");

        User user = userService.createUser(registerDTO);

        assertThat(user).isNotNull();
        assertThat(user.getUsername()).isEqualTo("testuser");
        assertThat(user.getEmail()).isEqualTo("<EMAIL>");
    }

    @Test
    void testUserAuthentication() {
        // 测试用户认证逻辑
    }
}
```

#### 前端测试配置
```typescript
// __tests__/features/auth/authService.test.ts
import { authService } from '../../../src/features/auth/services/authService';
import { LoginRequest, User } from '../../../src/features/auth/types';

// Mock axios
jest.mock('axios');

describe('Auth Service Tests', () => {
  test('should login successfully with valid credentials', async () => {
    // Arrange
    const loginRequest: LoginRequest = {
      username: 'testuser',
      password: 'password123'
    };
    
    const expectedUser: User = {
      id: 1,
      username: 'testuser',
      email: '<EMAIL>',
      nickname: 'Test User'
    };

    // Mock API response
    jest.spyOn(authService, 'login').mockResolvedValue({
      user: expectedUser,
      token: 'mock-token'
    });

    // Act
    const result = await authService.login(loginRequest);

    // Assert
    expect(result).toBeDefined();
    expect(result.user.username).toBe('testuser');
    expect(result.token).toBe('mock-token');
  });

  test('should handle login failure', async () => {
    // Arrange
    const loginRequest: LoginRequest = {
      username: 'invaliduser',
      password: 'wrongpassword'
    };

    jest.spyOn(authService, 'login').mockRejectedValue(
      new Error('Invalid credentials')
    );

    // Act & Assert
    await expect(authService.login(loginRequest)).rejects.toThrow(
      'Invalid credentials'
    );
  });
});
```

#### 部署配置
```yaml
# docker-compose.yml
version: '3.8'
services:
  app:
    build: .
    ports:
      - "8080:8080"
    environment:
      - SPRING_PROFILES_ACTIVE=prod
      - MYSQL_HOST=db
      - REDIS_HOST=redis
    depends_on:
      - db
      - redis

  db:
    image: mysql:8.0
    environment:
      MYSQL_ROOT_PASSWORD: rootpassword
      MYSQL_DATABASE: goose_app
      MYSQL_USER: goose_user
      MYSQL_PASSWORD: goose_password
    volumes:
      - mysql_data:/var/lib/mysql

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"

volumes:
  mysql_data:
```

### 9.4 风险评估和应对策略

#### 技术风险
1. **数据库性能风险**
   - 风险：大量用户同时互动导致数据库压力
   - 应对：数据库连接池优化、读写分离、缓存策略

2. **移动端兼容性风险**
   - 风险：不同设备和系统版本兼容性问题
   - 应对：充分的设备测试、渐进式功能发布

3. **实时性能风险**
   - 风险：小鹅状态更新延迟、视频播放卡顿
   - 应对：WebSocket实时通信、CDN加速、性能监控

#### 进度风险
1. **开发进度延期风险**
   - 风险：功能复杂度超预期、技术难点耗时过长
   - 应对：功能优先级调整、并行开发、技术预研

2. **测试时间不足风险**
   - 风险：开发阶段延期导致测试时间压缩
   - 应对：测试驱动开发、自动化测试、持续集成

#### 业务风险
1. **用户体验风险**
   - 风险：界面不够友好、操作流程复杂
   - 应对：用户测试、UI/UX迭代、原型验证

2. **数据安全风险**
   - 风险：用户数据泄露、支付安全问题
   - 应对：数据加密、安全审计、权限控制

### 9.5 项目交付标准

#### 功能完整性标准
- [ ] 所有P0功能100%完成
- [ ] 所有P1功能90%完成
- [ ] 核心用户流程无阻塞问题
- [ ] API接口响应时间<500ms
- [ ] 移动端启动时间<3秒

#### 代码质量标准
- [ ] 单元测试覆盖率>80%
- [ ] 代码审查通过率100%
- [ ] 无严重安全漏洞
- [ ] 代码规范检查通过
- [ ] 文档完整度>90%

#### 性能标准
- [ ] 并发用户数>1000
- [ ] 数据库查询响应时间<100ms
- [ ] 页面加载时间<2秒
- [ ] 内存使用率<70%
- [ ] CPU使用率<60%

#### 用户体验标准
- [ ] 界面设计符合UI规范
- [ ] 操作流程简洁直观
- [ ] 错误提示友好明确
- [ ] 离线功能可用
- [ ] 多设备适配良好

---

## 📝 总结

本开发方案为"小鹅养成APP"提供了完整的技术实现路径，从架构设计到具体实现，从开发环境到部署上线，都进行了详细的规划。

### 方案亮点
1. **技术栈成熟稳定**：Java + Spring Boot + MySQL + Sa-Token + React Native的组合经过市场验证
2. **架构设计合理**：分层架构清晰，模块划分明确，便于维护和扩展
3. **开发流程规范**：从需求分析到测试部署，每个环节都有明确的标准和流程
4. **风险控制完善**：识别了主要风险点并制定了相应的应对策略

### 预期效果
- **开发效率**：预计10周完成MVP版本开发
- **代码质量**：通过测试驱动开发和代码审查保证质量
- **用户体验**：基于原型设计确保良好的用户体验
- **系统性能**：通过合理的架构设计和优化策略保证性能

### 后续规划
1. **功能扩展**：基于用户反馈持续迭代功能
2. **性能优化**：根据实际使用情况进行性能调优
3. **平台扩展**：考虑Web端、小程序等多平台支持
4. **商业化**：探索更多商业模式和盈利点

本方案为项目的成功实施提供了坚实的技术基础和明确的执行路径。