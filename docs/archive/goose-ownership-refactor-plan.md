# 小鹅归属逻辑重构实施计划

## 📖 对话总结与项目背景

### 对话背景
在小鹅养成APP后端系统的开发过程中，用户发现了关于小鹅创建和归属逻辑的重大设计问题。通过深入的代码分析和业务逻辑梳理，识别出当前系统设计与正确的业务模式存在根本性偏差。

### 问题发现过程
1. **用户需求明确**: 用户明确提出小鹅应该由后端管理员预先创建管理，C端用户通过购买方式获得小鹅归属权，用户不能直接创建小鹅实体
2. **系统现状分析**: 通过codebase-retrieval工具深入分析了当前系统的数据库设计、API接口、业务逻辑和权限控制
3. **问题识别**: 发现了数据库设计、API接口设计、业务逻辑和权限控制四个层面的设计问题
4. **解决方案制定**: 基于分析结果制定了详细的6步重构计划

### 核心业务逻辑要求
根据用户明确的业务需求，正确的小鹅养成APP业务逻辑应该是：
- **管理员职责**: 后端管理员负责预先创建和管理小鹅，小鹅创建后进入商品池等待销售
- **用户购买**: C端用户只能通过购买方式获得小鹅，购买成功后才建立用户与小鹅的归属关系
- **权限分离**: 用户不应该能够直接创建小鹅实体，创建权限仅限于管理员
- **完整追踪**: 系统需要完整记录小鹅的创建、销售、归属变更等全生命周期信息

### 技术架构要求
基于用户的技术偏好和现有系统架构：
- **技术栈**: Java + Spring Boot + MySQL + Sa-Token + Flutter
- **架构模式**: 分层架构（Controller-Service-Repository-Entity）
- **构建工具**: Maven
- **API规范**: RESTful API设计规范
- **权限框架**: Sa-Token，使用'satoken'作为token的header key，格式为'satoken: Bearer TOKEN'
- **数据库设计**: 采用'逻辑关联而非强绑定外键'的方式，避免不必要的外键约束

### 开发流程要求
用户强调严格的开发流程：
- **详细计划**: 所有新功能必须先制定详细计划
- **进度跟踪**: 实时更新development-progress.md文件
- **测试要求**: 为Service层编写完整单元测试，为Controller层编写集成测试
- **覆盖率标准**: 确保测试覆盖率达到80%以上
- **完成标准**: 只有所有测试通过后才能标记功能为已完成
- **文档要求**: 完整的开发方案文档化，设计文档生成并放置在项目根目录下

## 📋 项目概述

**项目名称**: 小鹅养成APP - 小鹅创建和归属逻辑重构
**项目目标**: 修正小鹅创建和归属逻辑，实现"管理员创建小鹅，用户购买获得归属"的正确业务模式
**技术栈**: Java 17 + Spring Boot 3.2.1 + MySQL 8.0 + Sa-Token + Maven
**预计工期**: 10个工作日
**开始时间**: 待定
**负责人**: 全栈开发工程师

## 🔍 当前系统问题详细分析

### 数据库设计问题
通过对现有数据库结构的分析，发现以下关键问题：

1. **小鹅表(geese)设计缺陷**:
   ```sql
   -- 当前设计问题
   CREATE TABLE geese (
       user_id BIGINT NOT NULL,  -- 强制要求归属用户，无法表示待售状态
       -- 缺少ownership_status字段
       -- 缺少purchase_time字段
   );
   ```
   - `user_id`字段设为NOT NULL，意味着小鹅创建时必须有归属用户
   - 缺少归属状态字段，无法区分"待售"和"已归属"状态
   - 缺少购买时间记录，无法追踪归属变更历史

2. **缺少权限管理表结构**:
   - 系统中没有用户角色表，无法区分管理员和普通用户
   - 缺少权限控制的数据基础

3. **缺少购买记录表**:
   - 没有购买记录表，无法追踪用户购买历史
   - 缺少购买流程的数据支撑

### API接口设计问题
通过代码分析发现的接口设计问题：

1. **存在错误的创建接口**:
   ```java
   // 问题接口：允许普通用户创建小鹅
   @PostMapping("/api/v1/geese")
   public ApiResponse<GooseDTO> createGoose(@RequestBody CreateGooseRequest request)
   ```
   - 该接口允许任何登录用户创建小鹅
   - 违反了"只有管理员可以创建小鹅"的业务规则

2. **缺少管理员专用接口**:
   - 没有管理员创建小鹅的专用接口
   - 缺少商品池管理接口

3. **缺少购买相关接口**:
   - 没有小鹅购买接口
   - 缺少购买历史查询接口

### 业务逻辑问题
Service层业务逻辑的关键问题：

1. **GooseService.createGoose()方法问题**:
   ```java
   // 问题代码：任何用户都可以创建小鹅
   public GooseDTO createGoose(Long userId, String name, String breed, Integer gender) {
       Goose goose = new Goose();
       goose.setUserId(userId);  // 直接绑定用户归属
       // ... 缺少权限验证
   }
   ```
   - 没有权限验证，任何用户都可以调用
   - 创建时直接绑定用户归属，违反业务逻辑

2. **缺少购买业务逻辑**:
   - 没有小鹅购买的Service实现
   - 缺少归属变更的业务逻辑

### 权限控制问题
权限管理方面的设计缺陷：

1. **Sa-Token配置不完整**:
   ```yaml
   # 当前配置缺少角色权限验证
   sa-token:
     token-name: satoken
     # 缺少角色权限相关配置
   ```

2. **缺少权限注解和拦截器**:
   - 没有管理员权限验证注解
   - 缺少权限拦截器实现

3. **用户角色管理缺失**:
   - User实体中没有角色信息
   - 缺少角色管理相关代码

## 🎯 核心业务逻辑变更

### 变更前（错误逻辑）
- C端用户可以直接创建小鹅
- 小鹅创建时直接绑定用户归属
- 缺少购买流程和商品池概念
- 没有权限区分，所有用户权限相同

### 变更后（正确逻辑）
- 只有管理员可以创建小鹅到商品池
- C端用户通过购买方式获得小鹅归属
- 完整的购买流程和归属变更追踪
- 明确的权限分离和角色管理

### 业务流程对比

#### 错误流程（当前）
```
用户登录 → 直接创建小鹅 → 小鹅立即归属用户 → 开始养成
```

#### 正确流程（目标）
```
管理员创建小鹅 → 小鹅进入商品池 → 用户浏览商城 →
选择小鹅 → 执行购买 → 归属变更 → 开始养成
```

## 📋 项目要求与约束条件

### 技术要求
基于用户明确的技术偏好和现有系统架构：

1. **技术栈约束**:
   - **后端**: Java 17 + Spring Boot 3.2.1 + MySQL 8.0 + Sa-Token
   - **前端**: Flutter（本次重构主要涉及后端）
   - **构建工具**: Maven
   - **数据库**: MySQL 8.0，采用逻辑关联而非强绑定外键

2. **架构模式要求**:
   - **分层架构**: 严格按照Controller-Service-Repository-Entity分层
   - **RESTful API**: 所有接口遵循RESTful设计规范
   - **模块化设计**: 按功能模块组织代码结构

3. **Sa-Token配置要求**:
   - Token名称: `satoken`
   - Header格式: `satoken: Bearer TOKEN`（注意是Bearer前缀）
   - 保持现有的Sa-Token配置兼容性

### 开发流程约束

1. **严格的开发顺序**:
   - 必须按照用户认证→小鹅核心→商城→转赠的模块顺序开发
   - 每个模块必须包含完整的后端API、前端页面、数据库操作和单元测试
   - 只有前一个模块完全完成并通过测试后，才能开始下一个模块

2. **测试要求**:
   - **Service层**: 必须编写完整的单元测试
   - **Controller层**: 必须编写集成测试
   - **测试覆盖率**: 必须达到80%以上
   - **测试执行**: 所有测试必须通过后才能标记功能为已完成

3. **文档要求**:
   - **实时更新**: 每个步骤完成后必须更新development-progress.md文件
   - **详细记录**: 需要详细的开发进度跟踪文件和测试执行指南
   - **API文档**: 完整的API文档更新
   - **设计文档**: 所有设计文档生成并放置在项目根目录下

### 质量保证要求

1. **代码质量标准**:
   - 遵循项目现有的编码规范
   - 所有新增代码必须包含充分的注释
   - 通过静态代码分析工具检查
   - 代码审查通过后才能合并

2. **数据安全要求**:
   - 所有涉及数据变更的操作必须有事务保护
   - 敏感操作必须有权限验证
   - 数据库操作必须防止SQL注入
   - 完整的数据备份和恢复机制

3. **性能要求**:
   - API响应时间不超过500ms
   - 数据库查询优化，合理使用索引
   - 并发处理能力满足业务需求

### 业务约束条件

1. **核心业务逻辑约束**:
   - **绝对禁止**: 普通用户直接创建小鹅
   - **强制要求**: 小鹅必须由管理员预先创建
   - **购买流程**: 用户只能通过购买方式获得小鹅归属
   - **数据一致性**: 确保小鹅归属状态与购买记录的一致性

2. **权限管理约束**:
   - 管理员和普通用户权限必须严格分离
   - 所有管理员操作必须有权限验证
   - 权限验证失败必须返回明确的错误信息

3. **数据完整性约束**:
   - 小鹅的创建、销售、归属变更必须有完整记录
   - 购买记录必须与小鹅归属状态保持一致
   - 用户统计数据必须实时更新

### 兼容性要求

1. **向后兼容**:
   - 现有的小鹅养成功能（喂食、喂水、清洁、玩耍）不能受影响
   - 现有用户数据必须完整保留
   - 现有API接口（除创建小鹅外）保持兼容

2. **数据迁移要求**:
   - 现有小鹅数据必须正确迁移为"已归属"状态
   - 数据迁移过程必须可回滚
   - 迁移过程中不能丢失任何数据

3. **前端兼容性**:
   - API接口变更必须提前通知前端团队
   - 提供详细的接口变更文档
   - 保留必要的过渡期

### 风险控制要求

1. **部署风险控制**:
   - 必须先在测试环境完整验证
   - 生产环境部署前必须有完整的回滚方案
   - 关键操作必须有人工确认步骤

2. **数据风险控制**:
   - 数据库变更前必须完整备份
   - 提供详细的数据回滚脚本
   - 数据迁移必须分步骤执行并验证

3. **业务风险控制**:
   - 重构期间不能影响现有用户的正常使用
   - 关键功能必须有降级方案
   - 异常情况必须有应急处理预案

## 🚀 实施步骤详情

### 步骤1: 数据库结构调整 ⏳
**时间安排**: 1个工作日
**开始时间**: ___________
**完成时间**: ___________
**状态**: ⏳ 待开始

#### 1.1 任务清单
- [ ] **新增用户角色表** (新增功能)
  - 创建`user_roles`表
  - 建立用户与角色的关联关系
  - 插入默认管理员角色数据

- [ ] **调整小鹅表结构** (修改现有功能)
  - 添加`ownership_status`字段（归属状态）
  - 添加`purchase_time`字段（购买时间）
  - 修改`user_id`字段为可空（允许待售状态）
  - 更新现有数据的归属状态

- [ ] **新增购买记录表** (新增功能)
  - 创建`goose_purchases`表
  - 建立购买记录的完整追踪机制

#### 1.2 数据库DDL脚本
```sql
-- 用户角色表
CREATE TABLE user_roles (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '角色ID',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    role_name VARCHAR(50) NOT NULL COMMENT '角色名称',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',

    INDEX idx_user_id (user_id),
    INDEX idx_role_name (role_name)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户角色表';

-- 小鹅表结构调整
ALTER TABLE geese
ADD COLUMN ownership_status TINYINT DEFAULT 0 COMMENT '归属状态：0-待售，1-已归属',
ADD COLUMN purchase_time TIMESTAMP NULL COMMENT '购买时间',
MODIFY COLUMN user_id BIGINT NULL COMMENT '所属用户ID（可为空）';

-- 购买记录表
CREATE TABLE goose_purchases (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '购买记录ID',
    user_id BIGINT NOT NULL COMMENT '购买用户ID',
    goose_id BIGINT NOT NULL COMMENT '购买的小鹅ID',
    product_id BIGINT NULL COMMENT '关联商品ID',
    purchase_price DECIMAL(10,2) NOT NULL COMMENT '购买价格',
    purchase_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '购买时间',
    status TINYINT DEFAULT 1 COMMENT '状态：1-成功，0-失败',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',

    INDEX idx_user_id (user_id),
    INDEX idx_goose_id (goose_id),
    INDEX idx_purchase_time (purchase_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='小鹅购买记录表';

-- 数据迁移：更新现有小鹅为已归属状态
UPDATE geese SET
    ownership_status = 1,
    purchase_time = created_at
WHERE user_id IS NOT NULL;

-- 插入默认管理员角色
INSERT INTO user_roles (user_id, role_name) VALUES (1, 'ADMIN');
```

#### 1.3 交付物
- [ ] 数据库迁移脚本文件：`scripts/migration/001_ownership_refactor.sql`
- [ ] 数据回滚脚本文件：`scripts/migration/001_ownership_refactor_rollback.sql`
- [ ] 数据库结构文档更新

#### 1.4 验收标准
- [ ] 所有DDL脚本在测试环境执行成功
- [ ] 现有数据完整性验证通过
- [ ] 新表结构符合设计要求
- [ ] 索引创建正确，查询性能满足要求

#### 1.5 风险评估
- **风险**: 数据迁移可能影响现有数据
- **缓解措施**: 先在测试环境验证，备份生产数据
- **回滚方案**: 提供完整的回滚脚本

---

### 步骤2: 权限管理系统开发 ⏳
**时间安排**: 2个工作日
**开始时间**: ___________
**完成时间**: ___________
**状态**: ⏳ 待开始

#### 2.1 任务清单
- [ ] **创建权限相关实体类** (新增功能)
  - `UserRole`实体类
  - `Role`枚举类
  - 权限相关DTO类

- [ ] **实现权限验证机制** (新增功能)
  - 基于Sa-Token的角色权限验证
  - `@AdminRequired`注解
  - 权限拦截器

- [ ] **权限服务层开发** (新增功能)
  - `RoleService`接口和实现
  - 用户角色管理逻辑
  - 权限验证工具类

#### 2.2 代码文件清单
**新增文件**:
- `src/main/java/com/gooseapp/user/entity/UserRole.java`
- `src/main/java/com/gooseapp/user/enums/Role.java`
- `src/main/java/com/gooseapp/user/dto/UserRoleDTO.java`
- `src/main/java/com/gooseapp/common/annotation/AdminRequired.java`
- `src/main/java/com/gooseapp/common/interceptor/PermissionInterceptor.java`
- `src/main/java/com/gooseapp/user/service/RoleService.java`
- `src/main/java/com/gooseapp/user/service/impl/RoleServiceImpl.java`
- `src/main/java/com/gooseapp/user/repository/UserRoleRepository.java`

**修改文件**:
- `src/main/java/com/gooseapp/common/config/WebConfig.java` (添加权限拦截器)
- `src/main/resources/application.yml` (Sa-Token权限配置)

#### 2.3 测试用例清单
- [ ] `UserRoleServiceTest` - 用户角色服务测试
- [ ] `PermissionInterceptorTest` - 权限拦截器测试
- [ ] `AdminRequiredAnnotationTest` - 管理员权限注解测试

#### 2.4 交付物
- [ ] 权限管理相关实体类
- [ ] 权限验证注解和拦截器
- [ ] 权限服务层代码
- [ ] 单元测试代码（覆盖率≥80%）
- [ ] 权限管理API文档

#### 2.5 验收标准
- [ ] 权限验证机制正常工作
- [ ] 管理员权限注解生效
- [ ] 普通用户无法访问管理员接口
- [ ] 单元测试覆盖率达到80%以上

---

### 步骤3: 管理员功能开发 ⏳
**时间安排**: 2个工作日
**开始时间**: ___________
**完成时间**: ___________
**状态**: ⏳ 待开始

#### 3.1 任务清单
- [ ] **管理员小鹅管理Controller** (新增功能)
  - 创建小鹅到商品池
  - 批量创建小鹅
  - 查看所有小鹅（包括待售和已归属）
  - 小鹅状态管理

- [ ] **管理员小鹅服务层** (新增功能)
  - `AdminGooseService`接口和实现
  - 小鹅创建业务逻辑
  - 商品池管理逻辑

#### 3.2 代码文件清单
**新增文件**:
- `src/main/java/com/gooseapp/admin/controller/AdminGooseController.java`
- `src/main/java/com/gooseapp/admin/service/AdminGooseService.java`
- `src/main/java/com/gooseapp/admin/service/impl/AdminGooseServiceImpl.java`
- `src/main/java/com/gooseapp/admin/dto/AdminGooseCreateRequest.java`
- `src/main/java/com/gooseapp/admin/dto/BatchCreateGooseRequest.java`

**修改文件**:
- `src/main/java/com/gooseapp/goose/service/impl/GooseServiceImpl.java` (移除普通用户创建逻辑)

#### 3.3 API接口设计
```java
@RestController
@RequestMapping("/api/v1/admin/geese")
@AdminRequired
public class AdminGooseController {

    @PostMapping("/create")
    @Operation(summary = "创建小鹅到商品池")
    public ApiResponse<GooseDTO> createGooseForSale(@RequestBody AdminGooseCreateRequest request);

    @PostMapping("/batch-create")
    @Operation(summary = "批量创建小鹅")
    public ApiResponse<List<GooseDTO>> batchCreateGeese(@RequestBody BatchCreateGooseRequest request);

    @GetMapping("/all")
    @Operation(summary = "查看所有小鹅")
    public ApiResponse<Page<GooseDTO>> getAllGeese(@RequestParam(defaultValue = "0") int page,
                                                   @RequestParam(defaultValue = "20") int size);

    @GetMapping("/available")
    @Operation(summary = "查看待售小鹅")
    public ApiResponse<Page<GooseDTO>> getAvailableGeese(@RequestParam(defaultValue = "0") int page,
                                                         @RequestParam(defaultValue = "20") int size);
}
```

#### 3.4 交付物
- [ ] 管理员小鹅管理Controller
- [ ] 管理员小鹅服务层代码
- [ ] 相关DTO和请求响应类
- [ ] 单元测试和集成测试
- [ ] API文档更新

#### 3.5 验收标准
- [ ] 管理员可以成功创建小鹅到商品池
- [ ] 创建的小鹅ownership_status为0（待售）
- [ ] 创建的小鹅user_id为空
- [ ] 批量创建功能正常工作
- [ ] 权限验证正确，普通用户无法访问

---

### 步骤4: 购买系统开发 ⏳
**时间安排**: 3个工作日
**开始时间**: ___________
**完成时间**: ___________
**状态**: ⏳ 待开始

#### 4.1 任务清单
- [ ] **购买服务层开发** (新增功能)
  - `GoosePurchaseService`接口和实现
  - 购买流程业务逻辑
  - 购买记录管理

- [ ] **商城Controller开发** (新增功能)
  - 查看可购买小鹅
  - 执行购买操作
  - 购买历史查询

- [ ] **购买相关实体和DTO** (新增功能)
  - `GoosePurchase`实体类
  - 购买相关DTO类
  - 购买请求响应类

#### 4.2 代码文件清单
**新增文件**:
- `src/main/java/com/gooseapp/shop/controller/ShopController.java`
- `src/main/java/com/gooseapp/shop/service/GoosePurchaseService.java`
- `src/main/java/com/gooseapp/shop/service/impl/GoosePurchaseServiceImpl.java`
- `src/main/java/com/gooseapp/shop/entity/GoosePurchase.java`
- `src/main/java/com/gooseapp/shop/repository/GoosePurchaseRepository.java`
- `src/main/java/com/gooseapp/shop/dto/PurchaseRequest.java`
- `src/main/java/com/gooseapp/shop/dto/PurchaseResultDTO.java`
- `src/main/java/com/gooseapp/shop/dto/PurchaseHistoryDTO.java`

#### 4.3 购买流程设计
```java
@Transactional
public PurchaseResultDTO purchaseGoose(Long userId, Long gooseId) {
    // 1. 验证小鹅可购买状态（ownership_status = 0）
    // 2. 验证用户状态和权限
    // 3. 创建购买记录
    // 4. 更新小鹅归属（user_id = userId, ownership_status = 1）
    // 5. 更新用户统计数据
    // 6. 返回购买结果
}
```

#### 4.4 交付物
- [ ] 购买服务层完整代码
- [ ] 商城Controller和相关接口
- [ ] 购买相关实体和DTO
- [ ] 购买流程单元测试
- [ ] 购买接口集成测试
- [ ] API文档更新

#### 4.5 验收标准
- [ ] 用户可以查看待售小鹅列表
- [ ] 购买流程完整且事务安全
- [ ] 购买成功后小鹅归属正确变更
- [ ] 购买记录正确保存
- [ ] 购买历史查询功能正常

---

### 步骤5: 用户接口调整 ⏳
**时间安排**: 1个工作日
**开始时间**: ___________
**完成时间**: ___________
**状态**: ⏳ 待开始

#### 5.1 任务清单
- [ ] **移除用户创建小鹅接口** (修改现有功能)
  - 删除`POST /api/v1/geese`接口
  - 移除`GooseService.createGoose()`方法
  - 更新相关测试用例

- [ ] **调整小鹅查询接口** (修改现有功能)
  - 只返回用户已归属的小鹅
  - 添加购买时间等新字段
  - 更新小鹅详情接口

- [ ] **新增购买历史接口** (新增功能)
  - 用户购买历史查询
  - 购买记录详情查询

#### 5.2 修改文件清单
**修改文件**:
- `src/main/java/com/gooseapp/goose/controller/GooseController.java`
- `src/main/java/com/gooseapp/goose/service/GooseService.java`
- `src/main/java/com/gooseapp/goose/service/impl/GooseServiceImpl.java`
- `src/main/java/com/gooseapp/goose/dto/GooseDTO.java`
- `src/main/java/com/gooseapp/goose/dto/GooseDetailDTO.java`

**删除文件**:
- `src/main/java/com/gooseapp/goose/dto/CreateGooseRequest.java`

#### 5.3 交付物
- [ ] 调整后的用户小鹅接口
- [ ] 更新的DTO类
- [ ] 修改后的测试用例
- [ ] 更新的API文档

#### 5.4 验收标准
- [ ] 普通用户无法创建小鹅
- [ ] 用户只能查看自己已归属的小鹅
- [ ] 小鹅详情包含购买信息
- [ ] 购买历史查询功能正常

---

### 步骤6: 集成测试和文档更新 ⏳
**时间安排**: 1个工作日
**开始时间**: ___________
**完成时间**: ___________
**状态**: ⏳ 待开始

#### 6.1 任务清单
- [ ] **完整流程集成测试** (新增功能)
  - 管理员创建小鹅流程测试
  - 用户购买小鹅流程测试
  - 权限验证集成测试

- [ ] **文档更新** (修改现有功能)
  - 更新API文档
  - 更新测试指南
  - 更新开发进度文档

- [ ] **性能和安全测试** (新增功能)
  - 购买流程并发测试
  - 权限绕过安全测试

#### 6.2 测试用例清单
- [ ] `OwnershipRefactorIntegrationTest` - 完整业务流程集成测试
- [ ] `AdminGooseManagementE2ETest` - 管理员功能端到端测试
- [ ] `GoosePurchaseE2ETest` - 购买流程端到端测试
- [ ] `PermissionSecurityTest` - 权限安全测试

#### 6.3 交付物
- [ ] 完整的集成测试套件
- [ ] 更新的API文档
- [ ] 更新的测试指南
- [ ] 更新的开发进度文档
- [ ] 性能测试报告

#### 6.4 验收标准
- [ ] 所有集成测试通过
- [ ] 整体测试覆盖率≥80%
- [ ] API文档与实际接口一致
- [ ] 业务流程完整且正确

## 📊 里程碑检查点

### 里程碑1: 数据库结构调整完成 ⏳
**检查时间**: 第1天结束
**检查内容**:
- [ ] 数据库结构调整完成
- [ ] 数据迁移验证通过
- [ ] 回滚方案验证通过

### 里程碑2: 权限管理系统完成 ⏳
**检查时间**: 第3天结束
**检查内容**:
- [ ] 权限验证机制正常工作
- [ ] 管理员权限注解生效
- [ ] 权限相关测试通过

### 里程碑3: 核心功能开发完成 ⏳
**检查时间**: 第7天结束
**检查内容**:
- [ ] 管理员创建小鹅功能完成
- [ ] 用户购买小鹅功能完成
- [ ] 核心业务逻辑测试通过

### 里程碑4: 项目完成 ⏳
**检查时间**: 第10天结束
**检查内容**:
- [ ] 所有功能开发完成
- [ ] 集成测试全部通过
- [ ] 文档更新完成
- [ ] 业务逻辑验证通过

## ⚠️ 风险评估和缓解措施

### 高风险项
1. **数据库结构变更风险**
   - **风险**: 可能影响现有数据完整性
   - **缓解**: 充分的测试环境验证，完整的备份和回滚方案
   - **应急预案**: 立即回滚到变更前状态

2. **权限系统引入风险**
   - **风险**: 可能影响现有用户访问
   - **缓解**: 渐进式部署，保持向后兼容
   - **应急预案**: 临时关闭权限验证

### 中风险项
1. **API接口变更风险**
   - **风险**: 前端调用可能受影响
   - **缓解**: 提前通知前端团队，提供迁移指南
   - **应急预案**: 保留旧接口一段时间

2. **业务逻辑复杂性风险**
   - **风险**: 购买流程可能存在边界情况
   - **缓解**: 充分的单元测试和集成测试
   - **应急预案**: 快速修复和补丁发布

## 🔄 回滚方案

### 数据库回滚
```sql
-- 回滚小鹅表结构
ALTER TABLE geese
DROP COLUMN ownership_status,
DROP COLUMN purchase_time,
MODIFY COLUMN user_id BIGINT NOT NULL;

-- 删除新增表
DROP TABLE IF EXISTS goose_purchases;
DROP TABLE IF EXISTS user_roles;
```

### 代码回滚
- 恢复原有的`GooseController.createGoose`接口
- 移除所有权限验证相关代码
- 恢复原有的业务逻辑

## 📈 质量保证措施

### 测试覆盖率要求
- **单元测试覆盖率**: ≥80%
- **集成测试覆盖率**: ≥70%
- **关键业务流程覆盖率**: 100%

### 代码审查标准
- [ ] 所有新增代码必须经过代码审查
- [ ] 遵循项目编码规范
- [ ] 包含充分的注释和文档
- [ ] 通过静态代码分析

### 测试验证标准
- [ ] 所有单元测试必须通过
- [ ] 所有集成测试必须通过
- [ ] 性能测试满足要求
- [ ] 安全测试通过

## 📝 进度跟踪

### 每日进度更新
请在每个工作日结束时更新以下内容：
- 当日完成的任务
- 遇到的问题和解决方案
- 明日计划
- 风险和阻塞项

### development-progress.md更新要求
每个步骤完成后，必须更新`docs/development-progress.md`文件：
- 更新模块完成状态
- 添加新功能描述
- 更新测试覆盖情况
- 记录重要变更

## ✅ 最终验收清单

### 功能验收
- [ ] 管理员可以创建小鹅到商品池
- [ ] 普通用户无法创建小鹅
- [ ] 用户可以购买待售小鹅
- [ ] 购买成功后小鹅归属正确变更
- [ ] 购买记录完整保存
- [ ] 权限验证正确工作

### 技术验收
- [ ] 所有测试用例通过
- [ ] 测试覆盖率达标
- [ ] 代码质量符合标准
- [ ] API文档完整准确
- [ ] 数据库结构合理

### 业务验收
- [ ] 核心业务逻辑正确实现
- [ ] 用户体验流畅
- [ ] 管理员功能完善
- [ ] 数据安全可靠

## 📋 详细实施指导

### 代码示例

#### UserRole实体类示例
```java
@Data
@Entity
@Table(name = "user_roles")
public class UserRole {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "user_id", nullable = false)
    private Long userId;

    @Enumerated(EnumType.STRING)
    @Column(name = "role_name", nullable = false)
    private Role roleName;

    @CreatedDate
    @Column(name = "created_at")
    private LocalDateTime createdAt;

    @LastModifiedDate
    @Column(name = "updated_at")
    private LocalDateTime updatedAt;
}
```

#### AdminRequired注解示例
```java
@Target({ElementType.METHOD, ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface AdminRequired {
    String message() default "需要管理员权限";
}
```

#### 购买服务核心逻辑示例
```java
@Service
@Transactional
public class GoosePurchaseServiceImpl implements GoosePurchaseService {

    @Override
    public PurchaseResultDTO purchaseGoose(Long userId, Long gooseId) {
        // 1. 验证小鹅状态
        Goose goose = gooseRepository.findById(gooseId)
            .orElseThrow(() -> new BusinessException(ErrorCode.GOOSE_NOT_FOUND));

        if (goose.getOwnershipStatus() != 0) {
            throw new BusinessException(ErrorCode.GOOSE_NOT_AVAILABLE);
        }

        // 2. 创建购买记录
        GoosePurchase purchase = new GoosePurchase();
        purchase.setUserId(userId);
        purchase.setGooseId(gooseId);
        purchase.setPurchasePrice(getGoosePrice(goose));
        purchase.setStatus(1);

        GoosePurchase savedPurchase = purchaseRepository.save(purchase);

        // 3. 更新小鹅归属
        goose.setUserId(userId);
        goose.setOwnershipStatus(1);
        goose.setPurchaseTime(LocalDateTime.now());
        gooseRepository.save(goose);

        // 4. 更新用户统计
        updateUserGooseCount(userId);

        return convertToPurchaseResultDTO(savedPurchase, goose);
    }
}
```

### 测试用例示例

#### 购买流程集成测试
```java
@SpringBootTest
@Transactional
class GoosePurchaseIntegrationTest {

    @Test
    void testPurchaseGoose_Success() {
        // Given
        Long adminUserId = 1L;
        Long customerUserId = 2L;

        // 管理员创建小鹅
        GooseDTO createdGoose = adminGooseService.createGooseForSale(
            adminUserId, "测试小鹅", "白鹅", 1);

        // When
        PurchaseResultDTO result = purchaseService.purchaseGoose(
            customerUserId, createdGoose.getId());

        // Then
        assertThat(result.isSuccess()).isTrue();
        assertThat(result.getGoose().getUserId()).isEqualTo(customerUserId);

        // 验证小鹅状态变更
        Goose updatedGoose = gooseRepository.findById(createdGoose.getId()).get();
        assertThat(updatedGoose.getOwnershipStatus()).isEqualTo(1);
        assertThat(updatedGoose.getUserId()).isEqualTo(customerUserId);
        assertThat(updatedGoose.getPurchaseTime()).isNotNull();
    }
}
```

### API测试脚本

#### 管理员创建小鹅测试
```bash
# 1. 管理员登录
curl -X POST "http://localhost:8080/api/v1/auth/login" \
  -H "Content-Type: application/json" \
  -d '{
    "username": "admin",
    "password": "admin123"
  }'

# 2. 创建小鹅到商品池
curl -X POST "http://localhost:8080/api/v1/admin/geese/create" \
  -H "Content-Type: application/json" \
  -H "satoken: Bearer ADMIN_TOKEN" \
  -d '{
    "name": "小白",
    "breed": "白鹅",
    "gender": 1,
    "initialHealth": 100
  }'

# 3. 查看待售小鹅
curl -X GET "http://localhost:8080/api/v1/shop/geese" \
  -H "satoken: Bearer USER_TOKEN"

# 4. 购买小鹅
curl -X POST "http://localhost:8080/api/v1/shop/purchase/1" \
  -H "Content-Type: application/json" \
  -H "satoken: Bearer USER_TOKEN" \
  -d '{
    "paymentMethod": "WECHAT_PAY"
  }'
```

## 🔍 关键验证点

### 数据一致性验证
```sql
-- 验证小鹅归属状态一致性
SELECT
    g.id,
    g.user_id,
    g.ownership_status,
    g.purchase_time,
    p.user_id as purchase_user_id,
    p.purchase_time as purchase_record_time
FROM geese g
LEFT JOIN goose_purchases p ON g.id = p.goose_id
WHERE g.ownership_status = 1;

-- 验证待售小鹅状态
SELECT COUNT(*) as available_geese_count
FROM geese
WHERE ownership_status = 0 AND user_id IS NULL;
```

### 权限验证测试
```java
@Test
void testUserCannotCreateGoose() {
    // 普通用户尝试创建小鹅应该失败
    assertThrows(AccessDeniedException.class, () -> {
        gooseService.createGoose(normalUserId, "小鹅", "白鹅", 1);
    });
}

@Test
void testUserCannotAccessAdminEndpoints() {
    // 普通用户访问管理员接口应该返回403
    mockMvc.perform(post("/api/v1/admin/geese/create")
            .header("satoken", "Bearer " + userToken)
            .contentType(MediaType.APPLICATION_JSON)
            .content(createGooseJson))
            .andExpect(status().isForbidden());
}
```

## 📊 性能监控指标

### 关键性能指标
- **购买接口响应时间**: < 500ms
- **小鹅列表查询响应时间**: < 200ms
- **并发购买处理能力**: > 100 TPS
- **数据库连接池使用率**: < 80%

### 监控SQL语句
```sql
-- 监控购买操作性能
EXPLAIN SELECT * FROM geese WHERE ownership_status = 0 AND id = ?;

-- 监控用户小鹅查询性能
EXPLAIN SELECT * FROM geese WHERE user_id = ? AND ownership_status = 1;

-- 监控购买记录查询性能
EXPLAIN SELECT * FROM goose_purchases WHERE user_id = ? ORDER BY purchase_time DESC;
```

## 🚨 应急处理预案

### 数据回滚紧急预案
```bash
# 1. 立即停止应用服务
sudo systemctl stop goose-app

# 2. 备份当前数据
mysqldump -u root -p goose_app > backup_before_rollback_$(date +%Y%m%d_%H%M%S).sql

# 3. 执行回滚脚本
mysql -u root -p goose_app < scripts/migration/001_ownership_refactor_rollback.sql

# 4. 恢复原有代码版本
git checkout previous_stable_version

# 5. 重启服务
sudo systemctl start goose-app
```

### 权限系统故障预案
```java
// 临时关闭权限验证的配置
@Configuration
public class EmergencyConfig {

    @Value("${app.emergency.disable-permission:false}")
    private boolean disablePermission;

    @Bean
    @ConditionalOnProperty(name = "app.emergency.disable-permission", havingValue = "true")
    public PermissionInterceptor emergencyPermissionInterceptor() {
        return new PermissionInterceptor() {
            @Override
            public boolean preHandle(HttpServletRequest request,
                                   HttpServletResponse response,
                                   Object handler) {
                return true; // 临时放行所有请求
            }
        };
    }
}
```

## 📋 最终检查清单

### 代码质量检查
- [ ] 所有新增代码通过SonarQube扫描
- [ ] 代码覆盖率报告生成并达标
- [ ] 所有TODO和FIXME已处理
- [ ] 代码注释完整且准确
- [ ] 遵循项目编码规范

### 功能完整性检查
- [ ] 管理员可以创建小鹅（✅核心需求）
- [ ] 普通用户无法创建小鹅（✅核心需求）
- [ ] 用户可以购买小鹅（✅核心需求）
- [ ] 购买后归属正确变更（✅核心需求）
- [ ] 所有原有功能正常工作
- [ ] 新增功能符合设计要求

### 数据安全检查
- [ ] 敏感操作有事务保护
- [ ] 数据完整性约束正确
- [ ] 权限验证无漏洞
- [ ] SQL注入防护到位
- [ ] 数据备份机制完善

### 文档完整性检查
- [ ] API文档与实际接口一致
- [ ] 数据库文档更新完整
- [ ] 部署文档包含新增配置
- [ ] 测试文档覆盖所有场景
- [ ] 用户手册更新完成

---

**文档版本**: v1.0
**创建时间**: 2024-12-19
**最后更新**: 2024-12-19
**更新人**: 全栈开发工程师

> **重要提醒**: 本重构计划涉及核心业务逻辑变更，请严格按照步骤执行，确保每个里程碑的验收标准都得到满足后再进行下一步。如遇到任何问题，请立即停止并寻求技术支持。
