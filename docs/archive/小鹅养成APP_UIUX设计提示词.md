# 🦆 小鹅养成APP - UIUX设计提示词集

## 📋 设计提示词文档信息

**项目名称：** 小鹅萌宠APP  
**设计版本：** v1.0  
**适用场景：** React Native移动应用UI/UX设计  
**目标平台：** iOS & Android  
**设计理念：** 情感化、直观易用、社交友好、沉浸体验  

---

## 🎯 核心设计理念提示词

### 🌟 **整体设计哲学**

```
设计一个温暖而富有生命力的小鹅养成应用，体现以下核心价值：

1. **情感连接优先**：每个设计元素都要强化用户与小鹅的情感纽带
2. **直观交互体验**：30秒内让新用户理解并爱上这个产品
3. **自然社交传播**：让分享成为一种自然而愉悦的行为
4. **真实沉浸感受**：通过设计让虚拟养成具备真实陪伴感

设计语言要体现：温暖、可爱、可信、专业、有趣
避免：冷漠、复杂、商业化过重、孩子气过度
```

### 🎨 **视觉风格定位**

```
创建一套清新温暖的视觉风格系统：

**色彩情感表达：**
- 主色调：温暖的小鹅黄(#FFD93D) - 传递快乐和活力
- 辅助色：生机绿(#7CB342) - 象征自然和健康成长
- 点缀色：天空蓝(#42A5F5) - 营造清新和信任感
- 状态色：采用柔和渐变而非纯色，体现生命的层次感

**视觉质感要求：**
- 圆润而非尖锐：所有元素使用圆角设计，传递温和友善
- 柔和而非刺眼：避免高对比度，使用柔和的阴影和渐变
- 生动而非平板：适度使用微动画，让界面具有生命感
- 现代而非陈旧：清晰的层次结构，充足的留白空间
```

---

## 🏠 **主要界面设计提示词**

### 1. 🔍 **发现页设计**

```
设计一个引人入胜的内容发现界面：

**布局结构：**
- 顶部：简洁的搜索栏 + 消息图标，背景使用品牌渐变
- 轮播区：展示精选小鹅故事，使用卡片式设计，支持水平滑动
- 分类导航：快速筛选按钮，使用胶囊形状，选中状态有明显变化
- 内容流：瀑布流或网格布局，展示热门动态和推荐内容

**交互细节：**
- 轮播卡片：轻微的缩放动画，圆角阴影，吸引点击
- 分类按钮：点击时有弹性动画，配以触觉反馈
- 内容卡片：长按预览，点击平滑过渡到详情
- 下拉刷新：使用小鹅主题的loading动画

**视觉要点：**
- 内容密度适中，避免信息过载
- 使用品牌色彩系统，保持一致性
- 图片处理：圆角、适度的模糊背景
- 文字层级：清晰的标题-副标题-正文层次
```

### 2. 🛒 **商城页设计**

```
打造一个温馨而高效的购物体验：

**商品展示策略：**
- 商品卡片：大图展示，展现小鹅的可爱特征
- 价格标签：醒目但不突兀，使用品牌色彩
- 状态标识：健康状况用小图标表示，直观易懂
- 分类筛选：顶部固定，快速切换不同品种和价格

**购买流程优化：**
- 一键购买：减少步骤，但保持必要的确认
- 购物车：悬浮球设计，显示数量，轻松查看
- 支付界面：简洁明了，安全感强，支持微信支付
- 成功确认：温暖的祝贺动画，立即引导到养成页

**情感化元素：**
- 商品图片：展现小鹅的灵动感，非冷冰冰的商品照
- 文案语调：拟人化表达，如"我想要一个家"
- 互动反馈：购买成功后播放愉快的音效
- 关怀提醒：新手引导，帮助了解养成要点
```

### 3. 🦆 **我的小鹅页设计**

```
创建最核心的养成互动体验：

**小鹅展示方案：**
- 卡片布局：每只小鹅独立卡片，展示基本状态
- 状态可视化：使用进度条或环形图显示健康、饥饿等状态
- 快速操作：卡片上直接提供喂食、清洁快捷按钮
- 视频窗口：嵌入实时视频流，支持点击全屏观看

**互动设计重点：**
- 触觉反馈：每次互动都有震动和音效反馈
- 动画效果：喂食时的粒子效果，清洁时的光圈动画
- 状态变化：实时更新状态条，配以缓动动画
- 操作冷却：倒计时显示，防止过度操作，保护小鹅

**情感化细节：**
- 个性头像：每只小鹅的独特头像和名字
- 成长记录：时间轴展示成长历程
- 互动历史：记录每次喂食和关怀的时间
- 情感表达：小鹅的表情根据状态变化
```

### 4. 💝 **转赠功能设计**

```
设计一个令人感动的转赠体验：

**转赠流程设计：**
- 选择界面：展示所有可转赠的小鹅，支持多选
- 祝福编辑：提供模板祝福语，支持自定义编辑
- 分享预览：实时预览分享卡片效果
- 发送确认：温馨的确认提示，强调转赠的意义

**分享卡片设计：**
- 视觉元素：小鹅照片 + 祝福文字 + 品牌标识
- 设计风格：温暖的色彩，圆润的卡片边角
- 信息层级：清晰的标题-描述-操作层次
- 个性化：根据小鹅品种自动匹配背景色彩

**接收体验优化：**
- 深度链接：无缝跳转到APP，自动登录
- 接收动画：温馨的接收确认动画
- 感谢反馈：向送礼者发送接收通知
- 新手引导：帮助新用户快速上手养成
```

### 5. 🚀 **启动页/欢迎页设计**

```
设计温暖而专业的首次体验：

**启动界面设计：**
- 品牌Logo：居中展示，配以品牌渐变背景
- 加载动画：小鹅孵化主题的加载动效
- 版本信息：底部显示版本号和版权信息
- 启动时长：控制在2-3秒，避免过长等待

**引导页设计：**
- 页面数量：3-4页介绍核心功能
- 视觉风格：插画风格，温暖色调
- 内容结构：大图 + 简洁标题 + 关键描述
- 交互方式：滑动切换，支持跳过功能

**页面内容策划：**
- 第1页：小鹅养成概念介绍"真实的云养宠体验"
- 第2页：转赠功能介绍"爱的传递，情感的纽带"
- 第3页：实时监控介绍"24小时陪伴，随时关怀"
- 第4页：立即开始按钮，引导注册登录

**情感化元素：**
- 温馨插画：展现人与小鹅的情感连接
- 动效细节：页面切换时的小鹅飞行动画
- 文案语调：温暖而简洁，避免冗长说教
- 视觉一致性：与APP整体设计风格保持一致
```

### 6. 🔐 **登录注册页设计**

```
创建便捷安全的认证体验：

**登录界面布局：**
- 背景设计：品牌渐变 + 轻微的小鹅元素装饰
- Logo位置：顶部居中，适中大小
- 登录选项：微信登录优先，手机号登录备选
- 协议链接：底部显示用户协议和隐私政策

**微信登录设计：**
- 主按钮：绿色背景，微信图标 + "微信一键登录"
- 说明文字：简洁说明微信登录的便利性
- 授权提示：清晰说明所需的权限和用途
- 快速响应：点击后即时跳转微信授权

**手机号登录设计：**
- 输入框设计：圆角边框，聚焦时高亮
- 验证码：图片验证码 + 发送按钮
- 倒计时显示：发送后的倒计时状态
- 错误提示：友好的错误信息显示

**安全和信任元素：**
- 安全认证：显示安全认证标识
- 隐私说明：简洁的隐私保护说明
- 客服入口：遇到问题时的联系方式
- 免责声明：合规性的免责声明
```

### 7. 📱 **小鹅详情页设计**

```
打造最核心的养成互动体验：

**页面结构设计：**
- 顶部信息栏：小鹅名称 + 编码 + 更多操作
- 视频监控区：占屏幕40%，支持全屏切换
- 状态展示区：四个状态的可视化展示
- 互动操作区：喂食、喂水、清洁三个主要操作
- 底部功能区：拍照、转赠、数据、分享等

**视频监控界面：**
- 播放控制：播放/暂停、音量、全屏按钮
- 状态指示：直播状态、观看人数显示
- 网络适配：根据网络状况自动调节清晰度
- 截图功能：一键截图并保存到相册

**状态可视化设计：**
- 进度条设计：圆角进度条，不同状态使用不同颜色
- 数值显示：百分比 + 状态描述（如"很健康"）
- 趋势箭头：显示状态的变化趋势（上升/下降）
- 预警提示：低于阈值时的醒目提醒

**互动操作优化：**
- 按钮设计：大尺寸，易点击，配以图标和文字
- 冷却机制：清晰的冷却时间显示和倒计时
- 操作反馈：点击后的动画效果和状态更新
- 操作历史：显示最近的互动记录
```

### 8. 📺 **实时监控页设计**

```
提供沉浸式的监控体验：

**全屏监控界面：**
- 沉浸式设计：隐藏多余UI，专注视频内容
- 手势控制：双击全屏、滑动调节音量和亮度
- 状态叠加：视频上叠加小鹅基本状态信息
- 快速操作：屏幕边缘的快速互动按钮

**监控功能面板：**
- 播放控制：播放、暂停、重新连接
- 清晰度选择：自动、高清、标清选项
- 声音控制：音量调节、静音开关
- 录制功能：短视频录制和分享

**多视角支持：**
- 摄像头切换：如有多个摄像头支持切换
- 视角标识：清晰标识当前视角位置
- 历史回看：支持查看历史录像片段
- 智能推荐：推荐精彩时刻的视频片段

**环境信息显示：**
- 实时数据：温度、湿度、光照等环境参数
- 数据图表：简单的趋势图表显示
- 异常警报：环境异常时的提醒通知
- 地理位置：显示养殖场的地理位置信息
```

### 9. 🌟 **动态/社区页设计**

```
构建活跃的社交互动社区：

**动态流布局：**
- 时间线设计：垂直滚动的动态时间线
- 卡片结构：用户头像 + 内容 + 互动按钮
- 内容类型：文字、图片、视频、直播截图混合
- 加载策略：无限滚动 + 智能预加载

**内容展示优化：**
- 图片处理：统一尺寸，圆角设计，支持多图查看
- 视频播放：自动播放（静音）+ 点击全屏
- 文字排版：清晰的字体层级，适度的行间距
- 时间标记：相对时间显示（如"2分钟前"）

**互动功能设计：**
- 点赞动画：心形动画效果，数量实时更新
- 评论系统：支持文字、表情、@功能
- 分享功能：转发到微信、复制链接等
- 举报机制：长按内容显示举报选项

**发布动态界面：**
- 编辑器：富文本编辑，支持图片、视频、表情
- 标签功能：话题标签、位置标签
- 隐私设置：公开、仅好友、仅自己可见
- 发布预览：发布前的内容预览确认
```

### 10. 👤 **个人中心页设计**

```
打造个性化的用户管理中心：

**个人信息区域：**
- 背景设计：渐变背景 + 个性化装饰
- 头像展示：圆形头像，支持点击放大编辑
- 基本信息：昵称、个性签名、等级标识
- 数据概览：关注数、粉丝数、获赞数

**功能菜单设计：**
- 分组布局：将功能按类型分组显示
- 图标设计：清晰的功能图标，保持视觉一致性
- 红点提示：有未读消息或待处理事项时显示
- 快速访问：常用功能的优先级排序

**核心功能列表：**
- 我的订单：订单状态、历史订单、售后服务
- 我的小鹅：快速跳转到小鹅列表
- 转赠记录：发出和收到的转赠历史
- 积分中心：积分余额、获取记录、兑换商城
- 邀请好友：推广码、邀请奖励、好友列表
- 设置中心：账号设置、隐私设置、通知设置

**个性化元素：**
- 等级系统：用户等级、成长进度、特权说明
- 成就徽章：获得的成就徽章展示
- 个性装饰：背景主题、头像边框等个性化装饰
- 活动入口：当前参与的活动和任务
```

### 11. ⚙️ **设置页面设计**

```
提供完善的个性化设置选项：

**账户设置区域：**
- 个人信息：头像、昵称、个性签名编辑
- 账户安全：密码修改、手机号绑定、实名认证
- 隐私设置：动态可见性、搜索权限、黑名单管理
- 注销账户：账户注销流程和风险提示

**通知设置优化：**
- 推送总开关：是否接收APP推送通知
- 分类设置：小鹅状态、系统消息、社交互动分别设置
- 免打扰时间：设置免打扰的时间段
- 微信通知：是否接收微信服务号消息

**应用设置功能：**
- 语言选择：多语言支持（简中、繁中、英文）
- 主题设置：日间模式、夜间模式、跟随系统
- 视频设置：默认清晰度、自动播放、流量保护
- 缓存管理：缓存清理、存储空间查看

**其他设置选项：**
- 意见反馈：问题反馈、建议提交、满意度评价
- 关于我们：APP版本、团队介绍、联系方式
- 用户协议：服务条款、隐私政策、更新日志
- 检查更新：版本检查、更新下载、更新说明
```

### 12. 📨 **消息通知页设计**

```
设计高效的消息管理系统：

**消息分类设计：**
- 系统消息：APP更新、活动通知、重要公告
- 小鹅通知：状态变化、成长节点、需要关怀
- 社交消息：点赞、评论、关注、私信
- 转赠消息：转赠接收、转赠成功、权益变更

**消息列表界面：**
- 卡片设计：每条消息独立卡片，清晰分隔
- 图标标识：不同类型消息使用不同图标
- 时间显示：相对时间或绝对时间，保持一致
- 读取状态：未读消息的醒目标识

**消息详情页面：**
- 完整内容：消息的完整内容展示
- 相关操作：根据消息类型提供相应操作
- 跳转链接：支持跳转到相关页面
- 历史记录：相同类型消息的历史记录

**通知管理功能：**
- 全部已读：一键标记所有消息为已读
- 分类筛选：按消息类型筛选显示
- 批量操作：批量删除、批量已读
- 设置入口：快速跳转到通知设置页面
```

### 13. 📦 **订单页面设计**

```
提供清晰的订单管理体验：

**订单状态分类：**
- 全部订单：所有订单的综合列表
- 待支付：未完成支付的订单，突出支付按钮
- 已支付：支付完成，等待发货的订单
- 已完成：交易完成的订单历史

**订单卡片设计：**
- 商品信息：小鹅照片、品种、编码、价格
- 订单状态：醒目的状态标识和进度指示
- 时间信息：下单时间、支付时间、完成时间
- 操作按钮：取消订单、立即支付、查看详情

**订单详情页面：**
- 商品详情：完整的商品信息和规格
- 价格明细：商品价格、优惠折扣、实付金额
- 物流信息：如果涉及实物配送的物流跟踪
- 联系客服：订单问题的客服联系入口

**特殊功能设计：**
- 订单搜索：根据订单号、商品名称搜索
- 筛选功能：按时间、状态、价格筛选
- 发票申请：支持电子发票申请和下载
- 订单分享：订单信息的分享功能
```

### 14. 💰 **支付页面设计**

```
打造安全便捷的支付体验：

**支付信息确认：**
- 商品信息：清晰的商品名称、图片、价格
- 价格明细：原价、优惠、实付金额的详细分解
- 安全提示：支付安全认证标识和说明
- 协议确认：支付协议和服务条款的勾选

**支付方式选择：**
- 微信支付：优先推荐，突出显示
- 支付宝：备选支付方式
- 苹果支付：iOS设备支持
- 银行卡：传统银行卡支付选项

**支付界面设计：**
- 简洁布局：去除干扰元素，专注支付流程
- 安全标识：SSL加密、支付安全认证
- 倒计时：支付时限的倒计时显示
- 取消支付：明确的取消支付选项

**支付结果页面：**
- 支付成功：庆祝动画、成功提示、下一步引导
- 支付失败：失败原因、重新支付、联系客服
- 支付中：支付处理中的等待状态
- 收据信息：支付凭证、交易号、时间等信息
```

### 15. 🔍 **搜索页面设计**

```
设计智能高效的搜索体验：

**搜索输入界面：**
- 搜索框设计：大尺寸搜索框，占据顶部主要位置
- 搜索建议：实时搜索建议，支持自动补全
- 搜索历史：最近搜索记录，支持一键删除
- 热门搜索：当前热门搜索关键词推荐

**搜索结果展示：**
- 综合结果：小鹅、用户、动态的混合搜索结果
- 分类筛选：按内容类型分类显示结果
- 排序选择：相关度、时间、热度等排序方式
- 结果统计：搜索结果数量和耗时显示

**搜索优化功能：**
- 语音搜索：支持语音输入搜索
- 扫码搜索：扫描二维码或条码搜索
- 图片搜索：上传图片进行相似搜索
- 高级搜索：更多筛选条件的高级搜索

**无结果状态：**
- 友好提示：无搜索结果时的友好提示
- 搜索建议：推荐相关的搜索关键词
- 热门推荐：推荐当前热门内容
- 反馈机制：搜索问题的反馈入口
```

### 16. 🎧 **客服帮助页设计**

```
提供贴心的客户服务体验：

**帮助中心首页：**
- 常见问题：分类整理的常见问题解答
- 快速入口：常用功能的快速操作入口
- 搜索功能：帮助内容的搜索查找
- 联系客服：多种客服联系方式

**在线客服界面：**
- 聊天窗口：类似微信的聊天界面设计
- 快速回复：常见问题的快速回复选项
- 图片发送：支持发送截图或照片
- 问题分类：自动分类用户问题类型

**FAQ设计：**
- 问题分类：按功能模块分类整理
- 搜索功能：FAQ内容的快速搜索
- 问题反馈：FAQ是否有帮助的反馈
- 相关推荐：相关问题的智能推荐

**联系方式多样化：**
- 在线客服：即时聊天客服
- 电话客服：客服热线电话
- 邮件客服：邮件反馈和回复
- 微信客服：微信群或微信号联系

**问题反馈系统：**
- 问题描述：详细的问题描述输入
- 问题分类：自动或手动选择问题类型
- 截图上传：支持上传问题截图
- 进度跟踪：问题处理进度的跟踪查看
```

---

## 🚀 **全页面交互流程设计提示词**

### 🔄 **页面间导航设计**

```
设计流畅的页面跳转体验：

**底部导航栏：**
- 五个主Tab：首页、商城、我的小鹅、动态、我的
- 选中状态：颜色变化 + 图标填充 + 轻微放大
- 徽章提示：消息数量、待处理事项的数字提示
- 动画效果：切换时的平滑过渡动画

**顶部导航设计：**
- 返回按钮：统一的返回箭头，支持手势返回
- 页面标题：居中显示，简洁明了
- 右侧操作：分享、设置、搜索等功能按钮
- 状态栏：适配不同设备的状态栏样式

**页面跳转动画：**
- 前进动画：新页面从右侧滑入
- 后退动画：当前页面向右滑出
- 弹窗动画：从中心放大，带透明度变化
- 加载动画：页面加载时的占位动画

**深度链接处理：**
- 微信分享：点击分享链接自动跳转对应页面
- 推送通知：点击通知跳转到相关内容页面
- 二维码：扫码后的页面跳转处理
- URL Scheme：支持外部APP调用
```

### 📱 **手势交互设计**

```
设计直观的手势操作：

**基础手势：**
- 点击：标准的点击反馈，按钮缩放 + 颜色变化
- 长按：长按0.5秒触发，震动反馈 + 菜单弹出
- 滑动：列表滑动、页面切换、抽屉拉出
- 双击：图片放大、视频全屏、快速点赞

**特殊手势：**
- 下拉刷新：小鹅主题的下拉刷新动画
- 上拉加载：底部加载更多的指示器
- 左滑删除：消息、历史记录的左滑删除
- 右滑返回：全局的右滑返回上一页

**手势反馈：**
- 触觉反馈：重要操作的震动反馈
- 视觉反馈：手势操作的视觉指示
- 音效反馈：操作成功的音效提示
- 边界反馈：到达边界的弹性动画

**手势冲突处理：**
- 优先级定义：不同手势的优先级设定
- 冲突解决：手势冲突时的处理策略
- 用户教育：复杂手势的操作引导
- 降级处理：手势不可用时的按钮替代
```

---

## 🎨 **视觉设计细节提示词**

### 🌈 **色彩运用指南**

```
建立情感化的色彩系统：

**主色彩应用：**
- 小鹅黄(#FFD93D)：主要按钮、重要标识、品牌元素
- 草地绿(#7CB342)：健康状态、成功反馈、自然元素
- 天空蓝(#42A5F5)：链接文字、信息提示、清洁状态
- 暖橙色(#FF8A65)：互动元素、警告提示、活跃状态

**渐变色运用：**
- 背景渐变：subtle的双色渐变，营造空间感
- 按钮渐变：增加按钮的立体感和点击欲望
- 状态渐变：健康、饥饿等状态用渐变表示变化
- 卡片渐变：轻微的渐变增加卡片的层次感

**色彩情感映射：**
- 温暖色系：传递关爱、温馨、舒适的感受
- 清新色系：体现自然、健康、纯净的理念
- 避免冷色：减少冰冷、距离感的色彩使用
- 色彩平衡：保持整体色调的和谐统一
```

### ✍️ **字体与排版**

```
创建易读而有特色的文字系统：

**字体层级设计：**
- H1大标题(32px)：页面主标题，使用品牌色彩
- H2副标题(24px)：章节标题，较深的灰色
- H3小标题(20px)：卡片标题，中等灰色
- 正文(16px)：主要内容，深灰色，行高1.4
- 辅助文字(14px)：说明文字，浅灰色

**排版原则：**
- 行高控制：确保文字呼吸感，避免拥挤
- 段落间距：适当的段落间距，提升阅读体验
- 对齐方式：左对齐为主，特殊情况居中对齐
- 文字密度：避免大段文字，多用分点和列表

**特殊文字处理：**
- 数字突出：重要数字使用不同颜色或字重
- 链接样式：明确的链接样式，支持点击
- 错误提示：红色文字配小图标，清晰明了
- 成功反馈：绿色文字配对勾图标
```

---

## 📋 **设计规范与标准**

### 📏 **设计标准化**

```
建立一致的设计标准：

**间距系统：**
- 基础单位：8px为基础间距单位
- 组件间距：16px(2倍基础单位)
- 区块间距：24px(3倍基础单位)
- 页面边距：16px统一页面边距
- 安全距离：44px最小点击区域

**圆角规范：**
- 大卡片：16px圆角，营造柔和感
- 按钮组件：12px圆角，平衡美观与实用
- 小元素：8px圆角，保持一致性
- 头像图片：50%圆角，完全圆形
- 输入框：8px圆角，便于识别

**阴影层级：**
- Level 1：卡片阴影，0 2px 8px rgba(0,0,0,0.1)
- Level 2：悬浮元素，0 4px 16px rgba(0,0,0,0.15)
- Level 3：弹窗阴影，0 8px 32px rgba(0,0,0,0.2)
- Level 4：深层弹窗，0 16px 64px rgba(0,0,0,0.25)
```

### ⚡ **性能优化指南**

```
确保设计方案的技术可行性：

**图片优化策略：**
- 格式选择：WebP优先，PNG备选，避免大尺寸JPEG
- 尺寸控制：提供2x、3x倍图，适配不同DPI
- 懒加载：非关键图片使用懒加载策略
- 压缩处理：保持视觉质量的前提下最大化压缩

**动画性能：**
- 60fps目标：确保动画流畅度
- GPU加速：使用transform和opacity属性
- 动画时长：控制在300ms内，避免过长动画
- 降级处理：低性能设备的动画降级方案

**交互响应：**
- 100ms反馈：用户操作后100ms内给出反馈
- 预加载：预测用户行为，提前加载内容
- 缓存策略：合理使用缓存，提升加载速度
- 渐进式加载：优先加载核心内容和功能
```

---

## 🎯 **设计验证与测试**

### 👥 **用户测试指南**

```
设计完成后的验证方法：

**可用性测试：**
- 任务完成率：核心功能的完成率应达到95%以上
- 操作时长：首次购买流程控制在3分钟内
- 错误率：用户操作错误率低于5%
- 满意度：整体满意度评分8分以上(10分制)

**A/B测试方案：**
- 关键页面：首页、商品页、转赠页进行A/B测试
- 测试指标：转化率、留存率、分享率
- 样本大小：每组至少1000个有效样本
- 测试周期：至少运行1周，获得稳定数据

**设备兼容性：**
- 主流设备：iPhone 12/13/14系列，安卓主流品牌
- 系统版本：iOS 14+，Android 8+
- 屏幕尺寸：4.7寸到6.7寸全覆盖
- 性能测试：低端设备的性能表现验证
```

---

## 🎨 **品牌一致性提示词**

### 🏷️ **品牌元素应用**

```
确保品牌形象的一致性表达：

**Logo应用规范：**
- 主Logo：小鹅形象+品牌名称，用于启动页和关键位置
- 图标Logo：纯图形版本，用于小尺寸应用
- 单色Logo：黑白版本，用于特殊场景
- 最小使用：24x24px，保证清晰度

**品牌色彩一致性：**
- 主色调：在所有界面保持一致的品牌色应用
- 辅助色：配套使用，营造品牌氛围
- 色彩比例：主色占20%，辅助色占30%，中性色占50%
- 场景应用：不同场景的色彩应用指南

**品牌语调：**
- 温暖亲和：使用拟人化的表达方式
- 专业可信：在安全和支付相关页面体现专业性
- 有趣活泼：在互动和社交功能中体现趣味性
- 简洁明了：避免冗长复杂的表达
```

---

## 📖 **总结与执行指南**

### 🎯 **设计实施建议**

```
将设计提示词转化为实际设计的建议：

**分阶段实施：**
1. 第一阶段：核心功能界面(发现、商城、我的小鹅)
2. 第二阶段：社交功能界面(动态、转赠、个人中心)
3. 第三阶段：辅助功能界面(设置、帮助、客服)
4. 第四阶段：优化和细节完善

**设计协作流程：**
- 需求确认：基于MVP文档确认设计需求
- 原型设计：使用Figma创建低保真原型
- 视觉设计：完成高保真视觉稿
- 设计评审：团队评审和用户测试
- 开发对接：设计文档和切图交付
- 验收测试：设计还原度验收

**质量保证：**
- 设计一致性：使用设计系统确保一致性
- 用户测试：关键功能的用户测试验证
- 技术可行性：与开发团队确认实现难度
- 性能影响：评估设计对性能的影响
```

### 📝 **设计文档输出**

```
设计完成后需要输出的文档：

**必备文档：**
- 设计规范文档：色彩、字体、间距、组件规范
- 页面原型：完整的页面流程和交互说明
- 切图资源：各种尺寸的图片和图标资源
- 动效说明：关键动画的参数和实现说明

**协作文档：**
- 开发对接文档：技术实现要点和注意事项
- 测试用例：设计相关的测试验收标准
- 更新日志：设计变更的记录和说明
- 维护指南：设计系统的维护和扩展指南
```

---

## 💡 **创新设计建议**

### 🌟 **差异化设计点**

```
打造独特的产品体验：

**情感化创新：**
- 个性化头像：AI生成每只小鹅的独特头像
- 情感表达：小鹅根据互动表现不同情绪
- 记忆系统：记住用户的喂食习惯和偏好
- 成长故事：自动生成小鹅的成长故事

**交互创新：**
- 语音互动：支持语音呼唤小鹅名字
- 手势识别：特殊手势触发隐藏功能
- AR功能：AR模式下的小鹅互动
- 实时同步：多设备间的状态实时同步

**社交创新：**
- 小鹅聚会：多只小鹅的虚拟聚会功能
- 成长对比：朋友间小鹅成长对比
- 协作养成：朋友间共同照顾一只小鹅
- 社区活动：定期的线上线下活动
```

---

*本设计提示词文档为小鹅养成APP的完整UI/UX设计指南，涵盖了从整体设计理念到具体实现细节的全方位设计指导。请根据项目实际需求和开发进度，分阶段实施设计方案。* 