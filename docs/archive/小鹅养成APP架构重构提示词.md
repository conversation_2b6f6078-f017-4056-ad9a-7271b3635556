# 🔧 小鹅养成APP架构重构提示词

## 📋 项目背景
我有一个小鹅养成APP项目，当前使用Spring Boot 3.2.1 + JPA + Sa-Token架构。项目包含用户模块、小鹅模块、商城模块、转赠模块等，现需要进行架构重构。

## 🎯 重构目标

### 1. ORM层架构升级
- **从JPA迁移到MyBatis Plus**
- 保持现有业务逻辑不变
- 优化数据库操作性能
- 简化CRUD操作代码

### 2. 统一字段标准化
所有实体类必须包含以下标准字段：
```java
@TableId(type = IdType.AUTO)
private Long id;                    // 主键ID

private Integer status;             // 状态字段
private LocalDateTime createdAt;    // 创建时间
private LocalDateTime updatedAt;    // 更新时间  
private Long createUser;            // 创建用户ID
private Long updateUser;            // 更新用户ID

@TableLogic
private Integer isDeleted;          // 逻辑删除(0-未删除,1-已删除)
```

**重要规则**: 默认字段必须使用基础实体类(BaseEntity)来封装，所有业务实体类继承BaseEntity，避免重复定义公共字段。

### 3. 测试验证要求
- 重构后运行所有模块的测试脚本
- 确保测试成功率达到100%
- 验证所有API接口功能正常
- 确保数据库操作正确

## 📁 项目结构
```
goose-app-backend/
├── src/main/java/com/gooseapp/
│   ├── user/           # 用户模块
│   ├── goose/          # 小鹅模块  
│   ├── shop/           # 商城模块
│   ├── gift/           # 转赠模块
│   └── common/         # 公共模块
│       └── entity/     # 基础实体类
│           └── BaseEntity.java  # 公共字段基类
├── src/main/resources/
│   ├── application.yml
│   └── db/migration/   # 数据库迁移脚本
└── 测试脚本/
    ├── api-validation-test.sh      # 全量API测试
    ├── gift-api-test.sh           # 转赠模块测试
    └── quick-gift-test.sh         # 快速功能测试
```

## 🔧 技术栈信息
- **框架**: Spring Boot 3.2.1
- **认证**: Sa-Token
- **数据库**: MySQL 8.0 (Docker容器)
- **构建工具**: Maven
- **当前ORM**: Spring Data JPA (需要替换为MyBatis Plus)

## 📝 具体执行要求

### Phase 1: 依赖配置
1. 在pom.xml中添加MyBatis Plus依赖
2. 移除Spring Data JPA相关依赖
3. 配置MyBatis Plus相关设置

### Phase 2: 基础架构设计
1. **创建BaseEntity基类**，包含所有公共字段
2. 配置MyBatis Plus自动填充功能
3. 配置逻辑删除全局设置
4. 设置主键生成策略

### Phase 3: 实体类重构
1. 所有实体类继承BaseEntity基类
2. 使用MyBatis Plus注解替换JPA注解
3. 移除重复的公共字段定义
4. 保持业务字段不变

### Phase 4: 数据访问层重构
1. Repository接口继承BaseMapper<T>
2. 替换JPA查询方法为MyBatis Plus语法
3. 保持Service层接口不变
4. 更新ServiceImpl实现类

### Phase 5: 数据库迁移
1. 为所有表添加缺失的标准字段
2. 创建数据库迁移脚本
3. 确保数据完整性
4. 验证逻辑删除功能

### Phase 6: 测试验证
1. 运行`./api-validation-test.sh`验证全量API
2. 运行`./gift-api-test.sh`验证转赠模块
3. 运行`./quick-gift-test.sh`验证核心功能
4. 确保所有测试100%通过

## 🏗️ BaseEntity设计规范

### BaseEntity类结构示例
```java
@Data
@EqualsAndHashCode(callSuper = false)
public abstract class BaseEntity implements Serializable {
    
    @TableId(type = IdType.AUTO)
    private Long id;
    
    private Integer status;
    
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createdAt;
    
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updatedAt;
    
    @TableField(fill = FieldFill.INSERT)
    private Long createUser;
    
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long updateUser;
    
    @TableLogic
    private Integer isDeleted;
}
```

### 自动填充处理器
需要实现MetaObjectHandler接口，自动填充创建时间、更新时间、创建用户、更新用户等字段。

## ⚠️ 注意事项
1. **保持业务逻辑不变**: 只改架构，不改功能
2. **渐进式迁移**: 一个模块一个模块地迁移
3. **测试驱动**: 每完成一个模块立即测试
4. **数据安全**: 迁移前备份数据库
5. **向下兼容**: 确保现有API接口不受影响
6. **基类设计**: BaseEntity必须设计为抽象类，包含所有公共字段
7. **继承规范**: 所有业务实体类必须继承BaseEntity

## 🎯 成功标准
- [ ] MyBatis Plus成功集成
- [ ] BaseEntity基类设计完成
- [ ] 所有实体类继承BaseEntity
- [ ] 逻辑删除功能正常
- [ ] 自动填充创建/更新时间和用户
- [ ] api-validation-test.sh 100%通过
- [ ] gift-api-test.sh 100%通过  
- [ ] quick-gift-test.sh 100%通过
- [ ] 所有模块功能验证正常

## 📞 技术支持
- 数据库连接: localhost:3306 (Docker容器)
- 管理员账户: superAdmin/admin123
- 项目路径: `/Users/<USER>/Desktop/code/wwr/project/nn/e_card_augment`

请按照上述要求进行架构重构，确保每个步骤都有详细的执行计划和验证方案。重点关注BaseEntity基类的设计和所有实体类的继承关系，确保代码的可维护性和一致性。
