# 🧪 小鹅养成APP - 完整API测试指南

## 📋 测试概述

**更新时间**: 2024年12月19日
**Sa-Token配置**: 已修正为使用 `satoken` header
**可测试接口**: 用户认证模块 + 用户管理模块

---

## ⚠️ 重要配置更新

### Sa-Token Header配置已修正
- **旧配置**: `Authorization: Bearer TOKEN`
- **新配置**: `satoken: Bearer TOKEN` ✅
- **原因**: Sa-Token框架默认使用satoken作为header名称

---

## 🚀 环境准备

### 1. 启动服务
```bash
cd goose-app-backend
mvn spring-boot:run
```

### 2. 验证服务状态
```bash
# 健康检查
curl http://localhost:8080/actuator/health

# API文档
打开浏览器: http://localhost:8080/swagger-ui.html
```

---

## 🔐 认证模块测试 (`/api/v1/auth`)

### 1. 用户注册 ✅ 可测试
```bash
curl -X POST http://localhost:8080/api/v1/auth/register \
  -H "Content-Type: application/json" \
  -d '{
    "username": "newuser",
    "email": "<EMAIL>",
    "password": "password123",
    "confirmPassword": "password123",
    "nickname": "新用户",
    "phone": "13800138999",
    "gender": 1,
    "birthday": "1995-06-15"
  }'
```

### 2. 用户登录 ✅ 可测试
```bash
curl -X POST http://localhost:8080/api/v1/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "username": "testuser",
    "password": "password123"
  }'
```

**返回示例**:
```json
{
  "code": 200,
  "message": "登录成功",
  "data": {
    "token": "your-token-here",
    "user": {
      "id": 1,
      "username": "testuser",
      "nickname": "测试用户"
    }
  }
}
```

### 3. 检查登录状态 ✅ 可测试
```bash
curl -X GET http://localhost:8080/api/v1/auth/status \
  -H "satoken: Bearer YOUR_TOKEN_HERE"
```

### 4. 获取当前用户信息 ✅ 可测试
```bash
curl -X GET http://localhost:8080/api/v1/auth/me \
  -H "satoken: Bearer YOUR_TOKEN_HERE"
```

### 5. Token刷新 ✅ 可测试
```bash
curl -X POST http://localhost:8080/api/v1/auth/refresh \
  -H "satoken: Bearer YOUR_TOKEN_HERE"
```

### 6. 用户登出 ✅ 可测试
```bash
curl -X POST http://localhost:8080/api/v1/auth/logout \
  -H "satoken: Bearer YOUR_TOKEN_HERE"
```

### 7. 修改密码 ✅ 可测试
```bash
curl -X POST http://localhost:8080/api/v1/auth/change-password \
  -H "Content-Type: application/json" \
  -H "satoken: Bearer YOUR_TOKEN_HERE" \
  -d '{
    "oldPassword": "password123",
    "newPassword": "newpassword123",
    "confirmPassword": "newpassword123"
  }'
```

### 8. 可用性检查接口 ✅ 可测试

#### 检查用户名是否可用
```bash
curl -X GET "http://localhost:8080/api/v1/auth/check-username?username=testname"
```

#### 检查邮箱是否可用
```bash
curl -X GET "http://localhost:8080/api/v1/auth/check-email?email=<EMAIL>"
```

#### 检查手机号是否可用
```bash
curl -X GET "http://localhost:8080/api/v1/auth/check-phone?phone=13800138000"
```

---

## 👤 用户管理模块测试 (`/api/v1/users`)

### 1. 获取用户详细信息 ✅ 可测试
```bash
curl -X GET http://localhost:8080/api/v1/users/profile \
  -H "satoken: Bearer YOUR_TOKEN_HERE"
```

### 2. 更新用户信息 ✅ 可测试
```bash
curl -X PUT http://localhost:8080/api/v1/users/profile \
  -H "Content-Type: application/json" \
  -H "satoken: Bearer YOUR_TOKEN_HERE" \
  -d '{
    "nickname": "新昵称",
    "avatar": "👨‍💻",
    "phone": "13800138888",
    "gender": 1,
    "birthday": "1990-01-01"
  }'
```

### 3. 获取用户统计信息 ✅ 可测试
```bash
curl -X GET http://localhost:8080/api/v1/users/stats \
  -H "satoken: Bearer YOUR_TOKEN_HERE"
```

### 4. 获取指定用户公开信息 ✅ 可测试
```bash
# 获取用户ID为1的公开信息
curl -X GET http://localhost:8080/api/v1/users/1 \
  -H "satoken: Bearer YOUR_TOKEN_HERE"
```

### 5. 搜索用户 ✅ 可测试
```bash
curl -X GET "http://localhost:8080/api/v1/users/search?keyword=测试" \
  -H "satoken: Bearer YOUR_TOKEN_HERE"
```

### 6. 获取用户排行榜 ✅ 可测试
```bash
# 按小鹅数量排行
curl -X GET "http://localhost:8080/api/v1/users/ranking?type=geese" \
  -H "satoken: Bearer YOUR_TOKEN_HERE"

# 按养成天数排行
curl -X GET "http://localhost:8080/api/v1/users/ranking?type=days" \
  -H "satoken: Bearer YOUR_TOKEN_HERE"

# 按喂食次数排行
curl -X GET "http://localhost:8080/api/v1/users/ranking?type=feeds" \
  -H "satoken: Bearer YOUR_TOKEN_HERE"
```

---

## 📊 测试用例建议

### 1. 正常流程测试
- ✅ 用户注册 → 登录 → 获取信息 → 更新信息 → 登出
- ✅ 检查用户名/邮箱/手机号可用性
- ✅ 密码修改流程
- ✅ Token刷新机制

### 2. 边界条件测试
- ✅ 用户名长度边界 (3-20字符)
- ✅ 密码强度验证 (8-20字符)
- ✅ 邮箱格式验证
- ✅ 手机号格式验证

### 3. 异常情况测试
- ✅ 重复用户名注册
- ✅ 重复邮箱注册
- ✅ 错误密码登录
- ✅ 无效Token访问
- ✅ 过期Token处理

### 4. 权限验证测试
- ✅ 未登录访问受保护接口
- ✅ Token格式错误
- ✅ 访问其他用户私有信息

---

## 🗄️ 测试数据

### 现有测试账号 (密码: password123)
- `testuser` / <EMAIL> - 测试用户
- `admin` / <EMAIL> - 管理员
- `alice` / <EMAIL> - 爱丽丝
- `bob` / <EMAIL> - 鲍勃
- `charlie` / <EMAIL> - 查理

### 数据库测试数据
- **用户数据**: 5个用户，不同性别和年龄
- **小鹅数据**: 7只小鹅，分属不同用户
- **商品数据**: 6种商品，不同价位
- **订单数据**: 5个订单，不同状态

---

## ❌ 未开发功能 (无法测试)

### 小鹅管理模块
- 小鹅列表查询
- 小鹅详情获取
- 小鹅互动操作 (喂食、喂水、清洁、玩耍)
- 小鹅状态更新

### 商城功能模块
- 商品列表查询
- 商品详情获取
- 购物车管理
- 订单创建和管理

### 转赠功能模块
- 创建转赠
- 领取转赠
- 转赠记录查询

---

## 🎯 推荐测试顺序

### 第一轮：基础功能测试
1. 用户注册 → 登录 → 获取信息
2. 可用性检查接口测试
3. 用户信息更新测试

### 第二轮：高级功能测试
1. 密码修改测试
2. Token刷新测试
3. 用户搜索和排行榜测试

### 第三轮：异常情况测试
1. 重复注册测试
2. 错误登录测试
3. 无效Token测试

---

## 📞 技术支持

### 常见问题
1. **Token格式错误**: 确保使用 `satoken: TOKEN` 而不是 `Authorization: Bearer TOKEN`
2. **接口404**: 检查URL路径是否正确
3. **参数验证失败**: 检查请求体格式和必填字段

### 调试建议
1. 查看Swagger文档: http://localhost:8080/swagger-ui.html
2. 检查应用日志: `logs/goose-app.log`
3. 使用Postman或其他API测试工具

**当前可测试接口总数: 15个** 🎉
