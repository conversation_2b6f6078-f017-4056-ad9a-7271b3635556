# 🦆 小鹅养成APP - 重构验证测试指导

## 📋 测试目标

**验证重构完成情况**: 确保小鹅归属权业务逻辑重构完全正确
**核心验证点**:
- ✅ 管理员权限系统正常工作
- ✅ 管理员可以创建小鹅到商品池
- ✅ 普通用户无法直接创建小鹅
- ✅ 用户购买流程完整可用
- ✅ 购买后归属权正确变更
- ✅ 用户只能查看已归属的小鹅

---

## 🛠️ 测试环境准备

### 1. 启动必要服务

```bash
# 1. 启动MySQL服务
brew services start mysql  # macOS
# 或 sudo systemctl start mysql  # Linux

# 2. 启动Redis服务 (Sa-Token需要)
brew services start redis  # macOS
# 或 sudo systemctl start redis  # Linux

# 3. 验证服务状态
brew services list | grep mysql
brew services list | grep redis
```

### 2. 数据库初始化

```bash
# 1. 连接MySQL
mysql -u root -p

# 2. 创建数据库和用户
CREATE DATABASE IF NOT EXISTS goose_app DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE USER IF NOT EXISTS 'goose_user'@'localhost' IDENTIFIED BY 'goose_password';
GRANT ALL PRIVILEGES ON goose_app.* TO 'goose_user'@'localhost';
FLUSH PRIVILEGES;

# 3. 使用数据库
USE goose_app;

# 4. 执行初始化脚本
source scripts/init-db.sql;

# 5. 验证表结构
SHOW TABLES;
DESCRIBE geese;
DESCRIBE user_roles;
DESCRIBE goose_purchases;
```

### 3. 启动应用服务

```bash
# 1. 编译项目
mvn clean compile

# 2. 启动应用 (后台运行)
nohup mvn spring-boot:run > app.log 2>&1 &

# 3. 查看启动日志
tail -f app.log

# 4. 验证应用启动
curl http://localhost:8080/actuator/health
```

### 4. 准备测试工具

```bash
# 安装jq工具 (用于JSON格式化)
brew install jq  # macOS
# 或 sudo apt-get install jq  # Linux

# 创建测试目录
mkdir -p test-results
cd test-results
```

---

## 👥 测试用户准备

### 1. 创建管理员账户

```bash
# 1. 注册管理员用户
curl -X POST "http://localhost:8080/api/v1/auth/register" \
  -H "Content-Type: application/json" \
  -d '{
    "username": "admin",
    "email": "<EMAIL>",
    "password": "admin123",
    "confirmPassword": "admin123",
    "nickname": "系统管理员"
  }' | jq '.'

# 2. 手动在数据库中添加管理员角色
mysql -u goose_user -pgoose_password goose_app -e "
INSERT INTO user_roles (user_id, role_name) 
SELECT id, 'ADMIN' FROM users WHERE username = 'admin';
"

# 3. 管理员登录获取Token
ADMIN_TOKEN=$(curl -X POST "http://localhost:8080/api/v1/auth/login" \
  -H "Content-Type: application/json" \
  -d '{
    "username": "admin",
    "password": "admin123"
  }' | jq -r '.data.token')

echo "管理员Token: $ADMIN_TOKEN"
```

### 2. 创建普通用户账户

```bash
# 1. 注册普通用户1
curl -X POST "http://localhost:8080/api/v1/auth/register" \
  -H "Content-Type: application/json" \
  -d '{
    "username": "user1",
    "email": "<EMAIL>",
    "password": "user123",
    "confirmPassword": "user123",
    "nickname": "测试用户1"
  }' | jq '.'

# 2. 注册普通用户2
curl -X POST "http://localhost:8080/api/v1/auth/register" \
  -H "Content-Type: application/json" \
  -d '{
    "username": "user2",
    "email": "<EMAIL>",
    "password": "user123",
    "confirmPassword": "user123",
    "nickname": "测试用户2"
  }' | jq '.'

# 3. 普通用户登录获取Token
USER1_TOKEN=$(curl -X POST "http://localhost:8080/api/v1/auth/login" \
  -H "Content-Type: application/json" \
  -d '{
    "username": "user1",
    "password": "user123"
  }' | jq -r '.data.token')

USER2_TOKEN=$(curl -X POST "http://localhost:8080/api/v1/auth/login" \
  -H "Content-Type: application/json" \
  -d '{
    "username": "user2",
    "password": "user123"
  }' | jq -r '.data.token')

echo "用户1 Token: $USER1_TOKEN"
echo "用户2 Token: $USER2_TOKEN"
```

---

## 🧪 核心功能测试

### 测试1: 权限验证 🔐

#### 1.1 验证普通用户无法创建小鹅 ❌

```bash
echo "=== 测试1.1: 普通用户尝试创建小鹅 (应该失败) ==="

# 尝试访问管理员创建小鹅接口 (应该返回403)
curl -X POST "http://localhost:8080/api/v1/admin/geese/create" \
  -H "Content-Type: application/json" \
  -H "satoken: Bearer $USER1_TOKEN" \
  -d '{
    "name": "非法小鹅",
    "breed": "白鹅",
    "gender": 1,
    "initialHealth": 100
  }' | jq '.'

# 期望结果: HTTP 403 Forbidden 或权限错误
```

#### 1.2 验证管理员可以创建小鹅 ✅

```bash
echo "=== 测试1.2: 管理员创建小鹅到商品池 (应该成功) ==="

# 管理员创建小鹅
GOOSE1_RESPONSE=$(curl -X POST "http://localhost:8080/api/v1/admin/geese/create" \
  -H "Content-Type: application/json" \
  -H "satoken: Bearer $ADMIN_TOKEN" \
  -d '{
    "name": "小白",
    "breed": "白鹅",
    "gender": 1,
    "initialHealth": 100
  }')

echo "$GOOSE1_RESPONSE" | jq '.'
GOOSE1_ID=$(echo "$GOOSE1_RESPONSE" | jq -r '.data.id')
echo "创建的小鹅ID: $GOOSE1_ID"

# 期望结果: HTTP 200, 返回小鹅信息，ownership_status = 0 (待售)
```

#### 1.3 批量创建小鹅 ✅

```bash
echo "=== 测试1.3: 管理员批量创建小鹅 ==="

curl -X POST "http://localhost:8080/api/v1/admin/geese/batch-create" \
  -H "Content-Type: application/json" \
  -H "satoken: Bearer $ADMIN_TOKEN" \
  -d '{
    "geese": [
      {
        "name": "小黑",
        "breed": "黑鹅",
        "gender": 0,
        "initialHealth": 95
      },
      {
        "name": "小灰",
        "breed": "灰鹅",
        "gender": 1,
        "initialHealth": 98
      }
    ]
  }' | jq '.'

# 期望结果: 成功创建多只小鹅到商品池
```

### 测试2: 商城功能 🛒

#### 2.1 查看待售小鹅

```bash
echo "=== 测试2.1: 查看商城待售小鹅 ==="

curl -X GET "http://localhost:8080/api/v1/shop/geese/available" \
  -H "satoken: Bearer $USER1_TOKEN" | jq '.'

# 期望结果: 显示所有待售小鹅 (ownership_status = 0)
```

#### 2.2 按品种筛选小鹅

```bash
echo "=== 测试2.2: 按品种筛选小鹅 ==="

curl -X GET "http://localhost:8080/api/v1/shop/geese/available/breed/白鹅" \
  -H "satoken: Bearer $USER1_TOKEN" | jq '.'

# 期望结果: 只显示白鹅品种的待售小鹅
```

#### 2.3 查看小鹅详情

```bash
echo "=== 测试2.3: 查看小鹅详情 ==="

curl -X GET "http://localhost:8080/api/v1/shop/geese/$GOOSE1_ID" \
  -H "satoken: Bearer $USER1_TOKEN" | jq '.'

# 期望结果: 显示小鹅详细信息
```

### 测试3: 购买流程 💰

#### 3.1 用户购买小鹅

```bash
echo "=== 测试3.1: 用户1购买小鹅 ==="

PURCHASE_RESPONSE=$(curl -X POST "http://localhost:8080/api/v1/shop/purchase" \
  -H "Content-Type: application/json" \
  -H "satoken: Bearer $USER1_TOKEN" \
  -d "{
    \"gooseId\": $GOOSE1_ID,
    \"paymentMethod\": \"WECHAT_PAY\"
  }")

echo "$PURCHASE_RESPONSE" | jq '.'
PURCHASE_ID=$(echo "$PURCHASE_RESPONSE" | jq -r '.data.id')
echo "购买记录ID: $PURCHASE_ID"

# 期望结果: 购买成功，返回购买记录
```

#### 3.2 验证小鹅归属权变更

```bash
echo "=== 测试3.2: 验证小鹅归属权变更 ==="

# 查询小鹅当前状态
curl -X GET "http://localhost:8080/api/v1/geese/$GOOSE1_ID" \
  -H "satoken: Bearer $USER1_TOKEN" | jq '.'

# 期望结果: 
# - ownership_status = 1 (已归属)
# - user_id = user1的ID
# - purchase_time 不为空
```

#### 3.3 重复购买应该失败

```bash
echo "=== 测试3.3: 重复购买同一只小鹅 (应该失败) ==="

curl -X POST "http://localhost:8080/api/v1/shop/purchase" \
  -H "Content-Type: application/json" \
  -H "satoken: Bearer $USER2_TOKEN" \
  -d "{
    \"gooseId\": $GOOSE1_ID,
    \"paymentMethod\": \"ALIPAY\"
  }" | jq '.'

# 期望结果: 购买失败，提示小鹅已售出
```

### 测试4: 用户小鹅管理 🐣

#### 4.1 查看用户已归属小鹅

```bash
echo "=== 测试4.1: 用户1查看已归属小鹅 ==="

curl -X GET "http://localhost:8080/api/v1/geese/my" \
  -H "satoken: Bearer $USER1_TOKEN" | jq '.'

# 期望结果: 只显示用户1已归属的小鹅
```

#### 4.2 验证用户2看不到用户1的小鹅

```bash
echo "=== 测试4.2: 用户2查看已归属小鹅 (应该为空) ==="

curl -X GET "http://localhost:8080/api/v1/geese/my" \
  -H "satoken: Bearer $USER2_TOKEN" | jq '.'

# 期望结果: 空数组，因为用户2没有购买任何小鹅
```

#### 4.3 用户可以正常互动

```bash
echo "=== 测试4.3: 用户1与已归属小鹅互动 ==="

# 喂食
curl -X POST "http://localhost:8080/api/v1/geese/$GOOSE1_ID/feed" \
  -H "satoken: Bearer $USER1_TOKEN" | jq '.'

# 喂水
curl -X POST "http://localhost:8080/api/v1/geese/$GOOSE1_ID/water" \
  -H "satoken: Bearer $USER1_TOKEN" | jq '.'

# 期望结果: 互动成功，小鹅状态发生变化
```

#### 4.4 用户2无法与用户1的小鹅互动

```bash
echo "=== 测试4.4: 用户2尝试与用户1的小鹅互动 (应该失败) ==="

curl -X POST "http://localhost:8080/api/v1/geese/$GOOSE1_ID/feed" \
  -H "satoken: Bearer $USER2_TOKEN" | jq '.'

# 期望结果: 权限错误或提示小鹅不属于该用户
```

### 测试5: 购买记录管理 📝

#### 5.1 查看购买记录

```bash
echo "=== 测试5.1: 查看用户购买记录 ==="

curl -X GET "http://localhost:8080/api/v1/shop/purchases/my" \
  -H "satoken: Bearer $USER1_TOKEN" | jq '.'

# 期望结果: 显示用户1的所有购买记录
```

#### 5.2 查看购买统计

```bash
echo "=== 测试5.2: 查看用户购买统计 ==="

curl -X GET "http://localhost:8080/api/v1/shop/purchases/statistics/my" \
  -H "satoken: Bearer $USER1_TOKEN" | jq '.'

# 期望结果: 显示用户购买统计信息
```

### 测试6: 管理员管理功能 👑

#### 6.1 查看所有小鹅状态

```bash
echo "=== 测试6.1: 管理员查看所有小鹅状态 ==="

curl -X GET "http://localhost:8080/api/v1/admin/geese/all" \
  -H "satoken: Bearer $ADMIN_TOKEN" | jq '.'

# 期望结果: 显示所有小鹅（待售和已归属）
```

#### 6.2 查看待售小鹅

```bash
echo "=== 测试6.2: 管理员查看待售小鹅 ==="

curl -X GET "http://localhost:8080/api/v1/admin/geese/available" \
  -H "satoken: Bearer $ADMIN_TOKEN" | jq '.'

# 期望结果: 只显示待售状态的小鹅
```

---

## 🔍 数据一致性验证

### 验证数据库状态

```bash
echo "=== 数据一致性验证 ==="

# 1. 验证小鹅归属状态
mysql -u goose_user -pgoose_password goose_app -e "
SELECT 
    g.id,
    g.name,
    g.user_id,
    g.ownership_status,
    g.purchase_time,
    u.username
FROM geese g
LEFT JOIN users u ON g.user_id = u.id
ORDER BY g.id;
"

# 2. 验证购买记录
mysql -u goose_user -pgoose_password goose_app -e "
SELECT 
    p.id as purchase_id,
    p.user_id,
    p.goose_id,
    p.purchase_price,
    p.purchase_time,
    u.username,
    g.name as goose_name
FROM goose_purchases p
JOIN users u ON p.user_id = u.id
JOIN geese g ON p.goose_id = g.id
ORDER BY p.purchase_time DESC;
"

# 3. 验证用户角色
mysql -u goose_user -pgoose_password goose_app -e "
SELECT 
    ur.id,
    ur.user_id,
    ur.role_name,
    u.username
FROM user_roles ur
JOIN users u ON ur.user_id = u.id;
"
```

---

## 📊 测试结果记录

### 创建测试结果记录文件

```bash
echo "=== 生成测试报告 ==="

cat > test-report.md << 'EOF'
# 重构验证测试报告

## 测试执行时间
- 开始时间: $(date)
- 测试人员: 

## 测试结果汇总

### ✅ 权限验证测试
- [ ] 普通用户无法创建小鹅 ❌
- [ ] 管理员可以创建小鹅 ✅
- [ ] 批量创建小鹅功能 ✅

### ✅ 商城功能测试  
- [ ] 查看待售小鹅
- [ ] 按品种筛选
- [ ] 查看小鹅详情

### ✅ 购买流程测试
- [ ] 用户购买小鹅
- [ ] 归属权变更验证
- [ ] 重复购买失败

### ✅ 用户管理测试
- [ ] 查看已归属小鹅
- [ ] 权限隔离验证
- [ ] 正常互动功能

### ✅ 购买记录测试
- [ ] 购买记录查询
- [ ] 购买统计功能

### ✅ 管理员功能测试
- [ ] 查看所有小鹅
- [ ] 管理商品池

## 问题记录
- 
- 

## 总结
- 
EOF

echo "测试报告模板已生成: test-report.md"
```

---

## 🚨 常见问题解决

### 问题1: 应用启动失败

```bash
# 检查日志
tail -f app.log | grep -i error

# 检查端口占用
lsof -i :8080

# 重新启动
pkill -f "spring-boot"
mvn spring-boot:run
```

### 问题2: 数据库连接失败

```bash
# 检查MySQL状态
brew services list | grep mysql

# 测试连接
mysql -u goose_user -pgoose_password -h localhost -P 3306 goose_app

# 重置权限
mysql -u root -p -e "GRANT ALL PRIVILEGES ON goose_app.* TO 'goose_user'@'localhost';"
```

### 问题3: Redis连接失败

```bash
# 检查Redis状态
redis-cli ping

# 启动Redis
brew services start redis

# 检查配置
cat src/main/resources/application.yml | grep -A 10 redis
```

### 问题4: Token无效

```bash
# 重新获取Token
curl -X POST "http://localhost:8080/api/v1/auth/login" \
  -H "Content-Type: application/json" \
  -d '{"username": "admin", "password": "admin123"}' | jq -r '.data.token'
```

---

## 📋 测试检查清单

### 启动前检查
- [ ] MySQL服务已启动
- [ ] Redis服务已启动  
- [ ] 数据库已初始化
- [ ] 应用启动成功

### 功能测试检查
- [ ] 权限验证正常 (普通用户无法创建小鹅)
- [ ] 管理员功能正常 (可以创建小鹅到商品池)
- [ ] 购买流程正常 (用户可以购买小鹅)
- [ ] 归属权变更正常 (购买后小鹅归属正确)
- [ ] 权限隔离正常 (用户只能看到自己的小鹅)
- [ ] 互动功能正常 (已归属小鹅可以正常互动)

### 数据一致性检查
- [ ] 小鹅状态与购买记录一致
- [ ] 用户角色分配正确
- [ ] 数据库约束有效

---

## 🎯 成功标准

### 必须通过的测试
1. **权限验证** - 普通用户无法创建小鹅 ❌
2. **管理员功能** - 管理员可以创建小鹅 ✅  
3. **购买流程** - 用户可以购买小鹅 ✅
4. **归属权变更** - 购买后归属正确 ✅
5. **权限隔离** - 用户只能操作自己的小鹅 ✅

### 性能验证
- API响应时间 < 500ms
- 数据库查询优化
- 并发购买处理正确

**测试完成标志**: 所有核心测试用例通过，数据一致性验证无问题，重构目标完全达成。

---

**文档版本**: v1.0  
**创建时间**: 2024-12-19  
**适用于**: 小鹅养成APP重构后验证测试 

- **技术栈**: Java + Spring Boot + MySQL + Sa-Token + React Native 