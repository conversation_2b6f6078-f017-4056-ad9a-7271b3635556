# 小鹅养成APP - 后端API完整测试方案

**项目名称：** 小鹅养成APP 后端API测试  
**测试版本：** v1.0.0  
**创建时间：** 2024年12月  
**测试环境：** 本地开发环境  
**服务地址：** http://localhost:8080  

---

## 📋 测试概述

### 测试目标
验证小鹅归属权业务逻辑重构后的系统功能完整性，确保：
- ✅ 管理员权限系统正常工作
- ✅ 管理员可以创建小鹅到商品池
- ✅ 普通用户无法直接创建小鹅
- ✅ 用户购买流程完整可用
- ✅ 购买后归属权正确变更
- ✅ 用户只能查看已归属的小鹅
- ✅ 小鹅互动功能正常

### 测试范围
- 用户认证系统
- 权限控制机制
- 小鹅商品池管理
- 购买流程验证
- 小鹅互动功能
- 数据一致性验证

---

## 🧪 详细测试用例

### 测试阶段一：用户认证系统 🔐

#### TC001 - 管理员账户注册
**接口：** `POST /api/v1/auth/register`  
**目标：** 注册管理员账户，后续手动添加管理员角色

**请求头：**
```http
Content-Type: application/json
```

**请求体：**
```json
{
    "username": "admin",
    "email": "<EMAIL>",
    "password": "admin123",
    "confirmPassword": "admin123",
    "nickname": "系统管理员"
}
```

**期望结果：**
- HTTP状态码：200
- 响应包含用户信息和token
- 自动登录成功

**验证步骤：**
1. 保存返回的用户ID到环境变量
2. 保存token到环境变量
3. 在数据库中手动添加管理员角色

**数据库操作：**
```sql
-- 为admin用户添加管理员角色
INSERT INTO user_roles (user_id, role_name) 
SELECT id, 'ADMIN' FROM users WHERE username = 'admin';
```

---

#### TC002 - 普通用户1注册
**接口：** `POST /api/v1/auth/register`

**请求体：**
```json
{
    "username": "user1",
    "email": "<EMAIL>",
    "password": "user123",
    "confirmPassword": "user123",
    "nickname": "测试用户1"
}
```

**期望结果：**
- HTTP状态码：200
- 用户注册成功
- 默认角色为普通用户

---

#### TC003 - 普通用户2注册
**接口：** `POST /api/v1/auth/register`

**请求体：**
```json
{
    "username": "user2",
    "email": "<EMAIL>",
    "password": "user123",
    "confirmPassword": "user123",
    "nickname": "测试用户2"
}
```

**期望结果：**
- HTTP状态码：200
- 用户注册成功

---

#### TC004 - 管理员登录
**接口：** `POST /api/v1/auth/login`

**请求体：**
```json
{
    "username": "admin",
    "password": "admin123"
}
```

**期望结果：**
```json
{
    "code": 200,
    "message": "登录成功",
    "data": {
        "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
        "userInfo": {
            "id": 1,
            "username": "admin",
            "nickname": "系统管理员",
            "email": "<EMAIL>"
        },
        "tokenExpire": 7200
    }
}
```

---

#### TC005 - 用户1登录
**接口：** `POST /api/v1/auth/login`

**请求体：**
```json
{
    "username": "user1",
    "password": "user123"
}
```

---

#### TC006 - 用户2登录
**接口：** `POST /api/v1/auth/login`

**请求体：**
```json
{
    "username": "user2",
    "password": "user123"
}
```

---

### 测试阶段二：权限验证系统 🛡️

#### TC007 - 普通用户尝试创建小鹅（权限测试）
**接口：** `POST /api/v1/admin/geese/create`  
**目标：** 验证普通用户无法访问管理员接口

**请求头：**
```http
satoken: Bearer {{user1_token}}
Content-Type: application/json
```

**请求体：**
```json
{
    "name": "非法小鹅",
    "breed": "白鹅",
    "gender": 1,
    "initialHealth": 100,
    "initialHunger": 50,
    "initialThirst": 50,
    "initialCleanliness": 100,
    "initialHappiness": 80,
    "initialLevel": 1,
    "initialExperience": 0
}
```

**期望结果：**
- HTTP状态码：403 Forbidden
- 错误信息：权限不足或需要管理员权限

**期望响应：**
```json
{
    "code": 403,
    "message": "权限不足，需要管理员权限",
    "data": null
}
```

---

#### TC008 - 验证用户权限检查
**接口：** `GET /api/v1/auth/me`

**请求头：**
```http
satoken: Bearer {{user1_token}}
```

**期望结果：**
- HTTP状态码：200
- 返回当前用户信息
- 确认用户角色为普通用户

---

### 测试阶段三：管理员小鹅创建 🦆

#### TC009 - 管理员创建单只小鹅
**接口：** `POST /api/v1/admin/geese/create`

**请求头：**
```http
satoken: Bearer {{admin_token}}
Content-Type: application/json
```

**请求体：**
```json
{
    "name": "可爱小白",
    "breed": "白鹅",
    "gender": 1,
    "initialHealth": 100,
    "initialHunger": 50,
    "initialThirst": 50,
    "initialCleanliness": 100,
    "initialHappiness": 80,
    "initialLevel": 1,
    "initialExperience": 0,
    "imageUrl": "https://example.com/geese/white-goose-1.jpg",
    "videoUrl": "https://live.example.com/stream/goose-1"
}
```

**期望结果：**
```json
{
    "code": 200,
    "message": "小鹅创建成功",
    "data": {
        "id": 1,
        "name": "可爱小白",
        "breed": "白鹅",
        "gender": 1,
        "health": 100,
        "hunger": 50,
        "thirst": 50,
        "cleanliness": 100,
        "happiness": 80,
        "level": 1,
        "experience": 0,
        "ownershipStatus": 0,
        "userId": null,
        "purchaseTime": null,
        "imageUrl": "https://example.com/geese/white-goose-1.jpg",
        "videoUrl": "https://live.example.com/stream/goose-1",
        "createdAt": "2024-12-19T10:30:00"
    }
}
```

**验证步骤：**
1. 保存小鹅ID用于后续测试
2. 确认 `ownershipStatus` = 0 (待售状态)
3. 确认 `userId` = null (无归属用户)

---

#### TC010 - 管理员批量创建小鹅
**接口：** `POST /api/v1/admin/geese/batch-create`

**请求头：**
```http
satoken: Bearer {{admin_token}}
Content-Type: application/json
```

**请求体：**
```json
{
    "geese": [
        {
            "name": "活泼小灰",
            "breed": "灰鹅",
            "gender": 0,
            "initialHealth": 95,
            "initialHunger": 30,
            "initialThirst": 40,
            "initialCleanliness": 90,
            "initialHappiness": 85,
            "initialLevel": 1,
            "initialExperience": 0,
            "imageUrl": "https://example.com/geese/grey-goose-1.jpg",
            "videoUrl": "https://live.example.com/stream/goose-2"
        },
        {
            "name": "淘气小黄",
            "breed": "黄鹅",
            "gender": 1,
            "initialHealth": 98,
            "initialHunger": 45,
            "initialThirst": 35,
            "initialCleanliness": 95,
            "initialHappiness": 90,
            "initialLevel": 1,
            "initialExperience": 0,
            "imageUrl": "https://example.com/geese/yellow-goose-1.jpg",
            "videoUrl": "https://live.example.com/stream/goose-3"
        },
        {
            "name": "温柔小粉",
            "breed": "粉鹅",
            "gender": 0,
            "initialHealth": 100,
            "initialHunger": 20,
            "initialThirst": 25,
            "initialCleanliness": 100,
            "initialHappiness": 95,
            "initialLevel": 1,
            "initialExperience": 0,
            "imageUrl": "https://example.com/geese/pink-goose-1.jpg",
            "videoUrl": "https://live.example.com/stream/goose-4"
        }
    ],
    "ignoreErrors": true
}
```

**期望结果：**
- HTTP状态码：200
- 成功创建3只小鹅
- 所有小鹅ownershipStatus = 0

**验证步骤：**
1. 保存返回的小鹅ID用于后续测试
2. 确认创建数量正确
3. 验证所有小鹅都处于待售状态

---

### 测试阶段四：商城功能验证 🛒

#### TC011 - 查看待售小鹅列表
**接口：** `GET /api/v1/shop/geese/available?page=0&size=10`

**请求头：**
```http
satoken: Bearer {{user1_token}}
```

**期望结果：**
```json
{
    "code": 200,
    "message": "查询成功",
    "data": {
        "content": [
            {
                "id": 1,
                "name": "可爱小白",
                "breed": "白鹅",
                "gender": 1,
                "ownershipStatus": 0,
                "userId": null
            }
        ],
        "totalElements": 4,
        "totalPages": 1,
        "size": 10,
        "number": 0
    }
}
```

**验证步骤：**
1. 确认返回的小鹅数量正确
2. 确认所有小鹅 `ownershipStatus` = 0
3. 确认所有小鹅 `userId` = null

---

#### TC012 - 按品种查看小鹅
**接口：** `GET /api/v1/shop/geese/available/breed/白鹅`

**请求头：**
```http
satoken: Bearer {{user1_token}}
```

**期望结果：**
- 返回所有白鹅品种的待售小鹅
- 过滤结果正确

---

#### TC013 - 查看小鹅详情
**接口：** `GET /api/v1/shop/geese/{gooseId}`

**请求头：**
```http
satoken: Bearer {{user1_token}}
```

**期望结果：**
- 返回指定小鹅的详细信息
- 包含所有属性值

---

#### TC014 - 检查小鹅是否可购买
**接口：** `GET /api/v1/shop/geese/{gooseId}/available`

**请求头：**
```http
satoken: Bearer {{user1_token}}
```

**期望结果：**
```json
{
    "code": 200,
    "message": "查询成功",
    "data": true
}
```

---

### 测试阶段五：购买流程验证 💰

#### TC015 - 用户1成功购买小鹅
**接口：** `POST /api/v1/shop/purchase`

**请求头：**
```http
satoken: Bearer {{user1_token}}
Content-Type: application/json
```

**请求体：**
```json
{
    "gooseId": 1,
    "purchasePrice": 199.00,
    "productId": 1
}
```

**期望结果：**
```json
{
    "code": 200,
    "message": "购买成功",
    "data": {
        "purchaseId": 1,
        "userId": 1,
        "goose": {
            "id": 1,
            "name": "可爱小白",
            "ownershipStatus": 1,
            "userId": 1,
            "purchaseTime": "2024-12-19T10:35:00"
        },
        "purchasePrice": 199.00,
        "purchaseTime": "2024-12-19T10:35:00",
        "status": 1
    }
}
```

**验证步骤：**
1. 保存购买记录ID用于后续测试
2. 确认小鹅归属状态变更为1
3. 确认用户ID正确设置
4. 确认购买时间已记录

---

#### TC016 - 用户2尝试购买同一只小鹅（并发测试）
**接口：** `POST /api/v1/shop/purchase`

**请求头：**
```http
satoken: Bearer {{user2_token}}
Content-Type: application/json
```

**请求体：**
```json
{
    "gooseId": 1,
    "purchasePrice": 199.00,
    "productId": 1
}
```

**期望结果：**
```json
{
    "code": 400,
    "message": "购买失败：小鹅已被购买",
    "data": null
}
```

**验证步骤：**
确认购买失败，错误信息正确

---

#### TC017 - 用户2购买其他小鹅
**接口：** `POST /api/v1/shop/purchase`

**请求头：**
```http
satoken: Bearer {{user2_token}}
Content-Type: application/json
```

**请求体：**
```json
{
    "gooseId": 2,
    "purchasePrice": 299.00,
    "productId": 2
}
```

**期望结果：**
- 购买成功
- 小鹅归属给用户2

---

### 测试阶段六：用户小鹅管理 🏠

#### TC018 - 查看用户1的小鹅
**接口：** `GET /api/v1/geese/my`

**请求头：**
```http
satoken: Bearer {{user1_token}}
```

**期望结果：**
```json
{
    "code": 200,
    "message": "查询成功",
    "data": [
        {
            "id": 1,
            "name": "可爱小白",
            "breed": "白鹅",
            "ownershipStatus": 1,
            "userId": 1,
            "purchaseTime": "2024-12-19T10:35:00"
        }
    ]
}
```

**验证步骤：**
1. 确认只返回用户1拥有的小鹅
2. 确认小鹅归属状态正确

---

#### TC019 - 查看用户2的小鹅
**接口：** `GET /api/v1/geese/my`

**请求头：**
```http
satoken: Bearer {{user2_token}}
```

**期望结果：**
- 只返回用户2拥有的小鹅
- 不包含用户1的小鹅

---

#### TC020 - 获取小鹅详细信息
**接口：** `GET /api/v1/geese/{gooseId}`

**请求头：**
```http
satoken: Bearer {{user1_token}}
```

**期望结果：**
- 返回小鹅的详细信息
- 包含当前属性值

---

### 测试阶段七：小鹅互动功能 🎮

#### TC021 - 喂食小鹅
**接口：** `POST /api/v1/geese/{gooseId}/feed`

**请求头：**
```http
satoken: Bearer {{user1_token}}
```

**期望结果：**
```json
{
    "code": 200,
    "message": "喂食成功",
    "data": {
        "id": 1,
        "userId": 1,
        "gooseId": 1,
        "interactionType": "FEED",
        "interactionTime": "2024-12-19T10:40:00",
        "experience": 5,
        "description": "给小鹅喂食，获得5点经验"
    }
}
```

**验证步骤：**
1. 确认互动记录创建成功
2. 确认小鹅状态更新（饥饿度降低）
3. 确认经验值增加

---

#### TC022 - 给小鹅喂水
**接口：** `POST /api/v1/geese/{gooseId}/water`

**请求头：**
```http
satoken: Bearer {{user1_token}}
```

**期望结果：**
- 喂水成功
- 口渴度降低
- 获得经验值

---

#### TC023 - 清洁小鹅
**接口：** `POST /api/v1/geese/{gooseId}/clean`

**请求头：**
```http
satoken: Bearer {{user1_token}}
```

**期望结果：**
- 清洁成功
- 清洁度提升
- 获得经验值

---

#### TC024 - 与小鹅玩耍
**接口：** `POST /api/v1/geese/{gooseId}/play`

**请求头：**
```http
satoken: Bearer {{user1_token}}
```

**期望结果：**
- 玩耍成功
- 快乐度提升
- 获得经验值

---

#### TC025 - 用户2尝试与用户1的小鹅互动（权限测试）
**接口：** `POST /api/v1/geese/{gooseId}/feed`

**请求头：**
```http
satoken: Bearer {{user2_token}}
```

**期望结果：**
```json
{
    "code": 403,
    "message": "无权限操作此小鹅",
    "data": null
}
```

---

### 测试阶段八：购买记录查询 📊

#### TC026 - 查看用户1购买记录
**接口：** `GET /api/v1/shop/purchases/my?page=0&size=10`

**请求头：**
```http
satoken: Bearer {{user1_token}}
```

**期望结果：**
```json
{
    "code": 200,
    "message": "查询成功",
    "data": {
        "content": [
            {
                "id": 1,
                "userId": 1,
                "gooseId": 1,
                "purchasePrice": 199.00,
                "purchaseTime": "2024-12-19T10:35:00",
                "status": 1,
                "goose": {
                    "name": "可爱小白",
                    "breed": "白鹅"
                }
            }
        ],
        "totalElements": 1
    }
}
```

---

#### TC027 - 查看购买记录详情
**接口：** `GET /api/v1/shop/purchases/{purchaseId}`

**请求头：**
```http
satoken: Bearer {{user1_token}}
```

**期望结果：**
- 返回完整的购买记录详情
- 包含关联的小鹅信息

---

#### TC028 - 查看购买统计
**接口：** `GET /api/v1/shop/purchases/statistics/my`

**请求头：**
```http
satoken: Bearer {{user1_token}}
```

**期望结果：**
```json
{
    "code": 200,
    "message": "查询成功",
    "data": {
        "totalPurchases": 1,
        "successfulPurchases": 1,
        "failedPurchases": 0,
        "totalSpent": 199.00,
        "firstPurchaseTime": "2024-12-19T10:35:00",
        "lastPurchaseTime": "2024-12-19T10:35:00"
    }
}
```

---

### 测试阶段九：管理员功能验证 👨‍💼

#### TC029 - 管理员查看所有小鹅
**接口：** `GET /api/v1/admin/geese/all?page=0&size=20`

**请求头：**
```http
satoken: Bearer {{admin_token}}
```

**期望结果：**
- 返回所有小鹅（包括待售和已归属）
- 分页信息正确

---

#### TC030 - 管理员查看待售小鹅
**接口：** `GET /api/v1/admin/geese/available?page=0&size=20`

**请求头：**
```http
satoken: Bearer {{admin_token}}
```

**期望结果：**
- 只返回待售状态的小鹅
- ownershipStatus = 0

---

#### TC031 - 管理员查看已归属小鹅
**接口：** `GET /api/v1/admin/geese/owned?page=0&size=20`

**请求头：**
```http
satoken: Bearer {{admin_token}}
```

**期望结果：**
- 只返回已归属的小鹅
- ownershipStatus = 1

---

#### TC032 - 管理员强制更改小鹅归属
**接口：** `PUT /api/v1/admin/geese/{gooseId}/ownership?userId={userId}`

**请求头：**
```http
satoken: Bearer {{admin_token}}
```

**期望结果：**
- 成功更改小鹅归属
- 小鹅归属状态更新

---

#### TC033 - 管理员获取小鹅统计
**接口：** `GET /api/v1/admin/geese/statistics`

**请求头：**
```http
satoken: Bearer {{admin_token}}
```

**期望结果：**
```json
{
    "code": 200,
    "message": "查询成功",
    "data": {
        "totalGeese": 4,
        "availableGeese": 2,
        "ownedGeese": 2,
        "totalBreeds": 3,
        "breeds": ["白鹅", "灰鹅", "黄鹅", "粉鹅"]
    }
}
```

---

## 🔍 数据库验证测试

### 测试阶段十：数据一致性验证 📊

#### TC034 - 验证数据库状态
在测试过程中，定期执行以下SQL查询验证数据一致性：

```sql
-- 1. 检查用户角色分配
SELECT 
    u.username,
    ur.role_name,
    COUNT(*) as role_count
FROM users u
LEFT JOIN user_roles ur ON u.id = ur.user_id
GROUP BY u.username, ur.role_name
ORDER BY u.username;

-- 2. 检查小鹅归属状态
SELECT 
    ownership_status,
    COUNT(*) as count,
    CASE 
        WHEN ownership_status = 0 THEN '待售'
        WHEN ownership_status = 1 THEN '已归属'
        ELSE '未知状态'
    END as status_desc
FROM geese 
GROUP BY ownership_status;

-- 3. 检查购买记录完整性
SELECT 
    gp.id as purchase_id,
    u.username as buyer,
    g.name as goose_name,
    gp.purchase_price,
    gp.status,
    gp.purchase_time
FROM goose_purchases gp
JOIN users u ON gp.user_id = u.id
JOIN geese g ON gp.goose_id = g.id
ORDER BY gp.purchase_time DESC;

-- 4. 检查小鹅与用户的关联关系
SELECT 
    g.id as goose_id,
    g.name as goose_name,
    g.ownership_status,
    u.username as owner,
    g.purchase_time
FROM geese g
LEFT JOIN users u ON g.user_id = u.id
ORDER BY g.ownership_status, g.id;

-- 5. 验证互动记录
SELECT 
    i.id,
    u.username,
    g.name as goose_name,
    i.interaction_type,
    i.interaction_time,
    i.experience
FROM interactions i
JOIN users u ON i.user_id = u.id
JOIN geese g ON i.goose_id = g.id
ORDER BY i.interaction_time DESC;
```

**期望结果：**
1. 管理员用户具有ADMIN角色
2. 普通用户具有USER角色或无特殊角色
3. 已购买小鹅的ownership_status = 1
4. 待售小鹅的ownership_status = 0
5. 购买记录与小鹅归属状态一致
6. 互动记录只存在于用户与其拥有的小鹅之间

---

## 📝 测试检查清单

### 功能完整性检查
- [ ] 用户注册登录功能正常
- [ ] 权限控制机制工作正确
- [ ] 管理员可以创建小鹅到商品池
- [ ] 普通用户无法访问管理员接口
- [ ] 用户可以查看和购买待售小鹅
- [ ] 购买流程完整，归属权正确变更
- [ ] 并发购买控制，防止重复购买
- [ ] 小鹅互动功能正常
- [ ] 购买记录正确保存和查询
- [ ] 数据库数据一致性保持

### 业务规则验证
- [ ] 小鹅创建后默认为待售状态
- [ ] 购买成功后小鹅归属状态变更
- [ ] 用户只能查看和操作自己的小鹅
- [ ] 管理员具有查看和管理所有小鹅的权限
- [ ] 互动功能只对小鹅主人开放

### 安全性验证
- [ ] Token认证机制正常
- [ ] 权限控制防止越权操作
- [ ] 数据隔离，用户间数据不互通
- [ ] 管理员权限正确验证

---

## 🐛 常见问题及解决方案

### 问题1：权限验证失败
**现象：** 管理员无法访问管理员接口  
**排查：** 
1. 检查用户是否正确获得管理员角色
2. 验证Token是否正确传递
3. 确认权限拦截器配置正确

**解决：**
```sql
-- 手动为用户添加管理员角色
INSERT INTO user_roles (user_id, role_name) 
SELECT id, 'ADMIN' FROM users WHERE username = 'admin';
```

### 问题2：购买失败
**现象：** 小鹅显示可购买但购买时失败  
**排查：**
1. 检查小鹅ownership_status状态
2. 验证并发控制是否正常
3. 确认数据库事务正确提交

### 问题3：数据不一致
**现象：** 小鹅归属状态与购买记录不匹配  
**排查：**
1. 检查事务回滚机制
2. 验证数据库外键约束
3. 确认业务逻辑正确执行

---

## 📊 测试报告模板

### 测试执行记录
| 测试用例 | 执行状态 | 期望结果 | 实际结果 | 备注 |
|---------|---------|---------|---------|------|
| TC001 | ✅ 通过 | 200 成功 | 200 成功 | - |
| TC002 | ✅ 通过 | 200 成功 | 200 成功 | - |
| ... | ... | ... | ... | ... |

### 缺陷记录
| 缺陷ID | 严重级别 | 描述 | 状态 | 修复说明 |
|-------|---------|------|------|---------|
| BUG001 | 高 | 权限验证异常 | 已修复 | 更新权限拦截器逻辑 |
| ... | ... | ... | ... | ... |

---

## 🎯 结论

通过本测试方案的完整执行，可以验证小鹅养成APP后端系统的以下能力：

1. **业务逻辑完整性** - 管理员创建→用户购买→归属变更的完整流程
2. **权限控制正确性** - 用户角色权限正确分离和验证
3. **数据一致性** - 多表关联数据保持一致
4. **并发安全性** - 防止重复购买和数据竞争
5. **功能完整性** - 所有核心功能正常工作

测试完成后，系统应该能够：
- ✅ 支持管理员创建小鹅到商品池
- ✅ 支持用户浏览和购买小鹅
- ✅ 正确处理小鹅归属权变更
- ✅ 提供完整的小鹅养成互动功能
- ✅ 保证数据安全和用户权限控制

---

**测试负责人：** 全栈开发工程师  
**测试完成时间：** 待执行  
**下次测试计划：** 根据测试结果制定 