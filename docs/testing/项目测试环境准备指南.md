# 🚀 小鹅养成APP - 项目测试环境准备指南

## 📋 环境准备概述

**准备时间**: 2024年12月19日
**项目状态**: 用户认证模块后端完成，数据库设计优化完毕
**准备目标**: 建立完整的本地测试环境，支持手动启动和API测试

---

## 🛠 环境要求检查

### 1. 基础环境 ✅ 已确认

- **JDK**: 17+ ✅
- **Maven**: 3.9+ ✅
- **MySQL**: 8.0+ ✅
- **Redis**: 6.0+ (可选，用于Sa-Token缓存)
- **IDE**: IntelliJ IDEA 或 VS Code

### 2. 项目技术栈 ✅ 已确认

- **后端框架**: Spring Boot 3.2.1
- **认证框架**: Sa-Token 1.37.0
- **数据库**: MySQL 8.0 + JPA
- **构建工具**: Maven
- **API文档**: SpringDoc OpenAPI 3

---

## 📊 项目完成情况分析

### 已完成功能 ✅

#### 用户认证模块 (90%完成)
- ✅ 用户注册、登录、登出
- ✅ Token管理和刷新
- ✅ 密码加密和验证
- ✅ 参数验证和异常处理
- ✅ 完整的单元测试
- ✅ Swagger API文档

#### 数据库设计 (100%完成)
- ✅ 7张核心业务表
- ✅ 逻辑关联设计（无外键约束）
- ✅ 完整的索引优化
- ✅ 丰富的测试数据

#### 项目配置 (100%完成)
- ✅ Maven依赖配置
- ✅ Spring Boot配置
- ✅ Sa-Token认证配置
- ✅ 数据库连接配置

### 待开发功能 ⏳

- ⏳ 小鹅核心功能模块 (0%)
- ⏳ 商城基础功能模块 (0%)
- ⏳ 转赠功能模块 (0%)
- ⏳ React Native前端应用 (0%)

---

## 🗄️ 数据库初始化

### 1. 创建数据库和用户

```bash
# 连接MySQL
mysql -u root -p

# 执行数据库初始化脚本
source goose-app-backend/scripts/init-db.sql
```

### 2. 验证数据库创建

```sql
-- 检查数据库
SHOW DATABASES;
USE goose_app;

-- 检查表结构
SHOW TABLES;

-- 检查测试数据
SELECT COUNT(*) FROM users;      -- 应该有5条记录
SELECT COUNT(*) FROM products;   -- 应该有6条记录
SELECT COUNT(*) FROM geese;      -- 应该有7条记录
```

### 3. 测试数据说明

**用户账号** (密码都是: password123):
- testuser / <EMAIL> - 测试用户
- admin / <EMAIL> - 管理员
- alice / <EMAIL> - 爱丽丝
- bob / <EMAIL> - 鲍勃
- charlie / <EMAIL> - 查理

**商品数据**:
- 6种不同类型的小鹅商品
- 价格从129元到599元不等
- 包含新手推荐、热门推荐、限量版等分类

**小鹅数据**:
- 7只不同品种的小鹅
- 分属不同用户
- 不同的成长阶段和状态

---

## 🚀 项目启动步骤

### 1. 环境检查

```bash
# 检查Java版本
java -version

# 检查Maven版本
mvn -version

# 检查MySQL服务
mysql --version
```

### 2. 启动MySQL服务

```bash
# macOS (使用Homebrew)
brew services start mysql

# Ubuntu/Debian
sudo systemctl start mysql

# Windows
net start mysql
```

### 3. 启动Redis服务 (可选)

```bash
# macOS
brew services start redis

# Ubuntu/Debian
sudo systemctl start redis

# Windows
redis-server
```

### 4. 编译和启动项目

```bash
# 进入项目目录
cd goose-app-backend

# 清理和编译
mvn clean compile

# 运行测试
mvn test

# 启动应用
mvn spring-boot:run
```

### 5. 验证启动成功

访问以下地址确认服务正常：

- **应用首页**: http://localhost:8080
- **API文档**: http://localhost:8080/swagger-ui.html
- **健康检查**: http://localhost:8080/actuator/health

---

## 🧪 API测试指南

### 1. 用户注册测试

```bash
curl -X POST http://localhost:8080/api/v1/auth/register \
  -H "Content-Type: application/json" \
  -d '{
    "username": "newuser",
    "email": "<EMAIL>",
    "password": "password123",
    "confirmPassword": "password123",
    "nickname": "新用户"
  }'
```

### 2. 用户登录测试

```bash
curl -X POST http://localhost:8080/api/v1/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "username": "testuser",
    "password": "password123"
  }'
```

### 3. 获取用户信息测试

```bash
# 先登录获取token，然后使用token访问
curl -X GET http://localhost:8080/api/v1/users/profile \
  -H "satoken: Bearer YOUR_TOKEN_HERE"
```

### 4. 检查登录状态

```bash
curl -X GET http://localhost:8080/api/v1/auth/status \
  -H "satoken: Bearer YOUR_TOKEN_HERE"
```

---

## 📁 项目结构确认

### 后端项目结构 ✅ 完整

```
goose-app-backend/
├── pom.xml                     # Maven配置
├── README.md                   # 项目文档
├── TESTING.md                  # 测试指南
├── scripts/init-db.sql         # 数据库初始化脚本
└── src/
    ├── main/java/com/gooseapp/
    │   ├── GooseAppApplication.java    # 启动类
    │   ├── common/                     # 公共模块
    │   └── user/                       # 用户模块
    └── test/java/                      # 测试代码
```

### 配置文件 ✅ 完整

- ✅ application.yml - 主配置文件
- ✅ application-dev.yml - 开发环境配置
- ✅ 数据库连接配置
- ✅ Sa-Token认证配置
- ✅ 日志配置

---

## ⚠️ 常见问题解决

### 1. 数据库连接失败

```bash
# 检查MySQL服务状态
sudo systemctl status mysql

# 检查端口占用
netstat -an | grep 3306

# 重置MySQL密码
mysql -u root -p
ALTER USER 'goose_user'@'localhost' IDENTIFIED BY 'goose_password';
```

### 2. 端口占用问题

```bash
# 检查8080端口占用
lsof -i :8080

# 修改端口配置
# 在application.yml中修改server.port
```

### 3. Maven依赖下载失败

```bash
# 清理Maven缓存
mvn clean

# 强制更新依赖
mvn clean install -U
```

---

## ✅ 测试环境验收标准

### 功能验收 ✅

- [x] 数据库成功创建并包含测试数据
- [x] 应用成功启动，端口8080可访问
- [x] Swagger API文档可正常访问
- [x] 用户注册接口正常工作
- [x] 用户登录接口正常工作
- [x] Token认证机制正常工作
- [x] 所有单元测试通过

### 性能验收 ✅

- [x] 应用启动时间 < 30秒
- [x] API响应时间 < 500ms
- [x] 数据库查询性能良好

### 文档验收 ✅

- [x] API文档完整且可访问
- [x] 数据库设计文档完整
- [x] 测试指南文档完整

---

## 🎯 下一步计划

1. **小鹅核心功能开发** (预计2周)
   - 小鹅实体和Repository
   - 互动系统业务逻辑
   - 状态更新机制

2. **React Native前端开发** (预计3周)
   - 项目初始化和基础架构
   - 用户认证页面
   - 小鹅管理页面

3. **商城功能开发** (预计2周)
   - 商品管理API
   - 订单处理流程

4. **转赠功能开发** (预计1周)
   - 转赠业务逻辑
   - 分享功能集成

**总体预计完成时间**: 2025年2月底

---

## 📞 技术支持

如遇到环境搭建问题，请检查：
1. 日志文件: `logs/goose-app.log`
2. 测试报告: `target/surefire-reports/`
3. API文档: http://localhost:8080/swagger-ui.html

**测试环境准备完成！** 🎉
