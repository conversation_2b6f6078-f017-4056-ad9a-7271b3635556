# 后端开发进度跟踪

## 2025-06-12

### **特性：核心用户服务 (`UserService`) 重构**

-   **状态：** `已完成`
-   **主要贡献者：** 高级全栈工程师
-   **变更描述：**
    -   对原有的上帝服务 `UserServiceImpl` 进行了彻底的重构，遵循单一职责原则。
    -   成功将用户统计相关逻辑 (`getUserStats`, `updateUserStats`) 剥离，迁移至独立的 `UserStatsServiceImpl`。
    -   成功将用户登录日志记录逻辑 (`recordLoginLog`) 剥离，迁移至独立的 `UserLoginLogServiceImpl`。
    -   为新拆分出的两个服务编写了全面的单元测试，确保其逻辑的正确性和稳定性。
    -   通过端到端的集成测试，验证了重构后的注册与登录流程，确保核心功能未受影响并按预期工作。
-   **解决的关键问题：**
    -   在集成测试中，定位并修复了一系列因历史技术债导致的严重问题，包括：
        1.  **MyBatis绑定异常 (`BindingException`)**: 修正了自定义Repository与MyBatis-Plus在方法命名上的冲突（如 `findById` vs `selectById`）。
        2.  **组件依赖混乱**: 纠正了服务层错误依赖自定义的、权责不清的`UserRepository`，改为直接依赖功能明确的`UserMapper`。
    -   本次重构不仅优化了代码结构，更清理了潜藏的、可能导致未来更多bug的架构缺陷。