# 🦆 小鹅养成APP - MVP产品设计文档

## 📊 产品概述

**产品名称：** 小鹅萌宠  
**产品定位：** 农业+互联网的创新养成类社交电商平台  
**目标用户：** 都市白领、学生群体、送礼需求用户（18-35岁）  
**核心价值：** 通过数字化养成体验，将传统农业养殖转化为情感化互动商品

## 🎯 核心价值主张

- **买方价值：** 低成本体验真实养殖乐趣，获得情感陪伴
- **卖方价值：** 提升传统养殖附加值，建立稳定收入模式  
- **平台价值：** 构建养殖娱乐化生态，打造差异化竞争优势

## 👥 用户画像分析

### 主要用户群体
1. **城市养宠爱好者**（40%）- 无法实际养宠，寻求替代体验
2. **礼品消费者**（30%）- 寻找新颖有趣的礼品选择
3. **农业关注者**（20%）- 关心食品安全，希望了解养殖过程
4. **亲子教育群体**（10%）- 通过养成游戏教育孩子责任感

## 🛠️ 技术架构

### 前端技术栈
- **移动端框架：** React Native
- **状态管理：** TanStack React Query (负责服务端状态), Zustand (负责全局客户端状态)
- **UI框架：** (待定，遵循现有项目或引入 unistyles/tamagui 等现代化方案)
- **路由管理：** react-navigation
- **网络请求：** axios (由React Query封装)
- **本地存储：** MMKV或AsyncStorage

### 后端技术栈
- **开发框架：** Java + Spring Boot 3.0+
- **架构模式：** 分层架构（Controller-Service-Repository）
- **数据库：** MySQL 8.0（主库）+ Redis 6.0（缓存）
- **ORM框架：** MyBatis-Plus
- **安全框架：** Sa-Token
- **配置加密：** Jasypt
- **文档工具：** Swagger 3.0 / knife4j
- **构建工具：** Maven
- **日志框架：** Logback
- **数据删除策略：** 软删除（逻辑删除）

### 微信生态集成
- **微信登录：** 微信开放平台SDK
- **微信分享：** 微信SDK分享功能
- **深度链接：** URL Scheme + Universal Link
- **支付集成：** 微信支付SDK

### 基础设施
- **视频流服务：** 阿里云直播推流 / 腾讯云直播
- **文件存储：** 阿里云OSS / 腾讯云COS
- **CDN加速：** 阿里云CDN
- **服务器：** 阿里云ECS / 腾讯云CVM
- **数据库：** 阿里云RDS MySQL
- **缓存：** 阿里云Redis

### 硬件设备
- **摄像头：** 高清网络摄像头（支持夜视、移动侦测）
- **传感器：** 温湿度、空气质量监测设备
- **网络：** 稳定4G/WiFi网络，上行带宽≥2Mbps
- **补光设备：** LED补光灯（夜间拍摄）

## 🏗️ MVP功能架构

### 🔥 核心功能模块（P0 - 必须实现）

#### 1. 用户系统
- **微信一键登录**（主推方式）
  - 授权获取微信用户信息
  - 自动创建账户或绑定现有账户
  - 免输入手机号验证
- **手机号登录**（备选方式）
  - 短信验证码登录
  - 支持后续绑定微信
- **个人资料管理**
  - 头像、昵称编辑
  - 联系方式管理
- **账户安全**
  - 实名认证（转赠功能需要）
  - 密码找回机制

#### 2. 小鹅商城
- **商品展示**
  - 小鹅卡片列表（网格/列表视图）
  - 品种分类筛选（小白鹅、小灰鹅等）
  - 年龄阶段筛选（雏鹅、幼鹅）
  - 价格区间筛选
- **商品详情**
  - 小鹅基本信息（品种、年龄、健康状况）
  - 实时照片/视频预览
  - 价格信息
  - 养殖场介绍
- **购买流程**
  - 加入购物车
  - 订单确认
  - 支付集成（微信支付）
  - 订单状态跟踪

#### 3. 养成互动系统
- **我的小鹅列表**
  - 卡片式展示拥有的小鹅
  - 状态概览（健康度、饥饿度、清洁度）
  - 快速互动入口
- **小鹅详情页**
  - 基本信息展示（包含唯一编码code）
  - 实时状态数据
  - 互动功能区
  - 成长历程记录
- **基础互动功能**
  - 喂食操作（消耗饥饿度）
  - 喂水操作（消耗口渴度）
  - 清洁操作（提升清洁度）
  - 互动冷却机制（防止过度操作）
- **状态系统**
  - 健康值（0-100）
  - 饥饿度（0-100，自动递减）
  - 口渴度（0-100自动递减）
  - 清洁度（0-100，自动递减）
  - 心情值（基于互动频次）
  - 唯一编码（每只小鹅拥有独特的识别码，用于分享和追踪）

#### 4. 实时监控系统
- **视频直播**
  - 单路高清视频流
  - 支持多种清晰度切换
  - 网络自适应码率
- **拍照功能**
  - 手动截图保存
  - 自动美化处理
  - 本地相册存储
- **观看历史**
  - 观看时长统计
  - 历史截图记录
  - 观看时间记录

#### 5. 转赠系统
- **发起转赠**
  - 选择要转赠的小鹅
  - 选择分享渠道（微信好友/群聊/朋友圈）
  - 添加祝福语（可选）
  - 生成转赠链接/二维码
- **微信分享卡片**
  - 精美的分享卡片设计
  - 包含小鹅照片和基本信息
  - 自定义祝福语显示
  - 一键分享到微信
- **接收转赠**
  - 深度链接自动跳转APP
  - 未安装用户引导下载
  - 自动登录/注册流程
  - 确认接收界面
- **权益转移**
  - 自动切换小鹅所有权
  - 原主人权限收回
  - 新主人权限激活
  - 转赠状态同步
- **转赠记录**
  - 发送记录（已转赠状态）
  - 接收记录（来源信息）
  - 转赠历史查询

### ⭐ 重要功能模块（P1 - 优先实现）

#### 6. 社交互动
- **小鹅动态**
  - 系统自动生成动态（成长节点、互动记录）
  - 用户手动发布动态
  - 图片/视频/文字混合发布
- **互动功能**
  - 点赞、评论、转发
  - @好友功能
  - 话题标签系统
- **好友系统**
  - 关注/取消关注
  - 粉丝列表管理
  - 好友动态时间线

#### 7. 成长记录系统
- **自动记录**
  - 成长相册（定时自动截图）
  - 里程碑事件（第一次进食、满月、换毛等）
  - 互动历史时间轴
- **数据统计**
  - 总互动次数统计
  - 观看时长统计
  - 成长阶段进度
  - 健康度变化曲线
- **成长证书**
  - 阶段性成长证书生成
  - 个性化证书设计
  - 社交分享功能

#### 8. 消息通知系统
- **APP内消息**
  - 小鹅状态提醒（饥饿、生病、清洁）
  - 成长节点通知
  - 转赠相关通知
  - 系统公告通知
- **推送通知**
  - 重要状态变化推送
  - 定时关怀提醒
  - 活动通知推送
- **微信通知**
  - 服务号消息推送
  - 重要事件微信提醒

### 🎁 增值功能模块（P2 - 后期迭代）

#### 9. 个性化定制
- **小鹅命名**
  - 自定义小鹅名称
  - 名称重复性检查
  - 名称审核机制
- **个性化装饰**
  - 虚拟配件系统
  - 季节性装饰
  - 节日主题装饰
- **专属标签**
  - 成就标签系统
  - 个性签名
  - 等级徽章显示

#### 10. 会员系统
- **积分体系**
  - 互动获得积分
  - 积分商城兑换
  - 积分排行榜
- **等级特权**
  - VIP会员等级
  - 专属功能解锁
  - 优先客服支持
- **专属活动**
  - 会员专享小鹅
  - 线下农场参观
  - 专属客户群

## 📱 核心页面设计

### 主要页面结构
```
底部导航栏
├── 首页（发现）
│   ├── Banner轮播（活动/新品推荐）
│   ├── 推荐小鹅卡片
│   ├── 热门动态展示
│   └── 快捷功能入口
├── 商城
│   ├── 分类筛选导航
│   ├── 搜索功能
│   ├── 小鹅卡片网格展示
│   └── 购物车入口
├── 我的小鹅
│   ├── 小鹅列表（卡片视图）
│   ├── 状态概览
│   ├── 快速互动按钮
│   └── 实时监控入口
├── 动态
│   ├── 好友动态时间线
│   ├── 热门动态推荐
│   ├── 发布动态入口
│   └── 消息通知
└── 我的
    ├── 个人信息管理
    ├── 我的订单
    ├── 转赠记录
    ├── 积分中心
    └── 设置中心
```

### 关键页面详细设计

#### 小鹅详情页
```
┌─────────────────────────┐
│ ← 小白鹅2号        ⋯ 更多 │
│ 编号: XE240520001        │
├─────────────────────────┤
│ [实时视频画面区域]        │
│ 📹 直播中 👁 128人观看    │
├─────────────────────────┤
│ 基本信息：               │
│ 🦆 品种: 小白鹅          │
│ 📅 年龄: 3周            │
│ 📍 编码: XE240520001     │
├─────────────────────────┤
│ 状态信息：               │
│ ❤️ 健康度: ██████░░ 85%  │
│ 🍚 饥饿度: ████░░░░ 60%  │
│ 💧 口渴度: ███░░░░░ 45%  │
│ 🛁 清洁度: █████░░░ 75%  │
├─────────────────────────┤
│ 互动操作：               │
│ [🍚 喂食] [💧 喂水] [🛁 清洁] │
│                         │
│ 下次可操作: 2小时后       │
├─────────────────────────┤
│ [📸 拍照] [🎁 转赠] [📊 数据] │
└─────────────────────────┘
```

#### 转赠发起页
```
┌─────────────────────────┐
│ ← 转赠小鹅               │
├─────────────────────────┤
│ [小鹅头像] 小白鹅2号      │
│ 编码: XE240520001        │
│ 健康度: 85% | 年龄: 3周   │
├─────────────────────────┤
│ 选择转赠方式：           │
│                         │
│ [💬] 微信好友            │
│ [👥] 微信群聊            │
│ [🌟] 微信朋友圈          │
│ [🔗] 复制链接            │
├─────────────────────────┤
│ 添加祝福语：             │
│ ┌─────────────────────┐  │
│ │希望这只小鹅给你带来快乐！│  │
│ └─────────────────────┘  │
├─────────────────────────┤
│        [确认转赠]        │
└─────────────────────────┘
```

## 🔄 核心业务流程

### 用户注册登录流程
```
启动APP → 检查登录状态 → 未登录则显示登录页 →
选择微信登录 → 微信授权 → 获取用户信息 →
检查是否已注册 → 
├─ 已注册：直接登录成功
└─ 未注册：自动创建账户 → 完善个人信息 → 登录成功
```

### 小鹅购买流程
```
浏览商城 → 筛选小鹅 → 查看详情 → 确认购买 →
加入购物车 → 确认订单信息 → 选择支付方式 →
微信支付 → 支付成功 → 获得小鹅卡片 → 
自动跳转到"我的小鹅" → 开始养成体验
```

### 转赠完整流程
```
发起转赠：
选择小鹅 → 点击转赠 → 选择分享方式 → 
添加祝福语 → 生成分享内容 → 分享到微信

接收转赠：
微信中点击分享卡片 → 
├─ 未安装APP：跳转下载页 → 安装APP → 携带参数启动
└─ 已安装APP：直接唤起APP
→ 检查登录状态 →
├─ 未登录：引导微信登录
└─ 已登录：直接进入接收页面
→ 确认接收 → 权益转移 → 接收成功通知

权益转移：
系统检测接收确认 → 验证转赠有效性 → 
更新小鹅所有权 → 原主人权限收回 → 
新主人权限激活 → 双方收到通知
```

### 日常养成流程
```
打开"我的小鹅" → 查看小鹅状态 → 
判断需求（饥饿/口渴/脏污） → 执行相应互动 →
消耗冷却时间 → 状态更新 → 获得成长值 →
记录互动历史 → 检查是否达成成长节点 →
如达成则触发成长事件 → 系统自动拍照纪念
```

## 💰 商业模式设计

### 收入来源
1. **小鹅卡片销售**（主要收入 - 70%）
   - 基础小鹅卡片：99-299元
   - 限量版小鹅：399-599元
   - 节日特别版：299-999元

2. **增值服务**（次要收入 - 20%）
   - 加速成长道具：9.9-29.9元
   - 特殊饲料包：19.9-49.9元
   - 个性化装饰：6.6-19.9元

3. **会员订阅**（稳定收入 - 8%）
   - 月度会员：19.9元/月
   - 年度会员：199元/年
   - 终身会员：999元

4. **广告合作**（补充收入 - 2%）
   - 品牌合作推广
   - 信息流广告
   - 开屏广告

### 成本结构
- **硬件成本：** 摄像头、传感器、网络设备
- **运营成本：** 服务器、带宽、存储、人力
- **养殖成本：** 饲料、兽医、场地租赁
- **推广成本：** 用户获取、市场营销

## 📈 MVP验证指标

### 产品关键指标
- **用户注册转化率：** >15%
- **首次购买转化率：** >8%
- **日活跃用户率（DAU/MAU）：** >60%
- **平均使用时长：** >10分钟/日
- **转赠成功率：** >80%
- **用户留存率：** 
  - 次日留存：>70%
  - 7日留存：>40%
  - 30日留存：>20%

### 业务关键指标
- **小鹅存活率：** >95%
- **平均订单价值（AOV）：** >180元
- **客户生命周期价值（LTV）：** >500元
- **获客成本（CAC）：** <100元
- **月度复购率：** >25%

### 技术关键指标
- **视频流稳定性：** >99%
- **APP崩溃率：** <0.1%
- **API响应时间：** <500ms
- **客服响应时间：** <2小时
- **用户满意度：** >4.5分（5分制）


## 🔒 技术架构细节

### 后端技术栈详细说明

#### MyBatis-Plus配置
- **自动填充：** 创建时间、更新时间、创建人、更新人自动填充
- **逻辑删除：** 通过is_deleted字段实现软删除
- **分页插件：** 内置分页功能，支持多数据库
- **代码生成：** 自动生成Entity、Mapper、Service、Controller

#### Sa-Token安全框架
- **轻量级：** 相比Spring Security更轻量，学习成本低
- **功能丰富：** 登录验证、权限验证、Session会话、单点登录
- **微信集成：** 天然支持微信登录token管理
- **多端登录：** 支持同一账号多设备登录管理

#### 软删除策略
- **统一标准：** 所有表都包含is_deleted字段
- **查询过滤：** MyBatis-Plus自动过滤已删除数据
- **数据恢复：** 支持数据恢复功能
- **审计追踪：** 保留完整的数据变更历史

### 后端模块划分

#### 用户模块（User Module）
```
com.gooseapp.user
├── controller/     # 用户相关接口
├── service/        # 业务逻辑层
├── mapper/         # MyBatis-Plus数据访问层
├── entity/         # 实体类（包含公共字段）
└── dto/           # 数据传输对象
```

#### 商城模块（Shop Module）
```
com.gooseapp.shop
├── controller/     # 商城接口
├── service/        # 商城业务逻辑
├── mapper/         # 商城数据访问
├── entity/         # 商品、订单实体
└── dto/           # 商城相关DTO
```

#### 小鹅养成模块（Goose Module）
```
com.gooseapp.goose
├── controller/     # 小鹅管理接口
├── service/        # 养成逻辑
├── mapper/         # 小鹅数据访问
├── entity/         # 小鹅相关实体（包含code字段）
├── dto/           # 小鹅相关DTO
└── engine/        # 状态计算引擎
```

#### 转赠模块（Gift Module）
```
com.gooseapp.gift
├── controller/     # 转赠接口
├── service/        # 转赠业务逻辑
├── mapper/         # 转赠数据访问
├── entity/        # 转赠记录实体
└── dto/           # 转赠相关DTO
```

### 数据库设计

#### 核心表结构
```sql
-- 用户表
CREATE TABLE users (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    openid VARCHAR(100) UNIQUE COMMENT '微信openid',
    nickname VARCHAR(50) COMMENT '用户昵称',
    avatar_url VARCHAR(500) COMMENT '头像URL',
    phone VARCHAR(20) COMMENT '手机号',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    create_user BIGINT COMMENT '创建人ID',
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    update_user BIGINT COMMENT '更新人ID',
    is_deleted TINYINT DEFAULT 0 COMMENT '是否删除 0-未删除 1-已删除'
) COMMENT '用户表';

-- 小鹅表
CREATE TABLE geese (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    code VARCHAR(32) UNIQUE NOT NULL COMMENT '小鹅唯一编码',
    owner_id BIGINT COMMENT '拥有者ID',
    name VARCHAR(50) COMMENT '小鹅名称',
    breed VARCHAR(20) COMMENT '品种',
    birth_date DATE COMMENT '出生日期',
    health_value INT DEFAULT 100 COMMENT '健康值(0-100)',
    hunger_value INT DEFAULT 100 COMMENT '饥饿值(0-100)',
    thirst_value INT DEFAULT 100 COMMENT '口渴值(0-100)',
    cleanliness_value INT DEFAULT 100 COMMENT '清洁值(0-100)',
    status ENUM('HEALTHY', 'SICK', 'DEAD') DEFAULT 'HEALTHY' COMMENT '状态',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    create_user BIGINT COMMENT '创建人ID',
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    update_user BIGINT COMMENT '更新人ID',
    is_deleted TINYINT DEFAULT 0 COMMENT '是否删除 0-未删除 1-已删除'
) COMMENT '小鹅表';

-- 订单表
CREATE TABLE orders (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    order_no VARCHAR(32) UNIQUE NOT NULL COMMENT '订单号',
    user_id BIGINT COMMENT '用户ID',
    goose_id BIGINT COMMENT '小鹅ID',
    amount DECIMAL(10,2) COMMENT '订单金额',
    status ENUM('PENDING', 'PAID', 'DELIVERED', 'CANCELLED') COMMENT '订单状态',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    create_user BIGINT COMMENT '创建人ID',
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    update_user BIGINT COMMENT '更新人ID',
    is_deleted TINYINT DEFAULT 0 COMMENT '是否删除 0-未删除 1-已删除'
) COMMENT '订单表';

-- 转赠记录表
CREATE TABLE gift_records (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    goose_id BIGINT COMMENT '小鹅ID',
    sender_id BIGINT COMMENT '赠送者ID',
    receiver_id BIGINT COMMENT '接收者ID',
    gift_code VARCHAR(100) UNIQUE COMMENT '转赠码',
    message TEXT COMMENT '祝福语',
    status ENUM('PENDING', 'RECEIVED', 'EXPIRED') COMMENT '转赠状态',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    create_user BIGINT COMMENT '创建人ID',
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    update_user BIGINT COMMENT '更新人ID',
    is_deleted TINYINT DEFAULT 0 COMMENT '是否删除 0-未删除 1-已删除'
) COMMENT '转赠记录表';

-- 小鹅互动记录表
CREATE TABLE goose_interactions (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    goose_id BIGINT COMMENT '小鹅ID',
    user_id BIGINT COMMENT '用户ID',
    interaction_type ENUM('FEED', 'WATER', 'CLEAN') COMMENT '互动类型',
    interaction_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '互动时间',
    effect_value INT COMMENT '效果值',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    create_user BIGINT COMMENT '创建人ID',
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    update_user BIGINT COMMENT '更新人ID',
    is_deleted TINYINT DEFAULT 0 COMMENT '是否删除 0-未删除 1-已删除'
) COMMENT '小鹅互动记录表';
```

这份完整的MVP产品设计文档涵盖了从技术架构到业务流程的所有关键内容，为开发团队提供了清晰的指导方向。你觉得还有哪些方面需要补充或调整？
