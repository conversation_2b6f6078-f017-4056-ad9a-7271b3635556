# 🦆 小鹅养成APP - UI/UX设计文档 (v2.0)

## 📋 文档信息

**项目名称：** 小鹅萌宠APP
**设计版本：** v2.0 - "Gummy Glassmorphism"
**创建日期：** 2025年6月11日
**设计师：** 高级全栈工程师
**文档状态：** 设计方案完成

---

## 🎯 设计概述

### 设计目标
- **降低学习成本**：新用户能在30秒内理解核心功能
- **提升情感投入**：通过设计增强用户与小鹅的情感连接
- **优化转化路径**：从浏览到购买的流程顺畅无阻
- **强化社交传播**：转赠功能成为自然的分享行为

### 核心设计理念 (v2.0)
1.  **情感化设计:** 通过可爱的视觉元素和温暖的色彩建立情感连接。
2.  **直观易用:** 简化复杂功能，降低用户认知负荷。
3.  **社交友好:** 优化分享体验，促进病毒式传播。
4.  **沉浸体验:** 通过实时视频和互动增强真实感。
5.  **陪伴与惊喜 (v2.0 新增):** UI应具备生命感，通过动态反馈和细节彩蛋，为用户创造持续的愉悦感。

---

## 🎨 设计系统规范 (v2.0 - Gummy Glassmorphism)

### 核心设计哲学：软糖玻璃 (Gummy Glassmorphism)

我们追求的不是冰冷的科技感，而是**有温度的生命感**。全新的设计系统融合了**玻璃的通透**与**软糖的Q弹**，创造出一种独特、治愈且充满惊喜的视觉与交互体验。

- **通透感 (Transparency):** 通过背景模糊和半透明材质，构建富有深度的空间层次感，让UI感觉轻盈、不压抑。
- **光影感 (Light & Shadow):** 精致的光源模拟，让元素边缘产生微妙的光晕和柔和的内阴影，仿佛光线穿透玻璃。
- **软糯感 (Soft & Gummy):** 大量使用圆润、饱满的圆角，配合弹性动画，让每一次点击都像是在触摸一颗软糖，充满趣味和满足感。
- **呼吸感 (Breathing):** 界面元素会根据情境（如小鹅的情绪、一天中的时间）产生色彩、光影上的微妙变化，让整个应用充满生命力。

### 色彩体系：情感与治愈

告别传统的功能性色彩，我们的新色板源于**清晨的阳光、雨后的天空和温暖的拥抱**。

#### 核心调色板
```css
/* 主色调：晨曦金 & 软糖黄 (温暖、希望、活力) */
--gg-color-primary-gold: #FFD700;
--gg-color-primary-gummy: #FFEB3B;

/* 辅助色：天青蓝 & 呼吸蓝 (宁静、纯净、陪伴) */
--gg-color-secondary-sky: #87CEEB;
--gg-color-secondary-breath: #E3F2FD;

/* 点缀色：珊瑚粉 & 治愈绿 (关爱、健康、惊喜) */
--gg-color-accent-coral: #FFA07A;
--gg-color-accent-heal: #98FB98;
```

#### 中性与背景色
```css
/* 定义具有微妙色偏的“有色”中性色 */
--gg-color-text-primary: #1F2937;   /* 主文本，近黑偏蓝 */
--gg-color-text-secondary: #6B7280; /* 次要文本，中灰偏蓝 */
--gg-color-text-tertiary: #9CA3AF;  /* 辅助文本，浅灰偏蓝 */

/* 背景色使用柔和的、带有渐变的颜色，模拟环境光 */
--gg-background-day: linear-gradient(180deg, #E3F2FD 0%, #FFFDE7 100%); /* 白天：蓝天到暖阳 */
--gg-background-night: linear-gradient(180deg, #1F2937 0%, #37474F 100%); /* 夜晚：深空到微光 */
```

#### 状态与情感渐变
```css
/* 状态条不再是简单的色块，而是流光溢彩的能量条 */
--gg-gradient-health: linear-gradient(90deg, #98FB98 0%, #32CD32 100%); /* 健康：生命绿 */
--gg-gradient-hunger: linear-gradient(90deg, #FFA07A 0%, #FF7F50 100%); /* 饥饿：珊瑚橙 */
--gg-gradient-clean: linear-gradient(90deg, #87CEEB 0%, #4682B4 100%);  /* 清洁：天空蓝 */
```

### 字体规范：亲和与清晰

我们选择圆润、易读且充满亲和力的字体。

#### 字体族
```css
/* 优先使用圆体，营造可爱、友好的氛围 */
font-family: 'MiSans', 'Varela Round', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;

/* 数字和关键信息使用更现代、清晰的字体 */
font-family: 'Inter', 'DIN Alternate', 'Helvetica Neue', Arial, sans-serif;
```

#### 字体层级与大小
```css
/* 更少的字号变化，通过字重和色彩来区分层级 */
--gg-font-size-display: 36px;     /* 核心展示信息，如小鹅名字 */
--gg-font-size-title: 22px;       /* 页面标题 */
--gg-font-size-body-large: 17px;  /* 主要正文/按钮 */
--gg-font-size-body: 15px;        /* 次要正文 */
--gg-font-size-caption: 13px;     /* 辅助说明文字 */

/* 定义字重变量，用于强调 */
--gg-font-weight-regular: 400;
--gg-font-weight-medium: 500;
--gg-font-weight-bold: 700;
```

### 布局规范：呼吸感与层次感

#### 圆角：万物皆可圆润
```css
/* 更大、更夸张的圆角，突出“软糖”感 */
--gg-radius-full: 9999px;   /* 全圆角，用于药丸形状按钮 */
--gg-radius-blob: 32px;     /* 卡片、模态框，类似不规则的“果冻” */
--gg-radius-gummy: 20px;    /* 按钮、输入框 */
--gg-radius-soft: 12px;     /* 小标签、头像 */
```

#### 间距：赋予呼吸空间
```css
/* 使用8的倍数作为间距体系，更和谐统一 */
--gg-spacing-xs: 4px;
--gg-spacing-sm: 8px;
--gg-spacing-md: 16px;
--gg-spacing-lg: 24px;
--gg-spacing-xl: 32px;
--gg-spacing-xxl: 48px;

/* 页面边距增加，让内容呼吸 */
--gg-page-padding: 24px;
```

### 阴影系统：光影的艺术
```css
/* 抛弃传统外阴影，改用更细腻的内阴影、底板发光和弥散阴影 */

/* 玻璃卡片效果 */
--gg-shadow-glass: inset 0 1px 1px 0 rgba(255, 255, 255, 0.3); /* 顶部内高光，模拟光照 */

/* 软糖按钮效果 */
--gg-shadow-gummy-idle: 0 4px 8px -2px rgba(25, 40, 50, 0.1), 0 2px 4px -2px rgba(25, 40, 50, 0.06); /* 更柔和、弥散的阴影 */
--gg-shadow-gummy-press: 0 1px 2px -1px rgba(25, 40, 50, 0.1), 0 1px 2px -1px rgba(25, 40, 50, 0.06); /* 按下时阴影变小 */

/* 底板发光效果，用于悬浮元素 */
--gg-glow-primary: 0 0 20px 0 rgba(255, 235, 59, 0.5);  /* 主色光晕 */
--gg-glow-accent: 0 0 20px 0 rgba(255, 160, 122, 0.5); /* 点缀色光晕 */
```

---

## 🎨 视觉与动效规范 (v2.0 - 陪伴与惊喜)

### 图标系统：会呼吸的图标

图标不再是静态符号，而是情感的延伸。

#### 图标风格
- **风格:** **动态3D图标 (Animated 3D Icons)**。使用柔和、圆润的3D粘土风格，图标带有微妙的悬浮和呼吸动效。
- **状态响应:** 图标会根据小鹅的状态或操作反馈改变形态。例如，当小鹅开心时，“我的鹅”图标会轻轻摇摆；点击“喂食”后，图标会变成一个满足的表情。
- **色彩:** 采用调色板中的情感色彩，并带有柔和的渐变和光泽。

#### 核心图标库 (理念重构)
- **导航图标 (动态):**
    - **发现:** 一个望远镜，镜片里有流光划过。
    - **商城:** 一个Q弹的购物袋，每次点击都会晃动。
    - **我的鹅:** 小鹅的3D头像，会模仿APP里小鹅的实时表情（开心、睡觉等）。
    - **动态:** 一叠正在冒出爱心气泡的信纸。
    - **我的:** 一个可以由用户自定义形象的3D小人偶。
- **功能图标 (状态响应):**
    - **健康:** 一颗跳动的爱心，跳动速度随健康值变化。
    - **喂食:** 一个饭碗，喂食后会短暂出现蒸汽动画。
    - **清洁:** 一滴水珠，清洁后会迸发出闪亮的星星。
    - **转赠:** 一个礼物盒，长按时会轻轻打开一条缝，透出光芒。

### 插画规范：有故事的场景

插画是讲述“小鹅与你”故事的窗口。

- **风格定位:** **温暖的3D粘土风 (Warm 3D Clay)**，结合柔和的光影和细腻的纹理，创造一个童话般的微缩世界。
- **场景化:** 所有插画都是一个完整的故事场景。例如，“空状态”不再是一个哭泣的小鹅，而是小鹅打着手电筒在空荡荡的房间里探索的场景，配文“这里好像还没有邻居，快去发现新伙伴吧！”。
- **情感化:** 小鹅的形象设计更强调**表情和肢体语言**。它会有更多生动的姿态，如好奇地歪头、满足地打滚、期待地摇摆。

### 动效规范：充满惊喜的微交互

我们致力于创造让用户会心一笑的“Aha! Moment”。

#### 动画原则：物理与魔法
- **物理隐喻 (Physics-based):** 动画遵循现实世界的物理规律（重力、弹性、惯性），感觉自然不突兀。我们大量使用**弹性缓动 (Spring Animation)** 来模拟“软糖”质感。
- **魔法时刻 (Magic Moments):** 在关键节点，动画会打破常规，创造惊喜。例如，升级时的庆祝动画不是简单的彩带，而是从屏幕底部长出一棵绚丽的生命之树。

#### 关键动效重构
- **状态条流动效果:** 进度条不再是机械地填充，而是**带有粘性的液体流动效果**，仿佛在注入真实的能量。
- **“果冻”按钮:** 点击按钮时，按钮会像果冻一样被“压扁”并迅速回弹，产生有趣的触感。
- **粒子系统:** 积极的操作（如喂食、清洁）会迸发出柔和、多彩的光粒子，消极的状态会飘散出灰色的尘埃粒子，将状态变化视觉化、情感化。
- **天气系统:** APP的背景和光影会根据真实世界的天气变化而改变。下雨天，屏幕上会有凝结的水汽；晴天，光线会更加明亮。
- **入场动画:** 打开APP时，不是生硬地加载页面，而是你的专属小鹅从屏幕一角探出头来，好奇地张望一下，然后跳到主界面，欢迎你的到来。

---

## 🏗️ 信息架构设计 (继承自v1.0)

### 导航结构

#### 底部导航栏
```
┌─────────────────────────────────────┐
│ 🏠发现  🛒商城  🦆我的鹅  💬动态  👤我的 │
└─────────────────────────────────────┘
```

**导航说明：**
- **发现**：推荐内容、活动信息、热门动态
- **商城**：小鹅购买、分类浏览、搜索筛选
- **我的鹅**：已拥有的小鹅、快速互动、状态监控
- **动态**：社交时间线、好友动态、发布内容
- **我的**：个人中心、设置、订单记录

#### 全局底部导航栏 (Global Tab Bar) - V2 设计规范

**1. 组件定位与行为:**
-   **定位:** 始终固定在移动端视图的底部，覆盖在页面内容之上。
-   **安全区域:** 底部会留出适当的内边距，以适配现代智能手机的全面屏手势区域 (safe area)。

**2. 视觉风格 (Gummy Glassmorphism):**
-   **背景:** 使用半透明的磨砂玻璃效果 (`background: rgba(255, 255, 255, 0.6); backdrop-filter: blur(20px);`)，能隐约透出底下滚动的内容。
-   **边框:** 顶部有一条 `1px` 的、几乎看不见的白色内阴影 (`box-shadow: inset 0 1px 0 0 rgba(255, 255, 255, 0.2);`)，模拟玻璃的边缘反光。
-   **形状:** 整体是一个圆角矩形，与屏幕边缘留有微小间距，呈现出悬浮感。

**3. 图标与文案:**
| 索引 | 图标 (Font Awesome) | 文案 | 目标页面 |
| :--- | :--- | :--- | :--- |
| 1 | `fa-compass` | 发现 | `discover.html` |
| 2 | `fa-store` | 商城 | `shop.html` |
| 3 | `fa-egg` | 我的鹅 | `my-geese.html` |
| 4 | `fa-users` | 动态 | `social-feed.html` |
| 5 | `fa-user-circle`| 我的 | `profile.html` |

**4. 状态与交互:**
-   **默认状态:** 图标和文案统一使用次要文本颜色 (`--gg-color-text-secondary`)。
-   **触摸/悬停状态:** 被触摸的图标会轻微放大 (`transform: scale(1.1);`) 并带有弹性动画。
-   **激活状态:**
    -   图标和文案的颜色变为晨曦金 (`--gg-color-primary-gold`)。
    -   图标下方会出现一个药丸形状的、带有主色光晕 (`--gg-glow-primary`) 的背景板，强调当前所在页面。
    -   图标会播放一个微妙的、可爱的循环动画（例如“我的鹅”图标会轻轻摇晃）。

#### 页面层级结构
```
Level 1: 主导航页面 (5个)
├── 发现页
├── 商城页
├── 我的鹅页
├── 动态页
└── 我的页

Level 2: 核心功能页面 (8个)
├── 小鹅详情页
├── 商品详情页
├── 转赠流程页
├── 订单流程页
├── 视频全屏页
├── 拍照分享页
├── 成长数据页
└── 设置页面

Level 3: 辅助功能页面 (10+个)
├── 个人资料编辑
├── 帮助中心
├── 客服咨询
├── 订单详情
├── 支付页面
├── 地址管理
├── 消息通知
├── 隐私设置
├── 关于我们
└── 意见反馈
```

---

## 🤖 AI设计提示词 (AI Design Prompts)
### 主提示词 (Master Prompt) - 风格基石

**核心风格指令:**
`gummy glassmorphism, 3d clay style, soft-touch, translucent, frosted glass effect, deep depth of field, gentle light and shadow, pastel color palette, rounded shapes, blob-like forms, breathing animation, UI for a pet-raising mobile app, ultra-detailed, 8k, photorealistic, trending on Artstation`

**色彩体系指令:**
`Color palette based on morning sunlight and clear sky: primary colors are warm gold (#FFD700) and gummy yellow (#FFEB3B); secondary colors are sky blue (#87CEEB) and breathing blue (#E3F2FD); accent colors are coral pink (#FFA07A) and healing green (#98FB98). The overall tone is soft, warm, and healing.`

**字体与布局指令:**
`Typography uses a friendly rounded font like 'Varela Round' for main text, and a clean modern font like 'Inter' for numbers. The layout is spacious and airy, with large rounded corners (32px radius for cards), and uses a base-8 spacing system.`

**动效与质感指令:**
`Incorporate subtle breathing animations. All interactive elements have a gummy, bouncy feel, achieved through spring animations. Progress bars look like viscous liquid filling up. Buttons have a jelly-like press effect. Use particle effects (soft light particles for positive feedback) to enhance interactions.`

### 全量页面设计提示词 (Full Page Prompts)

**通用前缀 (Apply to all page prompts):**
`Master_Prompt_V2, UI design for a mobile app screen, showing [Page_Name], gummy glassmorphism style.`

#### Level 0: 认证页面 (Authentication) - V2 重构

**核心理念:** 无缝、低摩擦的用户入门体验。移除独立的注册流程，采用“首次登录即注册”的模式。

1.  **登录页 (Login Page):**
    `A friendly and modern login screen designed for speed. A large, cute, 3D clay-style goose mascot floats playfully at the top. The main interactive area is a single frosted glass panel. It features a primary input field for a mobile number, paired with a 'Get Code' gummy button. Below it is an input for the 6-digit SMS code. The main CTA is a large, glowing, primary-colored gummy button that says 'Login / Register'. Below the main panel, there's a divider labeled 'Or continue with'. At the bottom, two large, pill-shaped, secondary gummy buttons offer 'One-Tap Login' and 'WeChat Login', each with a corresponding 3D icon. The background is a warm, inviting gradient with subtly animated, floating light orbs.`

#### Level 1: 主导航页面

1.  **发现页 (Discover Page):**
    `A vibrant and dynamic layout. At the top, a large, translucent glass banner with a soft glow showcases featured events. Below, a horizontal scrolling list of blob-shaped cards presents "Today's Recommended Geese," each with a cute 3D clay goose illustration. Further down, a masonry grid displays dynamic content from friends, with each postaterials on a frosted glass card. The background is a gentle day-themed gradient, with subtle, floating, out-of-focus light orbs.`

2.  **商城页 (Shop Page):**
    `A clean and inviting grid of 2x2 soft, blob-like cards, each showcasing a different 3D clay goose. The cards have a frosted glass effect and a subtle inner glow. Each goose card has a prominent price tag and a small, animated 'new' or 'popular' label. At the top, there is a search bar with a pill shape and large rounded corners. The background features floating, out-of-focus, soft-colored geometric shapes.`

3.  **我的鹅页 (My Geese Page) - V2 交互重构:**
    `An immersive, full-screen horizontal slider (carousel). Each slide is a "card" representing one goose, featuring a large, interactive 3D model of the goose against a backdrop that reflects its personality or mood. The user can seamlessly swipe between their geese. Below the 3D model, essential stats and interaction buttons (Feed, Clean) are presented on a translucent glass panel. A subtle, glowing pagination indicator at the bottom shows the user their current position in the collection. This design removes the two-step selection process, creating a more fluid and direct browsing experience.`

4.  **动态页 (Social Feed Page):**
    `A vertical feed of user-generated content. Each post is a self-contained, rounded, frosted glass card with plenty of padding. Photos and videos have rounded corners. Interactive elements like 'like' and 'comment' are bouncy, gummy-style icons that animate playfully on tap. A floating action button (FAB) in the bottom-right, shaped like a chubby bird, allows users to create a new post.`

5.  **我的页 (Profile Page):**
    `A serene and organized layout. At the top, a large circular area displays the user's 3D avatar, which subtly mimics the user's phone tilt. Below, a series of horizontal, pill-shaped, translucent menu items (e.g., 'Settings', 'My Orders') are stacked with generous spacing. Each item has a soft, 3D clay-style icon. The background is a calm, personalizable color gradient.`

#### Level 2: 核心功能页面

6.  **小鹅详情页 (Goose Detail Page):**
    `A large, translucent glass card dominates the top half, showing a real-time video of a cute 3D clay-style goose. Below, several blob-shaped, semi-transparent cards display the goose's stats (health, hunger) using viscous liquid progress bars. At the bottom, there are four large, bouncy, gummy-style buttons for interactions like 'Feed' and 'Clean'. The background is a soft gradient of sky blue and warm yellow, simulating a day scene. The entire UI feels alive and responsive.`

7.  **商品详情页 (Product Detail Page):**
    `An immersive view focusing on a single goose. A large, high-quality 3D render of the goose is the centerpiece, which users can rotate 360 degrees. Below the model, key information like 'age' and 'personality' is presented in soft, pill-shaped tags. The 'Add to Cart' button is a large, glowing, gummy button at the bottom of the screen that pulses gently.`

8.  **转赠流程页 (Gifting Flow Page):**
    `A multi-step modal view. The main card is a large, frosted glass panel where the user can type a message. The 'Confirm Gift' button is a prominent, gummy button that glows softly. When the user confirms, a 3D animated gift box appears, ties itself with a ribbon, and flies off the screen. The background is blurred, focusing the user's attention on the gifting task. The overall experience feels warm, personal, and ceremonial.`

9.  **小鹅购买流程页 (Goose Purchase Flow Page):**
    `A streamlined and secure checkout process presented in a modal overlay. The top of the card shows a small, cute 3D model of the selected goose. Below, there are clear sections for 'Payment Method' and 'Order Summary', displayed on frosted glass panels. The 'Confirm Purchase' button is the most prominent element: a large, glowing, primary-colored gummy button at the bottom. Upon successful purchase, the button transforms into a 'Go to My Geese' link, and confetti particles burst from the center of the screen.`

10. **订单流程页 (Order Flow Page):**
    `A clean, step-by-step wizard. Each step (Address, Payment, Confirmation) is a clear, focused section on a frosted glass panel. Input fields are soft, rounded, and have a gentle inner shadow. The 'Next' button is a bright, gummy button. The progress indicator at the top is a series of connected, glowing orbs.`

10. **视频全屏页 (Full-Screen Video Page):**
    `An immersive, distraction-free view. The video takes up the entire screen. UI controls (play, pause, timeline) are overlaid on a translucent, blurred bar that fades out after a few seconds of inactivity. All icons are soft, white, and have a subtle glow to ensure visibility without being intrusive.`

11. **拍照分享页 (Photo & Share Page):**
    `A creative and fun interface. The camera view is live in the background. On top, users can drag and drop animated 3D stickers (hats, glasses for the goose). The shutter button is a large, pulsating, gummy circle. After taking a photo, the sharing options appear as colorful, bouncy icons for different social media platforms.`

12. **成长数据页 (Growth Data Page):**
    `A visually engaging data visualization screen. Instead of boring charts, data is shown as a growing, animated 3D plant. Each leaf or flower represents a milestone (e.g., weight gain, new skill). Tapping a leaf reveals detailed stats in a small, frosted glass pop-up. The page feels more like a garden than a dashboard.`

13. **设置页面 (Settings Page):**
    `A calm and clear list of options. Each setting is a toggle switch or a menu item within a horizontal, pill-shaped, translucent container. The toggle switches have a satisfying, jelly-like animation when flipped. Section headers are soft and understated. The page prioritizes clarity and ease of use over dense information.`

#### Level 3: 关键辅助页面

14. **个人资料编辑 (Edit Profile Page):**
    `A simple and friendly form. Input fields for 'Username' and 'Bio' are presented on a frosted glass card. The 'Profile Picture' is a large, circular area that, when tapped, initiates a fun, popping animation to open the image picker. The 'Save' button is a soft, gummy button that provides a haptic 'click' on success.`

15. **消息通知 (Notifications Page):**
    `A clean, vertical list of notifications. Each notification (e.g., 'Your goose is hungry!') is a separate, rounded, translucent card with a relevant 3D icon. Unread notifications have a soft, pulsing glow on their left edge. Swiping a notification reveals 'delete' and 'mute' actions with bouncy animations.`