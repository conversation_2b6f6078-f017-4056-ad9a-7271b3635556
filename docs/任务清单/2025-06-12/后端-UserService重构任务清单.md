# 后端-UserService重构任务清单 (2025-06-12)

## 目标
将 `UserServiceImpl` 中的非核心业务逻辑剥离，拆分“上帝服务”，提高代码内聚性和可维护性。

---

## 任务分解

### 阶段一：服务拆分

-   [x] **任务 1.1:** 创建 `UserStatsService`
    -   [x] 创建接口 `UserStatsService.java`
    -   [x] 创建实现 `UserStatsServiceImpl.java`
    -   [x] 将 `getUserStats` 和 `updateUserStats` 方法从 `UserServiceImpl` 迁移至 `UserStatsServiceImpl`。
    -   [x] 在 `UserServiceImpl` 中注入并调用 `UserStatsService`。

-   [x] **任务 1.2:** 创建 `UserLoginLogService`
    -   [x] 创建接口 `UserLoginLogService.java`
    -   [x] 创建实现 `UserLoginLogServiceImpl.java`
    -   [x] 将 `recordLoginLog` 方法从 `UserServiceImpl` 迁移至 `UserLoginLogServiceImpl`。
    -   [x] 在 `UserServiceImpl` 中注入并调用 `UserLoginLogService`。

### 阶段二：测试与验证

-   [x] **任务 2.1:** 为新服务编写单元测试
    -   [x] 为 `UserStatsServiceImpl` 编写单元测试。
    -   [x] 为 `UserLoginLogServiceImpl` 编写单元测试。
-   [x] **任务 2.2:** 集成验证
    -   [x] 运行应用并手动测试登录、注册等流程，确保重构未破坏原有功能。

### 阶段三：文档闭环

-   [x] **任务 3.1:** 更新 `docs/项目架构与结构分析报告.md`
    -   [x] 在报告中记录本次重构的完成情况。
-   [x] **任务 3.2:** 更新 `docs/后端开发进度跟踪.md`
    -   [x] 添加本次重构的进度记录。