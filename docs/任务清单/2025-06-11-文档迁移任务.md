# 任务清单：2025-06-11

**负责人**: 高级全栈开发顾问
**状态**: 进行中

---

## 目标

将项目文档结构对齐 `fullstack-pro` 模式中定义的新规范，并为下一阶段的重构工作做好准备。

---

## 任务分解

### P0: 文档结构整理 (已完成)

-   [x] **(已完成)** 在项目根目录创建 `/docs` 文件夹。
-   [x] **(已完成)** 迁移 `前端开发进度跟踪.md` 到 `/docs` 目录。
    -   [x] 读取源文件内容
    -   [x] 在目标位置写入新文件
    -   [x] 删除根目录的旧文件
-   [x] **(已完成)** 迁移 `项目架构与结构分析报告.md` 到 `/docs` 目录。
    -   [x] 读取源文件内容
    -   [x] 在目标位置写入新文件
    -   [x] 删除根目录的旧文件
-   [x] **(已完成)** 创建或更新 `README.md`，在其中说明项目当前的重构状态和新的文档结构。

### P1: 下一步行动计划

-   [ ] **(部分完成)** **后端**: `UserServiceImpl` 重构已完成一部分。
    -   [x] **(已完成)** **废除内存分页**：所有分页查询已迁移至数据库层面。
    -   [x] **(已完成)** **实现Repository模式**：通过组合代替继承，实现业务与持久层的解耦。
    -   [ ] **(待办)** **拆分上帝服务**：`UserServiceImpl` 中非核心用户的逻辑（如统计、复杂查询等）仍需抽离。
-   [ ] **(未开始)** **前端**: `LoginScreen.tsx` 的重构任务尚未开始。
    -   [ ] **(待办)** 引入 `React Hook Form` 管理表单状态、校验和提交。
    -   [ ] **(待办)** 将登录逻辑用 `React Query` 的 `useMutation` 封装成自定义 Hook。
    -   [ ] **(待办)** 将三种登录模式拆分成独立的子组件。
-   [ ] **(未开始)** **测试**: 为所有新的重构代码编写单元测试和集成测试，确保代码质量和功能正确性。
    -   [ ] **(待办)** 为后端 `UserServiceImpl` 中已重构的部分（如分页逻辑）编写单元测试。
    -   [ ] **(待办)** 为前端 `LoginScreen` 的重构（一旦开始）编写单元测试。

---

## 备注

-   在执行文件移动操作时，`desktop-commander` 的 `move_file` 和 `execute_command` (不带 `cwd` 时) 均表现出不稳定性。已切换到更可靠的 `read -> write -> delete` 原子操作流程，并强制使用带 `cwd` 的 `execute_command` 执行删除。后续文件操作应遵循此模式。