# 全局底部导航栏实现任务清单 (2025-06-11)

## 1. 设计与规划

-   [x] **任务 1.1:** 在 `小鹅养成APP_UIUX设计文档_v2.md` 中，补充“全局底部导航栏 (Global Tab Bar)”的设计规范，包括：
    -   明确5个导航按钮的图标 (Font Awesome) 和文案。
    -   定义按钮的默认状态和激活状态 (颜色、动效)。
    -   确定导航栏的背景、边框、阴影等视觉样式，确保符合“Gummy Glassmorphism”风格。

## 2. 样式实现

-   [x] **任务 2.1:** 在全局样式文件 `prototype_v2/assets/style.css` 中，编写并实现导航栏的 CSS 样式。

## 3. 组件集成

-   [x] **任务 3.1:** 将导航栏的 HTML 结构作为一个通用组件，添加到所有现存的 `prototype_v2/pages/*.html` 页面中。
    -   [x] `discover.html`
    -   [x] `goose-detail.html`
    -   [x] `login.html`
    -   [x] `my-geese.html`
    -   [x] `profile.html`
    -   [x] `purchase-flow.html`
    -   [x] `shop.html`
    -   [x] `social-feed.html`
-   [x] **任务 3.2:** 为每个页面的导航栏设置正确的“激活”状态。例如，在 `my-geese.html` 页面，"我的" 按钮应该是激活状态。

## 4. 文档闭环

-   [x] **任务 4.1:** 更新 `docs/UI原型迭代记录.md`，记录本次导航栏实现工作。