# 项目架构与结构分析报告

## 概述
本文档旨在记录和分析「小鹅养成」应用在开发过程中的重要架构决策、演进过程和当前结构。

---

## 2025-06-12: 用户服务 (`UserService`) 重构与数据持久层修复

### 1. 重构背景与目标

原始的 `UserServiceImpl` 是一个典型的“上帝服务”，承担了包括用户核心操作、用户统计、登录日志记录等多种职责，违反了单一职责原则（SRP），导致代码耦合度高、可测试性差、维护困难。

**本次重构核心目标：**
-   **拆分服务：** 将非核心职责从 `UserServiceImpl` 中剥离。
-   **提升内聚：** 创建 `UserStatsService` 和 `UserLoginLogService`，分别管理用户统计和登录日志。
-   **明确职责：** 使 `UserService` 专注于用户核心业务（注册、登录、信息管理）。
-   **修复架构缺陷：** 解决数据持久层的历史技术债务。

### 2. 核心架构决策：解决数据持久层冲突

在集成测试阶段，暴露了项目最严重的一个架构问题：数据持久层存在两种矛盾的实现方式。

-   **历史实现：** 一个自定义的 `UserRepository` 接口，试图模仿JPA的Repository模式。
-   **框架实现：** MyBatis-Plus 提供的 `BaseMapper` 接口。

`UserMapper` 同时继承了 `BaseMapper<User>` 和 `UserRepository`，这导致了：
-   **方法冲突：** 如 `save` (自定义) vs `insert` (MyBatis-Plus)。
-   **签名不匹配：** 如 `findById` (自定义) vs `selectById` (MyBatis-Plus)。
-   **依赖注入混乱：** Spring容器中存在多个 `User` 实体的DAO实现，引发 `UnsatisfiedDependencyException` 和 `BindingException`。

**最终解决方案：**
1.  **废除 `UserRepository`：** 彻底删除 `UserRepository.java` 接口及其所有引用。
2.  **统一为 `UserMapper`：** 将所有服务 (`UserServiceImpl`, `UserStatsServiceImpl`) 的数据访问依赖全部切换为注入 `UserMapper`。
3.  **遵循MyBatis-Plus标准：** `UserMapper` 只继承 `BaseMapper<User>`，成为一个纯粹的MyBatis-Plus Mapper，完全依靠其提供的方法或XML中定义的SQL进行数据操作。

### 3. 重构成果
-   成功将 `UserService` 拆分为三个职责明确的服务，提升了代码质量。
-   彻底根除了数据持久层的架构冲突，消除了相关的依赖注入和方法绑定错误。
-   为新拆分的服务编写了单元测试，保障了业务逻辑的正确性。
-   后端应用现已稳定运行，核心的注册/登录流程通过集成测试验证。